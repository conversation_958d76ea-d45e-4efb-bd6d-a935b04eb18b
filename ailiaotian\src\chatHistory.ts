/**
 * 聊天历史管理模块
 * 负责聊天记录的存储、检索和管理
 */

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  sessionId: string;
}

export interface ChatSession {
  id: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  messages: ChatMessage[];
}

export class ChatHistoryManager {
  private static instance: ChatHistoryManager;
  private readonly STORAGE_KEY = 'ailiaotian_chat_history';
  private readonly MAX_SESSIONS = 50; // 最大保存会话数
  private readonly MAX_MESSAGES_PER_SESSION = 100; // 每个会话最大消息数

  private constructor() {}

  public static getInstance(): ChatHistoryManager {
    if (!ChatHistoryManager.instance) {
      ChatHistoryManager.instance = new ChatHistoryManager();
    }
    return ChatHistoryManager.instance;
  }

  /**
   * 获取所有聊天会话
   */
  public getSessions(): ChatSession[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];
      
      const sessions: ChatSession[] = JSON.parse(stored);
      return sessions.sort((a, b) => b.updatedAt - a.updatedAt);
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      return [];
    }
  }

  /**
   * 获取指定会话
   */
  public getSession(sessionId: string): ChatSession | null {
    const sessions = this.getSessions();
    return sessions.find(s => s.id === sessionId) || null;
  }

  /**
   * 创建新的聊天会话
   */
  public createSession(title?: string): ChatSession {
    const session: ChatSession = {
      id: this.generateId(),
      title: title || '新对话',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      messages: []
    };

    const sessions = this.getSessions();
    sessions.unshift(session);

    // 限制会话数量
    if (sessions.length > this.MAX_SESSIONS) {
      sessions.splice(this.MAX_SESSIONS);
    }

    this.saveSessions(sessions);
    return session;
  }

  /**
   * 添加消息到会话
   */
  public addMessage(sessionId: string, role: 'user' | 'assistant', content: string): ChatMessage {
    const sessions = this.getSessions();
    const sessionIndex = sessions.findIndex(s => s.id === sessionId);
    
    if (sessionIndex === -1) {
      throw new Error(`会话 ${sessionId} 不存在`);
    }

    const message: ChatMessage = {
      id: this.generateId(),
      role,
      content,
      timestamp: Date.now(),
      sessionId
    };

    const session = sessions[sessionIndex];
    session.messages.push(message);
    session.updatedAt = Date.now();

    // 限制消息数量
    if (session.messages.length > this.MAX_MESSAGES_PER_SESSION) {
      session.messages.splice(0, session.messages.length - this.MAX_MESSAGES_PER_SESSION);
    }

    // 自动生成会话标题（基于第一条用户消息）
    if (session.title === '新对话' && role === 'user' && session.messages.length === 1) {
      session.title = this.generateSessionTitle(content);
    }

    this.saveSessions(sessions);
    return message;
  }

  /**
   * 更新会话标题
   */
  public updateSessionTitle(sessionId: string, title: string): void {
    const sessions = this.getSessions();
    const session = sessions.find(s => s.id === sessionId);
    
    if (session) {
      session.title = title;
      session.updatedAt = Date.now();
      this.saveSessions(sessions);
    }
  }

  /**
   * 删除会话
   */
  public deleteSession(sessionId: string): void {
    const sessions = this.getSessions();
    const filteredSessions = sessions.filter(s => s.id !== sessionId);
    this.saveSessions(filteredSessions);
  }

  /**
   * 清空所有聊天历史
   */
  public clearAllHistory(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * 获取会话的消息历史（用于API上下文）
   */
  public getSessionContext(sessionId: string, maxMessages: number = 10): ChatMessage[] {
    const session = this.getSession(sessionId);
    if (!session) return [];

    // 返回最近的消息，保持对话上下文
    return session.messages.slice(-maxMessages);
  }

  /**
   * 搜索聊天历史
   */
  public searchMessages(query: string): { session: ChatSession; message: ChatMessage }[] {
    const sessions = this.getSessions();
    const results: { session: ChatSession; message: ChatMessage }[] = [];

    sessions.forEach(session => {
      session.messages.forEach(message => {
        if (message.content.toLowerCase().includes(query.toLowerCase())) {
          results.push({ session, message });
        }
      });
    });

    return results.sort((a, b) => b.message.timestamp - a.message.timestamp);
  }

  /**
   * 导出聊天历史
   */
  public exportHistory(): string {
    const sessions = this.getSessions();
    return JSON.stringify(sessions, null, 2);
  }

  /**
   * 导入聊天历史
   */
  public importHistory(data: string): void {
    try {
      const sessions: ChatSession[] = JSON.parse(data);
      
      // 验证数据格式
      if (!Array.isArray(sessions)) {
        throw new Error('无效的数据格式');
      }

      // 合并现有历史
      const existingSessions = this.getSessions();
      const allSessions = [...sessions, ...existingSessions];
      
      // 去重并限制数量
      const uniqueSessions = allSessions
        .filter((session, index, arr) => 
          arr.findIndex(s => s.id === session.id) === index
        )
        .slice(0, this.MAX_SESSIONS);

      this.saveSessions(uniqueSessions);
    } catch (error) {
      console.error('导入聊天历史失败:', error);
      throw new Error('导入失败：数据格式错误');
    }
  }

  /**
   * 保存会话到本地存储
   */
  private saveSessions(sessions: ChatSession[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('保存聊天历史失败:', error);
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 根据消息内容生成会话标题
   */
  private generateSessionTitle(content: string): string {
    // 取前30个字符作为标题
    const title = content.trim().substring(0, 30);
    return title.length < content.trim().length ? title + '...' : title;
  }
}

/**
 * 获取聊天历史管理器实例
 */
export const getChatHistoryManager = (): ChatHistoryManager => {
  return ChatHistoryManager.getInstance();
};
