/**
 * 快捷键配置接口
 */
export interface KeybindingConfig {
  binding: string;      // Windows/Linux 快捷键
  mac: string;          // Mac 快捷键
  description: string;  // 快捷键描述
}

/**
 * 插件设置接口定义
 */
export interface PluginSettings {
  apiUrl: string;
  apiKey: string;
  modelName: string;
  temperature: number;
  maxTokens: number;
  enableHistory: boolean;
  customPrompts: Array<{name: string, prompt: string}>;
  // 快捷键设置
  keybindings: {
    openChat: KeybindingConfig;
    quickReply: KeybindingConfig;
    openChatWindow: KeybindingConfig;
  };
}

/**
 * 默认设置值
 */
export const DEFAULT_SETTINGS: PluginSettings = {
  apiUrl: "https://api.openai.com/v1/chat/completions",
  apiKey: "",
  modelName: "gpt-3.5-turbo",
  temperature: 0.7,
  maxTokens: 2000,
  enableHistory: false,
  customPrompts: [
    {
      name: "总结",
      prompt: "请总结以下内容的要点："
    },
    {
      name: "扩展",
      prompt: "请基于以下内容进行扩展和补充："
    }
  ],
  keybindings: {
    openChat: {
      binding: "ctrl+g",
      mac: "cmd+g",
      description: "打开AI聊天"
    },
    quickReply: {
      binding: "ctrl+shift+g",
      mac: "cmd+shift+g",
      description: "快速AI回复"
    },
    openChatWindow: {
      binding: "ctrl+alt+g",
      mac: "cmd+alt+g",
      description: "打开聊天窗口"
    }
  }
};

/**
 * 初始化插件设置
 * @returns 合并后的设置对象
 */
export async function initializeSettings(): Promise<PluginSettings> {
  // 获取当前设置，使用兼容的API
  const currentSettings = logseq.settings || {};
  const mergedSettings = Object.assign({}, DEFAULT_SETTINGS, currentSettings);
  
  // 确保customPrompts字段存在且为数组
  if (!Array.isArray(mergedSettings.customPrompts)) {
    mergedSettings.customPrompts = DEFAULT_SETTINGS.customPrompts;
  }
  
  // 确保新添加的字段有默认值
  if (mergedSettings.temperature === undefined) {
    mergedSettings.temperature = DEFAULT_SETTINGS.temperature;
  }

  if (mergedSettings.maxTokens === undefined) {
    mergedSettings.maxTokens = DEFAULT_SETTINGS.maxTokens;
  }

  // 确保快捷键设置存在
  if (!mergedSettings.keybindings) {
    mergedSettings.keybindings = DEFAULT_SETTINGS.keybindings;
  } else {
    // 确保所有快捷键配置都存在
    if (!mergedSettings.keybindings.openChat) {
      mergedSettings.keybindings.openChat = DEFAULT_SETTINGS.keybindings.openChat;
    }
    if (!mergedSettings.keybindings.quickReply) {
      mergedSettings.keybindings.quickReply = DEFAULT_SETTINGS.keybindings.quickReply;
    }
    if (!mergedSettings.keybindings.openChatWindow) {
      mergedSettings.keybindings.openChatWindow = DEFAULT_SETTINGS.keybindings.openChatWindow;
    }
  }
  
  // 保存合并后的设置
  await logseq.updateSettings(mergedSettings);
  
  return mergedSettings;
}

/**
 * 更新插件设置
 * @param settings 要更新的设置对象或部分设置
 */
export async function updateSettings(settings: Partial<PluginSettings>): Promise<void> {
  await logseq.updateSettings(settings);
}

/**
 * 获取当前插件设置
 * @returns 当前设置对象
 */
export async function getSettings(): Promise<PluginSettings> {
  // 获取当前设置，使用兼容的API
  const settings = logseq.settings || {};
  return Object.assign({}, DEFAULT_SETTINGS, settings) as PluginSettings;
}

/**
 * 验证快捷键格式
 * @param keybinding 快捷键字符串
 * @returns 验证结果对象
 */
export function validateKeybinding(keybinding: string): { isValid: boolean; error?: string } {
  if (!keybinding || typeof keybinding !== 'string') {
    return { isValid: false, error: '快捷键不能为空' };
  }

  const trimmed = keybinding.trim();
  if (!trimmed) {
    return { isValid: false, error: '快捷键不能为空' };
  }

  // 基本格式验证：允许的修饰键和字符
  const validModifiers = ['ctrl', 'cmd', 'alt', 'shift', 'meta'];
  const parts = trimmed.toLowerCase().split('+');

  if (parts.length === 0) {
    return { isValid: false, error: '快捷键格式无效' };
  }

  // 最后一个部分应该是按键
  const key = parts[parts.length - 1];
  if (!key || key.length === 0) {
    return { isValid: false, error: '缺少按键部分' };
  }

  // 检查修饰键是否有效
  const modifiers = parts.slice(0, -1);
  for (const modifier of modifiers) {
    if (!validModifiers.includes(modifier)) {
      return { isValid: false, error: `无效的修饰键: ${modifier}` };
    }
  }

  // 检查是否有重复的修饰键
  const uniqueModifiers = [...new Set(modifiers)];
  if (uniqueModifiers.length !== modifiers.length) {
    return { isValid: false, error: '不能有重复的修饰键' };
  }

  // 检查按键是否有效（基本字符、数字、功能键等）
  const validKeys = /^[a-z0-9]$|^f[1-9]$|^f1[0-2]$|^(space|enter|tab|escape|backspace|delete|home|end|pageup|pagedown|insert|up|down|left|right)$/;
  if (!validKeys.test(key)) {
    return { isValid: false, error: `无效的按键: ${key}` };
  }

  return { isValid: true };
}

/**
 * 格式化快捷键显示
 * @param keybinding 快捷键配置
 * @param isMac 是否为Mac系统
 * @returns 格式化后的快捷键字符串
 */
export function formatKeybindingDisplay(keybinding: KeybindingConfig, isMac: boolean = false): string {
  const key = isMac ? keybinding.mac : keybinding.binding;

  if (!key) {
    return '';
  }

  // 将快捷键转换为更友好的显示格式
  return key
    .split('+')
    .map(part => {
      const p = part.toLowerCase();
      switch (p) {
        case 'ctrl': return isMac ? '⌃' : 'Ctrl';
        case 'cmd': return '⌘';
        case 'alt': return isMac ? '⌥' : 'Alt';
        case 'shift': return isMac ? '⇧' : 'Shift';
        case 'meta': return isMac ? '⌘' : 'Win';
        default: return part.toUpperCase();
      }
    })
    .join(isMac ? '' : '+');
}

/**
 * 检查快捷键是否与系统快捷键冲突
 * @param keybinding 快捷键字符串
 * @param currentKeybindings 当前所有快捷键配置（用于检查内部冲突）
 * @returns 冲突检查结果
 */
export function checkKeybindingConflict(
  keybinding: string,
  currentKeybindings?: { [key: string]: KeybindingConfig }
): { hasConflict: boolean; type?: 'system' | 'internal'; message?: string } {
  const key = keybinding.toLowerCase().trim();

  if (!key) {
    return { hasConflict: false };
  }

  // 常见的系统快捷键
  const systemKeybindings = [
    'ctrl+c', 'cmd+c',     // 复制
    'ctrl+v', 'cmd+v',     // 粘贴
    'ctrl+x', 'cmd+x',     // 剪切
    'ctrl+z', 'cmd+z',     // 撤销
    'ctrl+y', 'cmd+y',     // 重做
    'ctrl+a', 'cmd+a',     // 全选
    'ctrl+s', 'cmd+s',     // 保存
    'ctrl+f', 'cmd+f',     // 查找
    'ctrl+n', 'cmd+n',     // 新建
    'ctrl+o', 'cmd+o',     // 打开
    'ctrl+p', 'cmd+p',     // 打印
    'ctrl+w', 'cmd+w',     // 关闭
    'ctrl+t', 'cmd+t',     // 新标签
    'ctrl+r', 'cmd+r',     // 刷新
    'ctrl+l', 'cmd+l',     // 地址栏
    'ctrl+d', 'cmd+d',     // 书签
    'ctrl+h', 'cmd+h',     // 历史
    'ctrl+j', 'cmd+j',     // 下载
    'ctrl+k', 'cmd+k',     // 搜索
    'ctrl+u', 'cmd+u',     // 查看源码
    'alt+f4', 'cmd+q',     // 退出
    'f5', 'cmd+r',         // 刷新
    'f11',                 // 全屏
    'f12',                 // 开发者工具
  ];

  // Logseq 特有的快捷键
  const logseqKeybindings = [
    'ctrl+shift+p', 'cmd+shift+p',  // 命令面板
    'ctrl+k', 'cmd+k',              // 快速搜索
    'ctrl+shift+k', 'cmd+shift+k',  // 块搜索
    'ctrl+/', 'cmd+/',              // 帮助
    'ctrl+shift+/', 'cmd+shift+/',  // 快捷键帮助
    'ctrl+e', 'cmd+e',              // 编辑模式
    'ctrl+shift+e', 'cmd+shift+e',  // 预览模式
  ];

  // 检查系统快捷键冲突
  if (systemKeybindings.includes(key)) {
    return {
      hasConflict: true,
      type: 'system',
      message: `可能与系统快捷键冲突: ${key}`
    };
  }

  // 检查Logseq快捷键冲突
  if (logseqKeybindings.includes(key)) {
    return {
      hasConflict: true,
      type: 'system',
      message: `可能与Logseq快捷键冲突: ${key}`
    };
  }

  // 检查内部快捷键冲突
  if (currentKeybindings) {
    for (const [configKey, config] of Object.entries(currentKeybindings)) {
      if (config.binding.toLowerCase() === key || config.mac.toLowerCase() === key) {
        return {
          hasConflict: true,
          type: 'internal',
          message: `与 "${config.description}" 快捷键冲突`
        };
      }
    }
  }

  return { hasConflict: false };
}

/**
 * 获取快捷键建议
 * @param description 功能描述
 * @returns 建议的快捷键配置
 */
export function suggestKeybinding(description: string): KeybindingConfig {
  const suggestions: { [key: string]: KeybindingConfig } = {
    '打开AI聊天': {
      binding: 'ctrl+g',
      mac: 'cmd+g',
      description: '打开AI聊天'
    },
    '快速AI回复': {
      binding: 'ctrl+shift+g',
      mac: 'cmd+shift+g',
      description: '快速AI回复'
    },
    'AI助手': {
      binding: 'ctrl+alt+a',
      mac: 'cmd+alt+a',
      description: 'AI助手'
    },
    '智能总结': {
      binding: 'ctrl+alt+s',
      mac: 'cmd+alt+s',
      description: '智能总结'
    }
  };

  return suggestions[description] || {
    binding: 'ctrl+alt+g',
    mac: 'cmd+alt+g',
    description: description
  };
}