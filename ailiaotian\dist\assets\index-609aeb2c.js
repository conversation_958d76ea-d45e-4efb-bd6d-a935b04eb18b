(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var Vi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Hh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ld={exports:{}},Ql={},Md={exports:{}},me={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mi=Symbol.for("react.element"),Wh=Symbol.for("react.portal"),Vh=Symbol.for("react.fragment"),qh=Symbol.for("react.strict_mode"),Kh=Symbol.for("react.profiler"),Qh=Symbol.for("react.provider"),Gh=Symbol.for("react.context"),Yh=Symbol.for("react.forward_ref"),Xh=Symbol.for("react.suspense"),Jh=Symbol.for("react.memo"),Zh=Symbol.for("react.lazy"),Cc=Symbol.iterator;function em(e){return e===null||typeof e!="object"?null:(e=Cc&&e[Cc]||e["@@iterator"],typeof e=="function"?e:null)}var Ad={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},jd=Object.assign,Rd={};function ho(e,t,n){this.props=e,this.context=t,this.refs=Rd,this.updater=n||Ad}ho.prototype.isReactComponent={};ho.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ho.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Dd(){}Dd.prototype=ho.prototype;function tu(e,t,n){this.props=e,this.context=t,this.refs=Rd,this.updater=n||Ad}var nu=tu.prototype=new Dd;nu.constructor=tu;jd(nu,ho.prototype);nu.isPureReactComponent=!0;var Sc=Array.isArray,zd=Object.prototype.hasOwnProperty,ru={current:null},Fd={key:!0,ref:!0,__self:!0,__source:!0};function Ud(e,t,n){var r,o={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)zd.call(t,r)&&!Fd.hasOwnProperty(r)&&(o[r]=t[r]);var u=arguments.length-2;if(u===1)o.children=n;else if(1<u){for(var d=Array(u),m=0;m<u;m++)d[m]=arguments[m+2];o.children=d}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)o[r]===void 0&&(o[r]=u[r]);return{$$typeof:mi,type:e,key:i,ref:a,props:o,_owner:ru.current}}function tm(e,t){return{$$typeof:mi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ou(e){return typeof e=="object"&&e!==null&&e.$$typeof===mi}function nm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var xc=/\/+/g;function js(e,t){return typeof e=="object"&&e!==null&&e.key!=null?nm(""+e.key):t.toString(36)}function cl(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case mi:case Wh:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+js(a,0):r,Sc(o)?(n="",e!=null&&(n=e.replace(xc,"$&/")+"/"),cl(o,t,n,"",function(m){return m})):o!=null&&(ou(o)&&(o=tm(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(xc,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",Sc(e))for(var u=0;u<e.length;u++){i=e[u];var d=r+js(i,u);a+=cl(i,t,n,d,o)}else if(d=em(e),typeof d=="function")for(e=d.call(e),u=0;!(i=e.next()).done;)i=i.value,d=r+js(i,u++),a+=cl(i,t,n,d,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function qi(e,t,n){if(e==null)return e;var r=[],o=0;return cl(e,r,"","",function(i){return t.call(n,i,o++)}),r}function rm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ut={current:null},dl={transition:null},om={ReactCurrentDispatcher:ut,ReactCurrentBatchConfig:dl,ReactCurrentOwner:ru};function $d(){throw Error("act(...) is not supported in production builds of React.")}me.Children={map:qi,forEach:function(e,t,n){qi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return qi(e,function(){t++}),t},toArray:function(e){return qi(e,function(t){return t})||[]},only:function(e){if(!ou(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};me.Component=ho;me.Fragment=Vh;me.Profiler=Kh;me.PureComponent=tu;me.StrictMode=qh;me.Suspense=Xh;me.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=om;me.act=$d;me.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=jd({},e.props),o=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=ru.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(d in t)zd.call(t,d)&&!Fd.hasOwnProperty(d)&&(r[d]=t[d]===void 0&&u!==void 0?u[d]:t[d])}var d=arguments.length-2;if(d===1)r.children=n;else if(1<d){u=Array(d);for(var m=0;m<d;m++)u[m]=arguments[m+2];r.children=u}return{$$typeof:mi,type:e.type,key:o,ref:i,props:r,_owner:a}};me.createContext=function(e){return e={$$typeof:Gh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Qh,_context:e},e.Consumer=e};me.createElement=Ud;me.createFactory=function(e){var t=Ud.bind(null,e);return t.type=e,t};me.createRef=function(){return{current:null}};me.forwardRef=function(e){return{$$typeof:Yh,render:e}};me.isValidElement=ou;me.lazy=function(e){return{$$typeof:Zh,_payload:{_status:-1,_result:e},_init:rm}};me.memo=function(e,t){return{$$typeof:Jh,type:e,compare:t===void 0?null:t}};me.startTransition=function(e){var t=dl.transition;dl.transition={};try{e()}finally{dl.transition=t}};me.unstable_act=$d;me.useCallback=function(e,t){return ut.current.useCallback(e,t)};me.useContext=function(e){return ut.current.useContext(e)};me.useDebugValue=function(){};me.useDeferredValue=function(e){return ut.current.useDeferredValue(e)};me.useEffect=function(e,t){return ut.current.useEffect(e,t)};me.useId=function(){return ut.current.useId()};me.useImperativeHandle=function(e,t,n){return ut.current.useImperativeHandle(e,t,n)};me.useInsertionEffect=function(e,t){return ut.current.useInsertionEffect(e,t)};me.useLayoutEffect=function(e,t){return ut.current.useLayoutEffect(e,t)};me.useMemo=function(e,t){return ut.current.useMemo(e,t)};me.useReducer=function(e,t,n){return ut.current.useReducer(e,t,n)};me.useRef=function(e){return ut.current.useRef(e)};me.useState=function(e){return ut.current.useState(e)};me.useSyncExternalStore=function(e,t,n){return ut.current.useSyncExternalStore(e,t,n)};me.useTransition=function(){return ut.current.useTransition()};me.version="18.3.1";Md.exports=me;var ne=Md.exports;const Bd=Hh(ne);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var im=ne,lm=Symbol.for("react.element"),sm=Symbol.for("react.fragment"),am=Object.prototype.hasOwnProperty,um=im.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,cm={key:!0,ref:!0,__self:!0,__source:!0};function Hd(e,t,n){var r,o={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)am.call(t,r)&&!cm.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:lm,type:e,key:i,ref:a,props:o,_owner:um.current}}Ql.Fragment=sm;Ql.jsx=Hd;Ql.jsxs=Hd;Ld.exports=Ql;var iu=Ld.exports;const dm=iu.Fragment,j=iu.jsx,Z=iu.jsxs;var Sl={exports:{}};/*! For license information please see lsplugin.user.js.LICENSE.txt */Sl.exports;(function(e,t){(function(n,r){e.exports=r()})(self,()=>(()=>{var n={227:(a,u,d)=>{var m=d(155);u.formatArgs=function(_){if(_[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+_[0]+(this.useColors?"%c ":" ")+"+"+a.exports.humanize(this.diff),!this.useColors)return;const E="color: "+this.color;_.splice(1,0,E,"color: inherit");let S=0,C=0;_[0].replace(/%[a-zA-Z%]/g,T=>{T!=="%%"&&(S++,T==="%c"&&(C=S))}),_.splice(C,0,E)},u.save=function(_){try{_?u.storage.setItem("debug",_):u.storage.removeItem("debug")}catch{}},u.load=function(){let _;try{_=u.storage.getItem("debug")}catch{}return!_&&m!==void 0&&"env"in m&&(_=m.env.DEBUG),_},u.useColors=function(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},u.storage=function(){try{return localStorage}catch{}}(),u.destroy=(()=>{let _=!1;return()=>{_||(_=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),u.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],u.log=console.debug||console.log||(()=>{}),a.exports=d(447)(u);const{formatters:w}=a.exports;w.j=function(_){try{return JSON.stringify(_)}catch(E){return"[UnexpectedJSONParseError]: "+E.message}}},447:(a,u,d)=>{a.exports=function(m){function w(S){let C,T,O,g=null;function p(...f){if(!p.enabled)return;const I=p,N=Number(new Date),L=N-(C||N);I.diff=L,I.prev=C,I.curr=N,C=N,f[0]=w.coerce(f[0]),typeof f[0]!="string"&&f.unshift("%O");let h=0;f[0]=f[0].replace(/%([a-zA-Z%])/g,(k,R)=>{if(k==="%%")return"%";h++;const M=w.formatters[R];if(typeof M=="function"){const G=f[h];k=M.call(I,G),f.splice(h,1),h--}return k}),w.formatArgs.call(I,f),(I.log||w.log).apply(I,f)}return p.namespace=S,p.useColors=w.useColors(),p.color=w.selectColor(S),p.extend=_,p.destroy=w.destroy,Object.defineProperty(p,"enabled",{enumerable:!0,configurable:!1,get:()=>g!==null?g:(T!==w.namespaces&&(T=w.namespaces,O=w.enabled(S)),O),set:f=>{g=f}}),typeof w.init=="function"&&w.init(p),p}function _(S,C){const T=w(this.namespace+(C===void 0?":":C)+S);return T.log=this.log,T}function E(S){return S.toString().substring(2,S.toString().length-2).replace(/\.\*\?$/,"*")}return w.debug=w,w.default=w,w.coerce=function(S){return S instanceof Error?S.stack||S.message:S},w.disable=function(){const S=[...w.names.map(E),...w.skips.map(E).map(C=>"-"+C)].join(",");return w.enable(""),S},w.enable=function(S){let C;w.save(S),w.namespaces=S,w.names=[],w.skips=[];const T=(typeof S=="string"?S:"").split(/[\s,]+/),O=T.length;for(C=0;C<O;C++)T[C]&&((S=T[C].replace(/\*/g,".*?"))[0]==="-"?w.skips.push(new RegExp("^"+S.slice(1)+"$")):w.names.push(new RegExp("^"+S+"$")))},w.enabled=function(S){if(S[S.length-1]==="*")return!0;let C,T;for(C=0,T=w.skips.length;C<T;C++)if(w.skips[C].test(S))return!1;for(C=0,T=w.names.length;C<T;C++)if(w.names[C].test(S))return!0;return!1},w.humanize=d(824),w.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(m).forEach(S=>{w[S]=m[S]}),w.names=[],w.skips=[],w.formatters={},w.selectColor=function(S){let C=0;for(let T=0;T<S.length;T++)C=(C<<5)-C+S.charCodeAt(T),C|=0;return w.colors[Math.abs(C)%w.colors.length]},w.enable(w.load()),w}},996:a=>{var u=function(O){return function(g){return!!g&&typeof g=="object"}(O)&&!function(g){var p=Object.prototype.toString.call(g);return p==="[object RegExp]"||p==="[object Date]"||function(f){return f.$$typeof===d}(g)}(O)},d=typeof Symbol=="function"&&Symbol.for?Symbol.for("react.element"):60103;function m(O,g){return g.clone!==!1&&g.isMergeableObject(O)?C((p=O,Array.isArray(p)?[]:{}),O,g):O;var p}function w(O,g,p){return O.concat(g).map(function(f){return m(f,p)})}function _(O){return Object.keys(O).concat(function(g){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(g).filter(function(p){return Object.propertyIsEnumerable.call(g,p)}):[]}(O))}function E(O,g){try{return g in O}catch{return!1}}function S(O,g,p){var f={};return p.isMergeableObject(O)&&_(O).forEach(function(I){f[I]=m(O[I],p)}),_(g).forEach(function(I){(function(N,L){return E(N,L)&&!(Object.hasOwnProperty.call(N,L)&&Object.propertyIsEnumerable.call(N,L))})(O,I)||(E(O,I)&&p.isMergeableObject(g[I])?f[I]=function(N,L){if(!L.customMerge)return C;var h=L.customMerge(N);return typeof h=="function"?h:C}(I,p)(O[I],g[I],p):f[I]=m(g[I],p))}),f}function C(O,g,p){(p=p||{}).arrayMerge=p.arrayMerge||w,p.isMergeableObject=p.isMergeableObject||u,p.cloneUnlessOtherwiseSpecified=m;var f=Array.isArray(g);return f===Array.isArray(O)?f?p.arrayMerge(O,g,p):S(O,g,p):m(g,p)}C.all=function(O,g){if(!Array.isArray(O))throw new Error("first argument should be an array");return O.reduce(function(p,f){return C(p,f,g)},{})};var T=C;a.exports=T},856:function(a){a.exports=function(){function u(W){return u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(K){return typeof K}:function(K){return K&&typeof Symbol=="function"&&K.constructor===Symbol&&K!==Symbol.prototype?"symbol":typeof K},u(W)}function d(W,K){return d=Object.setPrototypeOf||function(le,he){return le.__proto__=he,le},d(W,K)}function m(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function w(W,K,le){return w=m()?Reflect.construct:function(he,De,jt){var Yt=[null];Yt.push.apply(Yt,De);var Sn=new(Function.bind.apply(he,Yt));return jt&&d(Sn,jt.prototype),Sn},w.apply(null,arguments)}function _(W){return E(W)||S(W)||C(W)||O()}function E(W){if(Array.isArray(W))return T(W)}function S(W){if(typeof Symbol<"u"&&W[Symbol.iterator]!=null||W["@@iterator"]!=null)return Array.from(W)}function C(W,K){if(W){if(typeof W=="string")return T(W,K);var le=Object.prototype.toString.call(W).slice(8,-1);return le==="Object"&&W.constructor&&(le=W.constructor.name),le==="Map"||le==="Set"?Array.from(W):le==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(le)?T(W,K):void 0}}function T(W,K){(K==null||K>W.length)&&(K=W.length);for(var le=0,he=new Array(K);le<K;le++)he[le]=W[le];return he}function O(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var g=Object.hasOwnProperty,p=Object.setPrototypeOf,f=Object.isFrozen,I=Object.getPrototypeOf,N=Object.getOwnPropertyDescriptor,L=Object.freeze,h=Object.seal,k=Object.create,R=typeof Reflect<"u"&&Reflect,M=R.apply,G=R.construct;M||(M=function(W,K,le){return W.apply(K,le)}),L||(L=function(W){return W}),h||(h=function(W){return W}),G||(G=function(W,K){return w(W,_(K))});var ce=re(Array.prototype.forEach),ue=re(Array.prototype.pop),pe=re(Array.prototype.push),oe=re(String.prototype.toLowerCase),_e=re(String.prototype.match),we=re(String.prototype.replace),z=re(String.prototype.indexOf),v=re(String.prototype.trim),b=re(RegExp.prototype.test),U=fe(TypeError);function re(W){return function(K){for(var le=arguments.length,he=new Array(le>1?le-1:0),De=1;De<le;De++)he[De-1]=arguments[De];return M(W,K,he)}}function fe(W){return function(){for(var K=arguments.length,le=new Array(K),he=0;he<K;he++)le[he]=arguments[he];return G(W,le)}}function B(W,K){p&&p(W,null);for(var le=K.length;le--;){var he=K[le];if(typeof he=="string"){var De=oe(he);De!==he&&(f(K)||(K[le]=De),he=De)}W[he]=!0}return W}function Q(W){var K,le=k(null);for(K in W)M(g,W,[K])&&(le[K]=W[K]);return le}function se(W,K){for(;W!==null;){var le=N(W,K);if(le){if(le.get)return re(le.get);if(typeof le.value=="function")return re(le.value)}W=I(W)}function he(De){return console.warn("fallback value for",De),null}return he}var Re=L(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ve=L(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),lt=L(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Y=L(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),J=L(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),ve=L(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Ee=L(["#text"]),qe=L(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),dt=L(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),sn=L(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),wt=L(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ft=h(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Zn=h(/<%[\w\W]*|[\w\W]*%>/gm),cs=h(/^data-[\-\w.\u00B7-\uFFFF]/),At=h(/^aria-[\-\w]+$/),Gt=h(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ci=h(/^(?:\w+script|data):/i),ds=h(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),fs=h(/^html$/i),er=function(){return typeof window>"u"?null:window},ps=function(W,K){if(u(W)!=="object"||typeof W.createPolicy!="function")return null;var le=null,he="data-tt-policy-suffix";K.currentScript&&K.currentScript.hasAttribute(he)&&(le=K.currentScript.getAttribute(he));var De="dompurify"+(le?"#"+le:"");try{return W.createPolicy(De,{createHTML:function(jt){return jt}})}catch{return console.warn("TrustedTypes policy "+De+" could not be created."),null}};function Si(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:er(),K=function(x){return Si(x)};if(K.version="2.3.8",K.removed=[],!W||!W.document||W.document.nodeType!==9)return K.isSupported=!1,K;var le=W.document,he=W.document,De=W.DocumentFragment,jt=W.HTMLTemplateElement,Yt=W.Node,Sn=W.Element,yo=W.NodeFilter,xi=W.NamedNodeMap,an=xi===void 0?W.NamedNodeMap||W.MozNamedAttrMap:xi,hs=W.HTMLFormElement,ms=W.DOMParser,gs=W.trustedTypes,br=Sn.prototype,ys=se(br,"cloneNode"),vs=se(br,"nextSibling"),ws=se(br,"childNodes"),vo=se(br,"parentNode");if(typeof jt=="function"){var Rt=he.createElement("template");Rt.content&&Rt.content.ownerDocument&&(he=Rt.content.ownerDocument)}var Dt=ps(gs,le),_i=Dt?Dt.createHTML(""):"",Pr=he,wo=Pr.implementation,xn=Pr.createNodeIterator,Ei=Pr.createDocumentFragment,Ti=Pr.getElementsByTagName,ks=le.importNode,Ni={};try{Ni=Q(he).documentMode?he.documentMode:{}}catch{}var kt={};K.isSupported=typeof vo=="function"&&wo&&wo.createHTMLDocument!==void 0&&Ni!==9;var tr,Xt,Ir=ft,Or=Zn,ko=cs,Cs=At,bi=Ci,Lr=ds,ke=Gt,Ue=null,Pi=B({},[].concat(_(Re),_(Ve),_(lt),_(J),_(Ee))),$e=null,_n=B({},[].concat(_(qe),_(dt),_(sn),_(wt))),Ie=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),En=null,Mr=null,Co=!0,So=!0,Ii=!1,Tn=!1,un=!1,xo=!1,_o=!1,Nn=!1,Ar=!1,bn=!1,Oi=!0,Eo=!0,Pn=!1,zt={},In=null,Li=B({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Mi=null,Ai=B({},["audio","video","img","source","image","track"]),To=null,cn=B({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),On="http://www.w3.org/1998/Math/MathML",No="http://www.w3.org/2000/svg",Jt="http://www.w3.org/1999/xhtml",jr=Jt,ji=!1,nr=["application/xhtml+xml","text/html"],rr="text/html",Ln=null,Ss=he.createElement("form"),Ri=function(x){return x instanceof RegExp||x instanceof Function},bo=function(x){Ln&&Ln===x||(x&&u(x)==="object"||(x={}),x=Q(x),Ue="ALLOWED_TAGS"in x?B({},x.ALLOWED_TAGS):Pi,$e="ALLOWED_ATTR"in x?B({},x.ALLOWED_ATTR):_n,To="ADD_URI_SAFE_ATTR"in x?B(Q(cn),x.ADD_URI_SAFE_ATTR):cn,Mi="ADD_DATA_URI_TAGS"in x?B(Q(Ai),x.ADD_DATA_URI_TAGS):Ai,In="FORBID_CONTENTS"in x?B({},x.FORBID_CONTENTS):Li,En="FORBID_TAGS"in x?B({},x.FORBID_TAGS):{},Mr="FORBID_ATTR"in x?B({},x.FORBID_ATTR):{},zt="USE_PROFILES"in x&&x.USE_PROFILES,Co=x.ALLOW_ARIA_ATTR!==!1,So=x.ALLOW_DATA_ATTR!==!1,Ii=x.ALLOW_UNKNOWN_PROTOCOLS||!1,Tn=x.SAFE_FOR_TEMPLATES||!1,un=x.WHOLE_DOCUMENT||!1,Nn=x.RETURN_DOM||!1,Ar=x.RETURN_DOM_FRAGMENT||!1,bn=x.RETURN_TRUSTED_TYPE||!1,_o=x.FORCE_BODY||!1,Oi=x.SANITIZE_DOM!==!1,Eo=x.KEEP_CONTENT!==!1,Pn=x.IN_PLACE||!1,ke=x.ALLOWED_URI_REGEXP||ke,jr=x.NAMESPACE||Jt,x.CUSTOM_ELEMENT_HANDLING&&Ri(x.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ie.tagNameCheck=x.CUSTOM_ELEMENT_HANDLING.tagNameCheck),x.CUSTOM_ELEMENT_HANDLING&&Ri(x.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ie.attributeNameCheck=x.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),x.CUSTOM_ELEMENT_HANDLING&&typeof x.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(Ie.allowCustomizedBuiltInElements=x.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),tr=tr=nr.indexOf(x.PARSER_MEDIA_TYPE)===-1?rr:x.PARSER_MEDIA_TYPE,Xt=tr==="application/xhtml+xml"?function(q){return q}:oe,Tn&&(So=!1),Ar&&(Nn=!0),zt&&(Ue=B({},_(Ee)),$e=[],zt.html===!0&&(B(Ue,Re),B($e,qe)),zt.svg===!0&&(B(Ue,Ve),B($e,dt),B($e,wt)),zt.svgFilters===!0&&(B(Ue,lt),B($e,dt),B($e,wt)),zt.mathMl===!0&&(B(Ue,J),B($e,sn),B($e,wt))),x.ADD_TAGS&&(Ue===Pi&&(Ue=Q(Ue)),B(Ue,x.ADD_TAGS)),x.ADD_ATTR&&($e===_n&&($e=Q($e)),B($e,x.ADD_ATTR)),x.ADD_URI_SAFE_ATTR&&B(To,x.ADD_URI_SAFE_ATTR),x.FORBID_CONTENTS&&(In===Li&&(In=Q(In)),B(In,x.FORBID_CONTENTS)),Eo&&(Ue["#text"]=!0),un&&B(Ue,["html","head","body"]),Ue.table&&(B(Ue,["tbody"]),delete En.tbody),L&&L(x),Ln=x)},Di=B({},["mi","mo","mn","ms","mtext"]),zi=B({},["foreignobject","desc","title","annotation-xml"]),xs=B({},["title","style","font","a","script"]),Ft=B({},Ve);B(Ft,lt),B(Ft,Y);var Rr=B({},J);B(Rr,ve);var _s=function(x){var q=vo(x);q&&q.tagName||(q={namespaceURI:Jt,tagName:"template"});var H=oe(x.tagName),Ce=oe(q.tagName);return x.namespaceURI===No?q.namespaceURI===Jt?H==="svg":q.namespaceURI===On?H==="svg"&&(Ce==="annotation-xml"||Di[Ce]):!!Ft[H]:x.namespaceURI===On?q.namespaceURI===Jt?H==="math":q.namespaceURI===No?H==="math"&&zi[Ce]:!!Rr[H]:x.namespaceURI===Jt&&!(q.namespaceURI===No&&!zi[Ce])&&!(q.namespaceURI===On&&!Di[Ce])&&!Rr[H]&&(xs[H]||!Ft[H])},Ut=function(x){pe(K.removed,{element:x});try{x.parentNode.removeChild(x)}catch{try{x.outerHTML=_i}catch{x.remove()}}},or=function(x,q){try{pe(K.removed,{attribute:q.getAttributeNode(x),from:q})}catch{pe(K.removed,{attribute:null,from:q})}if(q.removeAttribute(x),x==="is"&&!$e[x])if(Nn||Ar)try{Ut(q)}catch{}else try{q.setAttribute(x,"")}catch{}},Fi=function(x){var q,H;if(_o)x="<remove></remove>"+x;else{var Ce=_e(x,/^[\r\n\t ]+/);H=Ce&&Ce[0]}tr==="application/xhtml+xml"&&(x='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+x+"</body></html>");var Le=Dt?Dt.createHTML(x):x;if(jr===Jt)try{q=new ms().parseFromString(Le,tr)}catch{}if(!q||!q.documentElement){q=wo.createDocument(jr,"template",null);try{q.documentElement.innerHTML=ji?"":Le}catch{}}var Ge=q.body||q.documentElement;return x&&H&&Ge.insertBefore(he.createTextNode(H),Ge.childNodes[0]||null),jr===Jt?Ti.call(q,un?"html":"body")[0]:un?q.documentElement:Ge},Ui=function(x){return xn.call(x.ownerDocument||x,x,yo.SHOW_ELEMENT|yo.SHOW_COMMENT|yo.SHOW_TEXT,null,!1)},Es=function(x){return x instanceof hs&&(typeof x.nodeName!="string"||typeof x.textContent!="string"||typeof x.removeChild!="function"||!(x.attributes instanceof an)||typeof x.removeAttribute!="function"||typeof x.setAttribute!="function"||typeof x.namespaceURI!="string"||typeof x.insertBefore!="function")},ir=function(x){return u(Yt)==="object"?x instanceof Yt:x&&u(x)==="object"&&typeof x.nodeType=="number"&&typeof x.nodeName=="string"},tt=function(x,q,H){kt[x]&&ce(kt[x],function(Ce){Ce.call(K,q,H,Ln)})},lr=function(x){var q;if(tt("beforeSanitizeElements",x,null),Es(x)||b(/[\u0080-\uFFFF]/,x.nodeName))return Ut(x),!0;var H=Xt(x.nodeName);if(tt("uponSanitizeElement",x,{tagName:H,allowedTags:Ue}),x.hasChildNodes()&&!ir(x.firstElementChild)&&(!ir(x.content)||!ir(x.content.firstElementChild))&&b(/<[/\w]/g,x.innerHTML)&&b(/<[/\w]/g,x.textContent)||H==="select"&&b(/<template/i,x.innerHTML))return Ut(x),!0;if(!Ue[H]||En[H]){if(!En[H]&&dn(H)&&(Ie.tagNameCheck instanceof RegExp&&b(Ie.tagNameCheck,H)||Ie.tagNameCheck instanceof Function&&Ie.tagNameCheck(H)))return!1;if(Eo&&!In[H]){var Ce=vo(x)||x.parentNode,Le=ws(x)||x.childNodes;if(Le&&Ce)for(var Ge=Le.length-1;Ge>=0;--Ge)Ce.insertBefore(ys(Le[Ge],!0),vs(x))}return Ut(x),!0}return x instanceof Sn&&!_s(x)?(Ut(x),!0):H!=="noscript"&&H!=="noembed"||!b(/<\/no(script|embed)/i,x.innerHTML)?(Tn&&x.nodeType===3&&(q=x.textContent,q=we(q,Ir," "),q=we(q,Or," "),x.textContent!==q&&(pe(K.removed,{element:x.cloneNode()}),x.textContent=q)),tt("afterSanitizeElements",x,null),!1):(Ut(x),!0)},$i=function(x,q,H){if(Oi&&(q==="id"||q==="name")&&(H in he||H in Ss))return!1;if(!(So&&!Mr[q]&&b(ko,q))){if(!(Co&&b(Cs,q))){if(!$e[q]||Mr[q]){if(!(dn(x)&&(Ie.tagNameCheck instanceof RegExp&&b(Ie.tagNameCheck,x)||Ie.tagNameCheck instanceof Function&&Ie.tagNameCheck(x))&&(Ie.attributeNameCheck instanceof RegExp&&b(Ie.attributeNameCheck,q)||Ie.attributeNameCheck instanceof Function&&Ie.attributeNameCheck(q))||q==="is"&&Ie.allowCustomizedBuiltInElements&&(Ie.tagNameCheck instanceof RegExp&&b(Ie.tagNameCheck,H)||Ie.tagNameCheck instanceof Function&&Ie.tagNameCheck(H))))return!1}else if(!To[q]){if(!b(ke,we(H,Lr,""))){if((q!=="src"&&q!=="xlink:href"&&q!=="href"||x==="script"||z(H,"data:")!==0||!Mi[x])&&!(Ii&&!b(bi,we(H,Lr,"")))){if(H)return!1}}}}}return!0},dn=function(x){return x.indexOf("-")>0},sr=function(x){var q,H,Ce,Le;tt("beforeSanitizeAttributes",x,null);var Ge=x.attributes;if(Ge){var Be={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:$e};for(Le=Ge.length;Le--;){var ar=q=Ge[Le],Zt=ar.name,pt=ar.namespaceURI;if(H=Zt==="value"?q.value:v(q.value),Ce=Xt(Zt),Be.attrName=Ce,Be.attrValue=H,Be.keepAttr=!0,Be.forceKeepAttr=void 0,tt("uponSanitizeAttribute",x,Be),H=Be.attrValue,!Be.forceKeepAttr&&(or(Zt,x),Be.keepAttr))if(b(/\/>/i,H))or(Zt,x);else{Tn&&(H=we(H,Ir," "),H=we(H,Or," "));var Po=Xt(x.nodeName);if($i(Po,Ce,H))try{pt?x.setAttributeNS(pt,Zt,H):x.setAttribute(Zt,H),ue(K.removed)}catch{}}}tt("afterSanitizeAttributes",x,null)}},Ts=function x(q){var H,Ce=Ui(q);for(tt("beforeSanitizeShadowDOM",q,null);H=Ce.nextNode();)tt("uponSanitizeShadowNode",H,null),lr(H)||(H.content instanceof De&&x(H.content),sr(H));tt("afterSanitizeShadowDOM",q,null)};return K.sanitize=function(x,q){var H,Ce,Le,Ge,Be;if((ji=!x)&&(x="<!-->"),typeof x!="string"&&!ir(x)){if(typeof x.toString!="function")throw U("toString is not a function");if(typeof(x=x.toString())!="string")throw U("dirty is not a string, aborting")}if(!K.isSupported){if(u(W.toStaticHTML)==="object"||typeof W.toStaticHTML=="function"){if(typeof x=="string")return W.toStaticHTML(x);if(ir(x))return W.toStaticHTML(x.outerHTML)}return x}if(xo||bo(q),K.removed=[],typeof x=="string"&&(Pn=!1),Pn){if(x.nodeName){var ar=Xt(x.nodeName);if(!Ue[ar]||En[ar])throw U("root node is forbidden and cannot be sanitized in-place")}}else if(x instanceof Yt)(Ce=(H=Fi("<!---->")).ownerDocument.importNode(x,!0)).nodeType===1&&Ce.nodeName==="BODY"||Ce.nodeName==="HTML"?H=Ce:H.appendChild(Ce);else{if(!Nn&&!Tn&&!un&&x.indexOf("<")===-1)return Dt&&bn?Dt.createHTML(x):x;if(!(H=Fi(x)))return Nn?null:bn?_i:""}H&&_o&&Ut(H.firstChild);for(var Zt=Ui(Pn?x:H);Le=Zt.nextNode();)Le.nodeType===3&&Le===Ge||lr(Le)||(Le.content instanceof De&&Ts(Le.content),sr(Le),Ge=Le);if(Ge=null,Pn)return x;if(Nn){if(Ar)for(Be=Ei.call(H.ownerDocument);H.firstChild;)Be.appendChild(H.firstChild);else Be=H;return $e.shadowroot&&(Be=ks.call(le,Be,!0)),Be}var pt=un?H.outerHTML:H.innerHTML;return un&&Ue["!doctype"]&&H.ownerDocument&&H.ownerDocument.doctype&&H.ownerDocument.doctype.name&&b(fs,H.ownerDocument.doctype.name)&&(pt="<!DOCTYPE "+H.ownerDocument.doctype.name+`>
`+pt),Tn&&(pt=we(pt,Ir," "),pt=we(pt,Or," ")),Dt&&bn?Dt.createHTML(pt):pt},K.setConfig=function(x){bo(x),xo=!0},K.clearConfig=function(){Ln=null,xo=!1},K.isValidAttribute=function(x,q,H){Ln||bo({});var Ce=Xt(x),Le=Xt(q);return $i(Ce,Le,H)},K.addHook=function(x,q){typeof q=="function"&&(kt[x]=kt[x]||[],pe(kt[x],q))},K.removeHook=function(x){if(kt[x])return ue(kt[x])},K.removeHooks=function(x){kt[x]&&(kt[x]=[])},K.removeAllHooks=function(){kt={}},K}return Si()}()},729:a=>{var u=Object.prototype.hasOwnProperty,d="~";function m(){}function w(C,T,O){this.fn=C,this.context=T,this.once=O||!1}function _(C,T,O,g,p){if(typeof O!="function")throw new TypeError("The listener must be a function");var f=new w(O,g||C,p),I=d?d+T:T;return C._events[I]?C._events[I].fn?C._events[I]=[C._events[I],f]:C._events[I].push(f):(C._events[I]=f,C._eventsCount++),C}function E(C,T){--C._eventsCount==0?C._events=new m:delete C._events[T]}function S(){this._events=new m,this._eventsCount=0}Object.create&&(m.prototype=Object.create(null),new m().__proto__||(d=!1)),S.prototype.eventNames=function(){var C,T,O=[];if(this._eventsCount===0)return O;for(T in C=this._events)u.call(C,T)&&O.push(d?T.slice(1):T);return Object.getOwnPropertySymbols?O.concat(Object.getOwnPropertySymbols(C)):O},S.prototype.listeners=function(C){var T=d?d+C:C,O=this._events[T];if(!O)return[];if(O.fn)return[O.fn];for(var g=0,p=O.length,f=new Array(p);g<p;g++)f[g]=O[g].fn;return f},S.prototype.listenerCount=function(C){var T=d?d+C:C,O=this._events[T];return O?O.fn?1:O.length:0},S.prototype.emit=function(C,T,O,g,p,f){var I=d?d+C:C;if(!this._events[I])return!1;var N,L,h=this._events[I],k=arguments.length;if(h.fn){switch(h.once&&this.removeListener(C,h.fn,void 0,!0),k){case 1:return h.fn.call(h.context),!0;case 2:return h.fn.call(h.context,T),!0;case 3:return h.fn.call(h.context,T,O),!0;case 4:return h.fn.call(h.context,T,O,g),!0;case 5:return h.fn.call(h.context,T,O,g,p),!0;case 6:return h.fn.call(h.context,T,O,g,p,f),!0}for(L=1,N=new Array(k-1);L<k;L++)N[L-1]=arguments[L];h.fn.apply(h.context,N)}else{var R,M=h.length;for(L=0;L<M;L++)switch(h[L].once&&this.removeListener(C,h[L].fn,void 0,!0),k){case 1:h[L].fn.call(h[L].context);break;case 2:h[L].fn.call(h[L].context,T);break;case 3:h[L].fn.call(h[L].context,T,O);break;case 4:h[L].fn.call(h[L].context,T,O,g);break;default:if(!N)for(R=1,N=new Array(k-1);R<k;R++)N[R-1]=arguments[R];h[L].fn.apply(h[L].context,N)}}return!0},S.prototype.on=function(C,T,O){return _(this,C,T,O,!1)},S.prototype.once=function(C,T,O){return _(this,C,T,O,!0)},S.prototype.removeListener=function(C,T,O,g){var p=d?d+C:C;if(!this._events[p])return this;if(!T)return E(this,p),this;var f=this._events[p];if(f.fn)f.fn!==T||g&&!f.once||O&&f.context!==O||E(this,p);else{for(var I=0,N=[],L=f.length;I<L;I++)(f[I].fn!==T||g&&!f[I].once||O&&f[I].context!==O)&&N.push(f[I]);N.length?this._events[p]=N.length===1?N[0]:N:E(this,p)}return this},S.prototype.removeAllListeners=function(C){var T;return C?(T=d?d+C:C,this._events[T]&&E(this,T)):(this._events=new m,this._eventsCount=0),this},S.prototype.off=S.prototype.removeListener,S.prototype.addListener=S.prototype.on,S.prefixed=d,S.EventEmitter=S,a.exports=S},717:a=>{typeof Object.create=="function"?a.exports=function(u,d){u.super_=d,u.prototype=Object.create(d.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}})}:a.exports=function(u,d){u.super_=d;var m=function(){};m.prototype=d.prototype,u.prototype=new m,u.prototype.constructor=u}},824:a=>{var u=1e3,d=60*u,m=60*d,w=24*m,_=7*w,E=365.25*w;function S(C,T,O,g){var p=T>=1.5*O;return Math.round(C/O)+" "+g+(p?"s":"")}a.exports=function(C,T){T=T||{};var O=typeof C;if(O==="string"&&C.length>0)return function(g){if(!((g=String(g)).length>100)){var p=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(g);if(p){var f=parseFloat(p[1]);switch((p[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return f*E;case"weeks":case"week":case"w":return f*_;case"days":case"day":case"d":return f*w;case"hours":case"hour":case"hrs":case"hr":case"h":return f*m;case"minutes":case"minute":case"mins":case"min":case"m":return f*d;case"seconds":case"second":case"secs":case"sec":case"s":return f*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return f;default:return}}}}(C);if(O==="number"&&isFinite(C))return T.long?function(g){var p=Math.abs(g);return p>=w?S(g,p,w,"day"):p>=m?S(g,p,m,"hour"):p>=d?S(g,p,d,"minute"):p>=u?S(g,p,u,"second"):g+" ms"}(C):function(g){var p=Math.abs(g);return p>=w?Math.round(g/w)+"d":p>=m?Math.round(g/m)+"h":p>=d?Math.round(g/d)+"m":p>=u?Math.round(g/u)+"s":g+"ms"}(C);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(C))}},520:(a,u,d)=>{var m=d(155),w=m.platform==="win32",_=d(539);function E(h,k){for(var R=[],M=0;M<h.length;M++){var G=h[M];G&&G!=="."&&(G===".."?R.length&&R[R.length-1]!==".."?R.pop():k&&R.push(".."):R.push(G))}return R}function S(h){for(var k=h.length-1,R=0;R<=k&&!h[R];R++);for(var M=k;M>=0&&!h[M];M--);return R===0&&M===k?h:R>M?[]:h.slice(R,M+1)}var C=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,T=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,O={};function g(h){var k=C.exec(h),R=(k[1]||"")+(k[2]||""),M=k[3]||"",G=T.exec(M);return[R,G[1],G[2],G[3]]}function p(h){var k=C.exec(h),R=k[1]||"",M=!!R&&R[1]!==":";return{device:R,isUnc:M,isAbsolute:M||!!k[2],tail:k[3]}}function f(h){return"\\\\"+h.replace(/^[\\\/]+/,"").replace(/[\\\/]+/g,"\\")}O.resolve=function(){for(var h="",k="",R=!1,M=arguments.length-1;M>=-1;M--){var G;if(M>=0?G=arguments[M]:h?(G=m.env["="+h])&&G.substr(0,3).toLowerCase()===h.toLowerCase()+"\\"||(G=h+"\\"):G=m.cwd(),!_.isString(G))throw new TypeError("Arguments to path.resolve must be strings");if(G){var ce=p(G),ue=ce.device,pe=ce.isUnc,oe=ce.isAbsolute,_e=ce.tail;if((!ue||!h||ue.toLowerCase()===h.toLowerCase())&&(h||(h=ue),R||(k=_e+"\\"+k,R=oe),h&&R))break}}return pe&&(h=f(h)),h+(R?"\\":"")+(k=E(k.split(/[\\\/]+/),!R).join("\\"))||"."},O.normalize=function(h){var k=p(h),R=k.device,M=k.isUnc,G=k.isAbsolute,ce=k.tail,ue=/[\\\/]$/.test(ce);return(ce=E(ce.split(/[\\\/]+/),!G).join("\\"))||G||(ce="."),ce&&ue&&(ce+="\\"),M&&(R=f(R)),R+(G?"\\":"")+ce},O.isAbsolute=function(h){return p(h).isAbsolute},O.join=function(){for(var h=[],k=0;k<arguments.length;k++){var R=arguments[k];if(!_.isString(R))throw new TypeError("Arguments to path.join must be strings");R&&h.push(R)}var M=h.join("\\");return/^[\\\/]{2}[^\\\/]/.test(h[0])||(M=M.replace(/^[\\\/]{2,}/,"\\")),O.normalize(M)},O.relative=function(h,k){h=O.resolve(h),k=O.resolve(k);for(var R=h.toLowerCase(),M=k.toLowerCase(),G=S(k.split("\\")),ce=S(R.split("\\")),ue=S(M.split("\\")),pe=Math.min(ce.length,ue.length),oe=pe,_e=0;_e<pe;_e++)if(ce[_e]!==ue[_e]){oe=_e;break}if(oe==0)return k;var we=[];for(_e=oe;_e<ce.length;_e++)we.push("..");return(we=we.concat(G.slice(oe))).join("\\")},O._makeLong=function(h){if(!_.isString(h))return h;if(!h)return"";var k=O.resolve(h);return/^[a-zA-Z]\:\\/.test(k)?"\\\\?\\"+k:/^\\\\[^?.]/.test(k)?"\\\\?\\UNC\\"+k.substring(2):h},O.dirname=function(h){var k=g(h),R=k[0],M=k[1];return R||M?(M&&(M=M.substr(0,M.length-1)),R+M):"."},O.basename=function(h,k){var R=g(h)[2];return k&&R.substr(-1*k.length)===k&&(R=R.substr(0,R.length-k.length)),R},O.extname=function(h){return g(h)[3]},O.format=function(h){if(!_.isObject(h))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof h);var k=h.root||"";if(!_.isString(k))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof h.root);var R=h.dir,M=h.base||"";return R?R[R.length-1]===O.sep?R+M:R+O.sep+M:M},O.parse=function(h){if(!_.isString(h))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof h);var k=g(h);if(!k||k.length!==4)throw new TypeError("Invalid path '"+h+"'");return{root:k[0],dir:k[0]+k[1].slice(0,-1),base:k[2],ext:k[3],name:k[2].slice(0,k[2].length-k[3].length)}},O.sep="\\",O.delimiter=";";var I=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,N={};function L(h){return I.exec(h).slice(1)}N.resolve=function(){for(var h="",k=!1,R=arguments.length-1;R>=-1&&!k;R--){var M=R>=0?arguments[R]:m.cwd();if(!_.isString(M))throw new TypeError("Arguments to path.resolve must be strings");M&&(h=M+"/"+h,k=M[0]==="/")}return(k?"/":"")+(h=E(h.split("/"),!k).join("/"))||"."},N.normalize=function(h){var k=N.isAbsolute(h),R=h&&h[h.length-1]==="/";return(h=E(h.split("/"),!k).join("/"))||k||(h="."),h&&R&&(h+="/"),(k?"/":"")+h},N.isAbsolute=function(h){return h.charAt(0)==="/"},N.join=function(){for(var h="",k=0;k<arguments.length;k++){var R=arguments[k];if(!_.isString(R))throw new TypeError("Arguments to path.join must be strings");R&&(h+=h?"/"+R:R)}return N.normalize(h)},N.relative=function(h,k){h=N.resolve(h).substr(1),k=N.resolve(k).substr(1);for(var R=S(h.split("/")),M=S(k.split("/")),G=Math.min(R.length,M.length),ce=G,ue=0;ue<G;ue++)if(R[ue]!==M[ue]){ce=ue;break}var pe=[];for(ue=ce;ue<R.length;ue++)pe.push("..");return(pe=pe.concat(M.slice(ce))).join("/")},N._makeLong=function(h){return h},N.dirname=function(h){var k=L(h),R=k[0],M=k[1];return R||M?(M&&(M=M.substr(0,M.length-1)),R+M):"."},N.basename=function(h,k){var R=L(h)[2];return k&&R.substr(-1*k.length)===k&&(R=R.substr(0,R.length-k.length)),R},N.extname=function(h){return L(h)[3]},N.format=function(h){if(!_.isObject(h))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof h);var k=h.root||"";if(!_.isString(k))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof h.root);return(h.dir?h.dir+N.sep:"")+(h.base||"")},N.parse=function(h){if(!_.isString(h))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof h);var k=L(h);if(!k||k.length!==4)throw new TypeError("Invalid path '"+h+"'");return k[1]=k[1]||"",k[2]=k[2]||"",k[3]=k[3]||"",{root:k[0],dir:k[0]+k[1].slice(0,-1),base:k[2],ext:k[3],name:k[2].slice(0,k[2].length-k[3].length)}},N.sep="/",N.delimiter=":",a.exports=w?O:N,a.exports.posix=N,a.exports.win32=O},155:a=>{var u,d,m=a.exports={};function w(){throw new Error("setTimeout has not been defined")}function _(){throw new Error("clearTimeout has not been defined")}function E(N){if(u===setTimeout)return setTimeout(N,0);if((u===w||!u)&&setTimeout)return u=setTimeout,setTimeout(N,0);try{return u(N,0)}catch{try{return u.call(null,N,0)}catch{return u.call(this,N,0)}}}(function(){try{u=typeof setTimeout=="function"?setTimeout:w}catch{u=w}try{d=typeof clearTimeout=="function"?clearTimeout:_}catch{d=_}})();var S,C=[],T=!1,O=-1;function g(){T&&S&&(T=!1,S.length?C=S.concat(C):O=-1,C.length&&p())}function p(){if(!T){var N=E(g);T=!0;for(var L=C.length;L;){for(S=C,C=[];++O<L;)S&&S[O].run();O=-1,L=C.length}S=null,T=!1,function(h){if(d===clearTimeout)return clearTimeout(h);if((d===_||!d)&&clearTimeout)return d=clearTimeout,clearTimeout(h);try{d(h)}catch{try{return d.call(null,h)}catch{return d.call(this,h)}}}(N)}}function f(N,L){this.fun=N,this.array=L}function I(){}m.nextTick=function(N){var L=new Array(arguments.length-1);if(arguments.length>1)for(var h=1;h<arguments.length;h++)L[h-1]=arguments[h];C.push(new f(N,L)),C.length!==1||T||E(p)},f.prototype.run=function(){this.fun.apply(null,this.array)},m.title="browser",m.browser=!0,m.env={},m.argv=[],m.version="",m.versions={},m.on=I,m.addListener=I,m.once=I,m.off=I,m.removeListener=I,m.removeAllListeners=I,m.emit=I,m.prependListener=I,m.prependOnceListener=I,m.listeners=function(N){return[]},m.binding=function(N){throw new Error("process.binding is not supported")},m.cwd=function(){return"/"},m.chdir=function(N){throw new Error("process.chdir is not supported")},m.umask=function(){return 0}},384:a=>{a.exports=function(u){return u&&typeof u=="object"&&typeof u.copy=="function"&&typeof u.fill=="function"&&typeof u.readUInt8=="function"}},539:(a,u,d)=>{var m=d(155),w=/%[sdj%]/g;u.format=function(v){if(!h(v)){for(var b=[],U=0;U<arguments.length;U++)b.push(S(arguments[U]));return b.join(" ")}U=1;for(var re=arguments,fe=re.length,B=String(v).replace(w,function(se){if(se==="%%")return"%";if(U>=fe)return se;switch(se){case"%s":return String(re[U++]);case"%d":return Number(re[U++]);case"%j":try{return JSON.stringify(re[U++])}catch{return"[Circular]"}default:return se}}),Q=re[U];U<fe;Q=re[++U])N(Q)||!M(Q)?B+=" "+Q:B+=" "+S(Q);return B},u.deprecate=function(v,b){if(k(d.g.process))return function(){return u.deprecate(v,b).apply(this,arguments)};if(m.noDeprecation===!0)return v;var U=!1;return function(){if(!U){if(m.throwDeprecation)throw new Error(b);m.traceDeprecation?console.trace(b):console.error(b),U=!0}return v.apply(this,arguments)}};var _,E={};function S(v,b){var U={seen:[],stylize:T};return arguments.length>=3&&(U.depth=arguments[2]),arguments.length>=4&&(U.colors=arguments[3]),I(b)?U.showHidden=b:b&&u._extend(U,b),k(U.showHidden)&&(U.showHidden=!1),k(U.depth)&&(U.depth=2),k(U.colors)&&(U.colors=!1),k(U.customInspect)&&(U.customInspect=!0),U.colors&&(U.stylize=C),O(U,v,U.depth)}function C(v,b){var U=S.styles[b];return U?"\x1B["+S.colors[U][0]+"m"+v+"\x1B["+S.colors[U][1]+"m":v}function T(v,b){return v}function O(v,b,U){if(v.customInspect&&b&&ue(b.inspect)&&b.inspect!==u.inspect&&(!b.constructor||b.constructor.prototype!==b)){var re=b.inspect(U,v);return h(re)||(re=O(v,re,U)),re}var fe=function(J,ve){if(k(ve))return J.stylize("undefined","undefined");if(h(ve)){var Ee="'"+JSON.stringify(ve).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return J.stylize(Ee,"string")}if(L(ve))return J.stylize(""+ve,"number");if(I(ve))return J.stylize(""+ve,"boolean");if(N(ve))return J.stylize("null","null")}(v,b);if(fe)return fe;var B=Object.keys(b),Q=function(J){var ve={};return J.forEach(function(Ee,qe){ve[Ee]=!0}),ve}(B);if(v.showHidden&&(B=Object.getOwnPropertyNames(b)),ce(b)&&(B.indexOf("message")>=0||B.indexOf("description")>=0))return g(b);if(B.length===0){if(ue(b)){var se=b.name?": "+b.name:"";return v.stylize("[Function"+se+"]","special")}if(R(b))return v.stylize(RegExp.prototype.toString.call(b),"regexp");if(G(b))return v.stylize(Date.prototype.toString.call(b),"date");if(ce(b))return g(b)}var Re,Ve="",lt=!1,Y=["{","}"];return f(b)&&(lt=!0,Y=["[","]"]),ue(b)&&(Ve=" [Function"+(b.name?": "+b.name:"")+"]"),R(b)&&(Ve=" "+RegExp.prototype.toString.call(b)),G(b)&&(Ve=" "+Date.prototype.toUTCString.call(b)),ce(b)&&(Ve=" "+g(b)),B.length!==0||lt&&b.length!=0?U<0?R(b)?v.stylize(RegExp.prototype.toString.call(b),"regexp"):v.stylize("[Object]","special"):(v.seen.push(b),Re=lt?function(J,ve,Ee,qe,dt){for(var sn=[],wt=0,ft=ve.length;wt<ft;++wt)z(ve,String(wt))?sn.push(p(J,ve,Ee,qe,String(wt),!0)):sn.push("");return dt.forEach(function(Zn){Zn.match(/^\d+$/)||sn.push(p(J,ve,Ee,qe,Zn,!0))}),sn}(v,b,U,Q,B):B.map(function(J){return p(v,b,U,Q,J,lt)}),v.seen.pop(),function(J,ve,Ee){return J.reduce(function(qe,dt){return dt.indexOf(`
`)>=0,qe+dt.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?Ee[0]+(ve===""?"":ve+`
 `)+" "+J.join(`,
  `)+" "+Ee[1]:Ee[0]+ve+" "+J.join(", ")+" "+Ee[1]}(Re,Ve,Y)):Y[0]+Ve+Y[1]}function g(v){return"["+Error.prototype.toString.call(v)+"]"}function p(v,b,U,re,fe,B){var Q,se,Re;if((Re=Object.getOwnPropertyDescriptor(b,fe)||{value:b[fe]}).get?se=Re.set?v.stylize("[Getter/Setter]","special"):v.stylize("[Getter]","special"):Re.set&&(se=v.stylize("[Setter]","special")),z(re,fe)||(Q="["+fe+"]"),se||(v.seen.indexOf(Re.value)<0?(se=N(U)?O(v,Re.value,null):O(v,Re.value,U-1)).indexOf(`
`)>-1&&(se=B?se.split(`
`).map(function(Ve){return"  "+Ve}).join(`
`).substr(2):`
`+se.split(`
`).map(function(Ve){return"   "+Ve}).join(`
`)):se=v.stylize("[Circular]","special")),k(Q)){if(B&&fe.match(/^\d+$/))return se;(Q=JSON.stringify(""+fe)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(Q=Q.substr(1,Q.length-2),Q=v.stylize(Q,"name")):(Q=Q.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),Q=v.stylize(Q,"string"))}return Q+": "+se}function f(v){return Array.isArray(v)}function I(v){return typeof v=="boolean"}function N(v){return v===null}function L(v){return typeof v=="number"}function h(v){return typeof v=="string"}function k(v){return v===void 0}function R(v){return M(v)&&pe(v)==="[object RegExp]"}function M(v){return typeof v=="object"&&v!==null}function G(v){return M(v)&&pe(v)==="[object Date]"}function ce(v){return M(v)&&(pe(v)==="[object Error]"||v instanceof Error)}function ue(v){return typeof v=="function"}function pe(v){return Object.prototype.toString.call(v)}function oe(v){return v<10?"0"+v.toString(10):v.toString(10)}u.debuglog=function(v){if(k(_)&&(_=m.env.NODE_DEBUG||""),v=v.toUpperCase(),!E[v])if(new RegExp("\\b"+v+"\\b","i").test(_)){var b=m.pid;E[v]=function(){var U=u.format.apply(u,arguments);console.error("%s %d: %s",v,b,U)}}else E[v]=function(){};return E[v]},u.inspect=S,S.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},S.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},u.isArray=f,u.isBoolean=I,u.isNull=N,u.isNullOrUndefined=function(v){return v==null},u.isNumber=L,u.isString=h,u.isSymbol=function(v){return typeof v=="symbol"},u.isUndefined=k,u.isRegExp=R,u.isObject=M,u.isDate=G,u.isError=ce,u.isFunction=ue,u.isPrimitive=function(v){return v===null||typeof v=="boolean"||typeof v=="number"||typeof v=="string"||typeof v=="symbol"||v===void 0},u.isBuffer=d(384);var _e=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function we(){var v=new Date,b=[oe(v.getHours()),oe(v.getMinutes()),oe(v.getSeconds())].join(":");return[v.getDate(),_e[v.getMonth()],b].join(" ")}function z(v,b){return Object.prototype.hasOwnProperty.call(v,b)}u.log=function(){console.log("%s - %s",we(),u.format.apply(u,arguments))},u.inherits=d(717),u._extend=function(v,b){if(!b||!M(b))return v;for(var U=Object.keys(b),re=U.length;re--;)v[U[re]]=b[U[re]];return v}}},r={};function o(a){var u=r[a];if(u!==void 0)return u.exports;var d=r[a]={exports:{}};return n[a].call(d.exports,d,d.exports,o),d.exports}o.n=a=>{var u=a&&a.__esModule?()=>a.default:()=>a;return o.d(u,{a:u}),u},o.d=(a,u)=>{for(var d in u)o.o(u,d)&&!o.o(a,d)&&Object.defineProperty(a,d,{enumerable:!0,get:u[d]})},o.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}(),o.o=(a,u)=>Object.prototype.hasOwnProperty.call(a,u),o.r=a=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})};var i={};return(()=>{o.r(i),o.d(i,{LSPluginUser:()=>As,setupPluginUserInstance:()=>kc});var a=o(520),u=(o(856),o(996)),d=o.n(u),m=function(){return m=Object.assign||function(s){for(var l,c=1,y=arguments.length;c<y;c++)for(var P in l=arguments[c])Object.prototype.hasOwnProperty.call(l,P)&&(s[P]=l[P]);return s},m.apply(this,arguments)};function w(s){return s.toLowerCase()}var _=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],E=/[^A-Z0-9]+/gi;function S(s,l,c){return l instanceof RegExp?s.replace(l,c):l.reduce(function(y,P){return y.replace(P,c)},s)}function C(s,l){return l===void 0&&(l={}),function(c,y){y===void 0&&(y={});for(var P=y.splitRegexp,A=P===void 0?_:P,D=y.stripRegexp,F=D===void 0?E:D,V=y.transform,te=V===void 0?w:V,ie=y.delimiter,ee=ie===void 0?" ":ie,ae=S(S(c,A,"$1\0$2"),F,"\0"),ge=0,de=ae.length;ae.charAt(ge)==="\0";)ge++;for(;ae.charAt(de-1)==="\0";)de--;return ae.slice(ge,de).split("\0").map(te).join(ee)}(s,m({delimiter:"."},l))}var T=o(729),O=o.n(T);function g(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}const p=navigator.platform.toLowerCase()==="win32"?a.win32:a.posix,f=function(s,l){return l===void 0&&(l={}),C(s,m({delimiter:"_"},l))};class I extends O(){constructor(l,c){super(),g(this,"_tag",void 0),g(this,"_opts",void 0),g(this,"_logs",[]),this._tag=l,this._opts=c}write(l,c,y){var P;c!=null&&c.length&&c[c.length-1]===!0&&(y=!0,c.pop());const A=c.reduce((F,V)=>(V&&V instanceof Error?F+=`${V.message} ${V.stack}`:F+=V.toString(),F),`[${this._tag}][${new Date().toLocaleTimeString()}] `);var D;this._logs.push([l,A]),(y||(P=this._opts)!==null&&P!==void 0&&P.console)&&((D=console)===null||D===void 0||D[l==="ERROR"?"error":"debug"](`${l}: ${A}`)),this.emit("change")}clear(){this._logs=[],this.emit("change")}info(...l){this.write("INFO",l)}error(...l){this.write("ERROR",l)}warn(...l){this.write("WARN",l)}setTag(l){this._tag=l}toJSON(){return this._logs}}function N(s,...l){try{const c=new URL(s);if(!c.origin)throw new Error(null);const y=p.join(s.substr(c.origin.length),...l);return c.origin+y}catch{return p.join(s,...l)}}function L(s,l){let c,y,P=!1;const A=F=>V=>{s&&clearTimeout(s),F(V),P=!0},D=new Promise((F,V)=>{c=A(F),y=A(V),s&&(s=setTimeout(()=>y(new Error(`[deferred timeout] ${l}`)),s))});return{created:Date.now(),setTag:F=>l=F,resolve:c,reject:y,promise:D,get settled(){return P}}}const h=new Map;window.__injectedUIEffects=h;var k=o(227),R=o.n(k);function M(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}const G="application/x-postmate-v1+json";let ce=0;const ue={handshake:1,"handshake-reply":1,call:1,emit:1,reply:1,request:1},pe=(s,l)=>(typeof l!="string"||s.origin===l)&&!!s.data&&(typeof s.data!="object"||"postmate"in s.data)&&s.data.type===G&&!!ue[s.data.postmate];class oe{constructor(l){M(this,"parent",void 0),M(this,"frame",void 0),M(this,"child",void 0),M(this,"events",{}),M(this,"childOrigin",void 0),M(this,"listener",void 0),this.parent=l.parent,this.frame=l.frame,this.child=l.child,this.childOrigin=l.childOrigin,this.listener=c=>{if(!pe(c,this.childOrigin))return!1;const{data:y,name:P}=((c||{}).data||{}).value||{};c.data.postmate==="emit"&&P in this.events&&this.events[P].forEach(A=>{A.call(this,y)})},this.parent.addEventListener("message",this.listener,!1)}get(l,...c){return new Promise((y,P)=>{const A=++ce,D=F=>{F.data.uid===A&&F.data.postmate==="reply"&&(this.parent.removeEventListener("message",D,!1),F.data.error?P(F.data.error):y(F.data.value))};this.parent.addEventListener("message",D,!1),this.child.postMessage({postmate:"request",type:G,property:l,args:c,uid:A},this.childOrigin)})}call(l,c){this.child.postMessage({postmate:"call",type:G,property:l,data:c},this.childOrigin)}on(l,c){this.events[l]||(this.events[l]=[]),this.events[l].push(c)}destroy(){window.removeEventListener("message",this.listener,!1),this.frame.parentNode.removeChild(this.frame)}}class _e{constructor(l){M(this,"model",void 0),M(this,"parent",void 0),M(this,"parentOrigin",void 0),M(this,"child",void 0),this.model=l.model,this.parent=l.parent,this.parentOrigin=l.parentOrigin,this.child=l.child,this.child.addEventListener("message",c=>{if(!pe(c,this.parentOrigin))return;const{property:y,uid:P,data:A,args:D}=c.data;c.data.postmate!=="call"?((F,V,te)=>{const ie=typeof F[V]=="function"?F[V].apply(null,te):F[V];return Promise.resolve(ie)})(this.model,y,D).then(F=>{c.source.postMessage({property:y,postmate:"reply",type:G,uid:P,value:F},c.origin)}).catch(F=>{c.source.postMessage({property:y,postmate:"reply",type:G,uid:P,error:F},c.origin)}):y in this.model&&typeof this.model[y]=="function"&&this.model[y](A)})}emit(l,c){this.parent.postMessage({postmate:"emit",type:G,value:{name:l,data:c}},this.parentOrigin)}}class we{constructor(l){M(this,"container",void 0),M(this,"parent",void 0),M(this,"frame",void 0),M(this,"child",void 0),M(this,"childOrigin",void 0),M(this,"url",void 0),M(this,"model",void 0),this.container=l.container,this.url=l.url,this.parent=window,this.frame=document.createElement("iframe"),l.id&&(this.frame.id=l.id),l.name&&(this.frame.name=l.name),this.frame.classList.add.apply(this.frame.classList,l.classListArray||[]),this.container.appendChild(this.frame),this.child=this.frame.contentWindow,this.model=l.model||{}}sendHandshake(l){const c=(A=>{const D=document.createElement("a");D.href=A;const F=D.protocol.length>4?D.protocol:window.location.protocol,V=D.host.length?D.port==="80"||D.port==="443"?D.hostname:D.host:window.location.host;return D.origin||`${F}//${V}`})(l=l||this.url);let y,P=0;return new Promise((A,D)=>{const F=te=>!!pe(te,c)&&(te.data.postmate==="handshake-reply"?(clearInterval(y),this.parent.removeEventListener("message",F,!1),this.childOrigin=te.origin,A(new oe(this))):D("Failed handshake"));this.parent.addEventListener("message",F,!1);const V=()=>{P++,this.child.postMessage({postmate:"handshake",type:G,model:this.model},c),P===5&&clearInterval(y)};this.frame.addEventListener("load",()=>{V(),y=setInterval(V,500)}),this.frame.src=l})}destroy(){this.frame.parentNode.removeChild(this.frame)}}M(we,"debug",!1),M(we,"Model",void 0);class z{constructor(l){M(this,"child",void 0),M(this,"model",void 0),M(this,"parent",void 0),M(this,"parentOrigin",void 0),this.child=window,this.model=l,this.parent=this.child.parent}sendHandshakeReply(){return new Promise((l,c)=>{const y=P=>{if(P.data.postmate){if(P.data.postmate==="handshake"){this.child.removeEventListener("message",y,!1),P.source.postMessage({postmate:"handshake-reply",type:G},P.origin),this.parentOrigin=P.origin;const A=P.data.model;return A&&Object.keys(A).forEach(D=>{this.model[D]=A[D]}),l(new _e(this))}return c("Handshake Reply Failed")}};this.child.addEventListener("message",y,!1)})}}function v(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}const{importHTML:b,createSandboxContainer:U}=window.QSandbox||{};function re(s,l){return s.startsWith("http")?fetch(s,l):(s=s.replace("file://",""),new Promise(async(c,y)=>{try{const P=await window.apis.doAction(["readFile",s]);c({text:()=>P})}catch(P){console.error(P),y(P)}}))}class fe extends O(){constructor(l){super(),v(this,"_pluginLocal",void 0),v(this,"_frame",void 0),v(this,"_root",void 0),v(this,"_loaded",!1),v(this,"_unmountFns",[]),this._pluginLocal=l,l._dispose(()=>{this._unmount()})}async load(){const{name:l,entry:c}=this._pluginLocal.options;if(this.loaded||!c)return;const{template:y,execScripts:P}=await b(c,{fetch:re});this._mount(y,document.body);const A=U(l,{elementGetter:()=>{var F;return(F=this._root)===null||F===void 0?void 0:F.firstChild}}).instance.proxy;A.__shadow_mode__=!0,A.LSPluginLocal=this._pluginLocal,A.LSPluginShadow=this,A.LSPluginUser=A.logseq=new As(this._pluginLocal.toJSON(),this._pluginLocal.caller);const D=await P(A,!0);this._unmountFns.push(D.unmount),this._loaded=!0}_mount(l,c){const y=this._frame=document.createElement("div");y.classList.add("lsp-shadow-sandbox"),y.id=this._pluginLocal.id,this._root=y.attachShadow({mode:"open"}),this._root.innerHTML=`<div>${l}</div>`,c.appendChild(y),this.emit("mounted")}_unmount(){for(const l of this._unmountFns)l&&l.call(null)}destroy(){var l,c;(l=this.frame)===null||l===void 0||(c=l.parentNode)===null||c===void 0||c.removeChild(this.frame)}get loaded(){return this._loaded}get document(){var l;return(l=this._root)===null||l===void 0?void 0:l.firstChild}get frame(){return this._frame}}function B(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}const Q=R()("LSPlugin:caller"),se="#await#response#",Re="#lspmsg#",Ve="#lspmsg#error#",lt=s=>`#lspmsg#${s}`;class Y extends O(){constructor(l){super(),B(this,"_pluginLocal",void 0),B(this,"_connected",!1),B(this,"_parent",void 0),B(this,"_child",void 0),B(this,"_shadow",void 0),B(this,"_status",void 0),B(this,"_userModel",{}),B(this,"_call",void 0),B(this,"_callUserModel",void 0),B(this,"_debugTag",""),this._pluginLocal=l,l&&(this._debugTag=l.debugTag)}async connectToChild(){if(this._connected)return;const{shadow:l}=this._pluginLocal;l?await this._setupShadowSandbox():await this._setupIframeSandbox()}async connectToParent(l={}){if(this._connected)return;const c=this,y=this._pluginLocal!=null;let P=0;const A=new Map,D=L(6e4),F=this._extendUserModel({"#lspmsg#ready#":async ie=>{F[lt(ie?.pid)]=({type:ee,payload:ae})=>{Q(`[host (_call) -> *user] ${this._debugTag}`,ee,ae),c.emit(ee,ae)},await D.resolve()},"#lspmsg#beforeunload#":async ie=>{const ee=L(1e4);c.emit("beforeunload",Object.assign({actor:ee},ie)),await ee.promise},"#lspmsg#settings#":async({type:ie,payload:ee})=>{c.emit("settings:changed",ee)},[Re]:async({ns:ie,type:ee,payload:ae})=>{Q(`[host (async) -> *user] ${this._debugTag} ns=${ie} type=${ee}`,ae),ie&&ie.startsWith("hook")?c.emit(`${ie}:${ee}`,ae):c.emit(ee,ae)},"#lspmsg#reply#":({_sync:ie,result:ee})=>{if(Q(`[sync host -> *user] #${ie}`,ee),A.has(ie)){const ae=A.get(ie);ae&&(ee!=null&&ee.hasOwnProperty(Ve)?ae.reject(ee[Ve]):ae.resolve(ee),A.delete(ie))}},...l});var V;if(y)return await D.promise,JSON.parse(JSON.stringify((V=this._pluginLocal)===null||V===void 0?void 0:V.toJSON()));const te=new z(F).sendHandshakeReply();return this._status="pending",await te.then(ie=>{this._child=ie,this._connected=!0,this._call=async(ee,ae={},ge)=>{if(ge){const de=++P;A.set(de,ge),ae._sync=de,ge.setTag(`async call #${de}`),Q(`async call #${de}`)}return ie.emit(lt(F.baseInfo.id),{type:ee,payload:ae}),ge?.promise},this._callUserModel=async(ee,ae)=>{try{F[ee](ae)}catch{Q(`[model method] #${ee} not existed`)}},setInterval(()=>{if(A.size>100)for(const[ee,ae]of A)ae.settled&&A.delete(ee)},18e5)}).finally(()=>{this._status=void 0}),await D.promise,F.baseInfo}async call(l,c={}){var y;return(y=this._call)===null||y===void 0?void 0:y.call(this,l,c)}async callAsync(l,c={}){var y;const P=L(1e4);return(y=this._call)===null||y===void 0?void 0:y.call(this,l,c,P)}async callUserModel(l,...c){var y;return(y=this._callUserModel)===null||y===void 0?void 0:y.apply(this,[l,...c])}async callUserModelAsync(l,...c){var y;return l=`${se}${l}`,(y=this._callUserModel)===null||y===void 0?void 0:y.apply(this,[l,...c])}async _setupIframeSandbox(){const l=this._pluginLocal,c=l.id,y=`${c}_lsp_main`,P=new URL(l.options.entry);P.searchParams.set("__v__",l.options.version);const A=document.querySelector(`#${y}`);A&&A.parentElement.removeChild(A);const D=document.createElement("div");D.classList.add("lsp-iframe-sandbox-container"),D.id=y,D.dataset.pid=c;try{var F;const ee=(F=await this._pluginLocal._loadLayoutsData())===null||F===void 0?void 0:F.$$0;if(ee){D.dataset.inited_layout="true";let{width:ae,height:ge,left:de,top:Se,vw:Te,vh:He}=ee;de=Math.max(de,0),de=typeof Te=="number"?`${Math.min(100*de/Te,99)}%`:`${de}px`,Se=Math.max(Se,45),Se=typeof He=="number"?`${Math.min(100*Se/He,99)}%`:`${Se}px`,Object.assign(D.style,{width:ae+"px",height:ge+"px",left:de,top:Se})}}catch(ee){console.error("[Restore Layout Error]",ee)}document.body.appendChild(D);const V=new we({id:c+"_iframe",container:D,url:P.href,classListArray:["lsp-iframe-sandbox"],model:{baseInfo:JSON.parse(JSON.stringify(l.toJSON()))}});let te,ie=V.sendHandshake();return this._status="pending",new Promise((ee,ae)=>{te=setTimeout(()=>{ae(new Error("handshake Timeout")),V.destroy()},4e3),ie.then(ge=>{this._parent=ge,this._connected=!0,this.emit("connected"),ge.on(lt(l.id),({type:de,payload:Se})=>{var Te,He;Q("[user -> *host] ",de,Se),(Te=this._pluginLocal)===null||Te===void 0||Te.emit(de,Se||{}),(He=this._pluginLocal)===null||He===void 0||He.caller.emit(de,Se||{})}),this._call=async(...de)=>{await ge.call(lt(l.id),{type:de[0],payload:Object.assign(de[1]||{},{$$pid:l.id})})},this._callUserModel=async(de,...Se)=>{if(de.startsWith(se))return await ge.get(de.replace(se,""),...Se);ge.call(de,Se?.[0])},ee(null)}).catch(ge=>{ae(ge)}).finally(()=>{clearTimeout(te)})}).catch(ee=>{throw Q("[iframe sandbox] error",ee),ee}).finally(()=>{this._status=void 0})}async _setupShadowSandbox(){const l=this._pluginLocal,c=this._shadow=new fe(l);try{this._status="pending",await c.load(),this._connected=!0,this.emit("connected"),this._call=async(y,P={},A)=>{var D;return A&&(P.actor=A),(D=this._pluginLocal)===null||D===void 0||D.emit(y,Object.assign(P,{$$pid:l.id})),A?.promise},this._callUserModel=async(...y)=>{var P;let A=y[0];(P=A)!==null&&P!==void 0&&P.startsWith(se)&&(A=A.replace(se,""));const D=y[1]||{},F=this._userModel[A];typeof F=="function"&&await F.call(null,D)}}catch(y){throw Q("[shadow sandbox] error",y),y}finally{this._status=void 0}}_extendUserModel(l){return Object.assign(this._userModel,l)}_getSandboxIframeContainer(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxShadowContainer(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxIframeRoot(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame}_getSandboxShadowRoot(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame}set debugTag(l){this._debugTag=l}async destroy(){var l;let c=null;this._parent&&(c=this._getSandboxIframeContainer(),await this._parent.destroy()),this._shadow&&(c=this._getSandboxShadowContainer(),this._shadow.destroy()),(l=c)===null||l===void 0||l.parentNode.removeChild(c)}}function J(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}class ve{constructor(l,c){J(this,"ctx",void 0),J(this,"opts",void 0),this.ctx=l,this.opts=c}get ctxId(){return this.ctx.baseInfo.id}setItem(l,c){var y;return this.ctx.caller.callAsync("api:call",{method:"write-plugin-storage-file",args:[this.ctxId,l,c,(y=this.opts)===null||y===void 0?void 0:y.assets]})}getItem(l){var c;return this.ctx.caller.callAsync("api:call",{method:"read-plugin-storage-file",args:[this.ctxId,l,(c=this.opts)===null||c===void 0?void 0:c.assets]})}removeItem(l){var c;return this.ctx.caller.call("api:call",{method:"unlink-plugin-storage-file",args:[this.ctxId,l,(c=this.opts)===null||c===void 0?void 0:c.assets]})}allKeys(){var l;return this.ctx.caller.callAsync("api:call",{method:"list-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}clear(){var l;return this.ctx.caller.call("api:call",{method:"clear-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}hasItem(l){var c;return this.ctx.caller.callAsync("api:call",{method:"exist-plugin-storage-file",args:[this.ctxId,l,(c=this.opts)===null||c===void 0?void 0:c.assets]})}}class Ee{constructor(l){var c,y,P;P=void 0,(y="ctx")in(c=this)?Object.defineProperty(c,y,{value:P,enumerable:!0,configurable:!0,writable:!0}):c[y]=P,this.ctx=l}get React(){return this.ensureHostScope().React}get ReactDOM(){return this.ensureHostScope().ReactDOM}get pluginLocal(){return this.ensureHostScope().LSPluginCore.ensurePlugin(this.ctx.baseInfo.id)}invokeExperMethod(l,...c){var y,P;const A=this.ensureHostScope();return l=(y=f(l))===null||y===void 0?void 0:y.toLowerCase(),(P=A.logseq.api["exper_"+l])===null||P===void 0?void 0:P.apply(A,c)}async loadScripts(...l){(l=l.map(c=>c!=null&&c.startsWith("http")?c:this.ctx.resolveResourceFullUrl(c))).unshift(this.ctx.baseInfo.id),await this.invokeExperMethod("loadScripts",...l)}registerFencedCodeRenderer(l,c){return this.ensureHostScope().logseq.api.exper_register_fenced_code_renderer(this.ctx.baseInfo.id,l,c)}registerExtensionsEnhancer(l,c){const y=this.ensureHostScope();return l==="katex"&&y.katex&&c(y.katex).catch(console.error),y.logseq.api.exper_register_extensions_enhancer(this.ctx.baseInfo.id,l,c)}ensureHostScope(){if(window===top)throw new Error("Can not access host scope!");return top}}function qe(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}const dt=s=>`task_callback_${s}`;class sn{constructor(l,c,y={}){qe(this,"_client",void 0),qe(this,"_requestId",void 0),qe(this,"_requestOptions",void 0),qe(this,"_promise",void 0),qe(this,"_aborted",!1),this._client=l,this._requestId=c,this._requestOptions=y,this._promise=new Promise((F,V)=>{if(!this._requestId)return V(null);this._client.once(dt(this._requestId),te=>{te&&te instanceof Error?V(te):F(te)})});const{success:P,fail:A,final:D}=this._requestOptions;this._promise.then(F=>{P?.(F)}).catch(F=>{A?.(F)}).finally(()=>{D?.()})}abort(){this._requestOptions.abortable&&!this._aborted&&(this._client.ctx._execCallableAPI("http_request_abort",this._requestId),this._aborted=!0)}get promise(){return this._promise}get client(){return this._client}get requestId(){return this._requestId}}class wt extends T.EventEmitter{constructor(l){super(),qe(this,"_ctx",void 0),this._ctx=l,this.ctx.caller.on("#lsp#request#callback",c=>{const y=c?.requestId;y&&this.emit(dt(y),c?.payload)})}static createRequestTask(l,c,y){return new sn(l,c,y)}async _request(l){const c=this.ctx.baseInfo.id,{success:y,fail:P,final:A,...D}=l,F=this.ctx.Experiments.invokeExperMethod("request",c,D),V=wt.createRequestTask(this.ctx.Request,F,l);return D.abortable?V:V.promise}get ctx(){return this._ctx}}const ft=Array.isArray,Zn=typeof Vi=="object"&&Vi&&Vi.Object===Object&&Vi;var cs=typeof self=="object"&&self&&self.Object===Object&&self;const At=Zn||cs||Function("return this")(),Gt=At.Symbol;var Ci=Object.prototype,ds=Ci.hasOwnProperty,fs=Ci.toString,er=Gt?Gt.toStringTag:void 0;const ps=function(s){var l=ds.call(s,er),c=s[er];try{s[er]=void 0;var y=!0}catch{}var P=fs.call(s);return y&&(l?s[er]=c:delete s[er]),P};var Si=Object.prototype.toString;const W=function(s){return Si.call(s)};var K=Gt?Gt.toStringTag:void 0;const le=function(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":K&&K in Object(s)?ps(s):W(s)},he=function(s){var l=typeof s;return s!=null&&(l=="object"||l=="function")},De=function(s){if(!he(s))return!1;var l=le(s);return l=="[object Function]"||l=="[object GeneratorFunction]"||l=="[object AsyncFunction]"||l=="[object Proxy]"},jt=At["__core-js_shared__"];var Yt,Sn=(Yt=/[^.]+$/.exec(jt&&jt.keys&&jt.keys.IE_PROTO||""))?"Symbol(src)_1."+Yt:"";const yo=function(s){return!!Sn&&Sn in s};var xi=Function.prototype.toString;const an=function(s){if(s!=null){try{return xi.call(s)}catch{}try{return s+""}catch{}}return""};var hs=/^\[object .+?Constructor\]$/,ms=Function.prototype,gs=Object.prototype,br=ms.toString,ys=gs.hasOwnProperty,vs=RegExp("^"+br.call(ys).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const ws=function(s){return!(!he(s)||yo(s))&&(De(s)?vs:hs).test(an(s))},vo=function(s,l){return s?.[l]},Rt=function(s,l){var c=vo(s,l);return ws(c)?c:void 0},Dt=function(){try{var s=Rt(Object,"defineProperty");return s({},"",{}),s}catch{}}(),_i=function(s,l,c){l=="__proto__"&&Dt?Dt(s,l,{configurable:!0,enumerable:!0,value:c,writable:!0}):s[l]=c},Pr=function(s){return function(l,c,y){for(var P=-1,A=Object(l),D=y(l),F=D.length;F--;){var V=D[s?F:++P];if(c(A[V],V,A)===!1)break}return l}}(),wo=function(s,l){for(var c=-1,y=Array(s);++c<s;)y[c]=l(c);return y},xn=function(s){return s!=null&&typeof s=="object"},Ei=function(s){return xn(s)&&le(s)=="[object Arguments]"};var Ti=Object.prototype,ks=Ti.hasOwnProperty,Ni=Ti.propertyIsEnumerable;const kt=Ei(function(){return arguments}())?Ei:function(s){return xn(s)&&ks.call(s,"callee")&&!Ni.call(s,"callee")},tr=function(){return!1};var Xt=t&&!t.nodeType&&t,Ir=Xt&&!0&&e&&!e.nodeType&&e,Or=Ir&&Ir.exports===Xt?At.Buffer:void 0;const ko=(Or?Or.isBuffer:void 0)||tr;var Cs=/^(?:0|[1-9]\d*)$/;const bi=function(s,l){var c=typeof s;return!!(l=l??9007199254740991)&&(c=="number"||c!="symbol"&&Cs.test(s))&&s>-1&&s%1==0&&s<l},Lr=function(s){return typeof s=="number"&&s>-1&&s%1==0&&s<=9007199254740991};var ke={};ke["[object Float32Array]"]=ke["[object Float64Array]"]=ke["[object Int8Array]"]=ke["[object Int16Array]"]=ke["[object Int32Array]"]=ke["[object Uint8Array]"]=ke["[object Uint8ClampedArray]"]=ke["[object Uint16Array]"]=ke["[object Uint32Array]"]=!0,ke["[object Arguments]"]=ke["[object Array]"]=ke["[object ArrayBuffer]"]=ke["[object Boolean]"]=ke["[object DataView]"]=ke["[object Date]"]=ke["[object Error]"]=ke["[object Function]"]=ke["[object Map]"]=ke["[object Number]"]=ke["[object Object]"]=ke["[object RegExp]"]=ke["[object Set]"]=ke["[object String]"]=ke["[object WeakMap]"]=!1;const Ue=function(s){return xn(s)&&Lr(s.length)&&!!ke[le(s)]},Pi=function(s){return function(l){return s(l)}};var $e=t&&!t.nodeType&&t,_n=$e&&!0&&e&&!e.nodeType&&e,Ie=_n&&_n.exports===$e&&Zn.process,En=function(){try{var s=_n&&_n.require&&_n.require("util").types;return s||Ie&&Ie.binding&&Ie.binding("util")}catch{}}(),Mr=En&&En.isTypedArray;const Co=Mr?Pi(Mr):Ue;var So=Object.prototype.hasOwnProperty;const Ii=function(s,l){var c=ft(s),y=!c&&kt(s),P=!c&&!y&&ko(s),A=!c&&!y&&!P&&Co(s),D=c||y||P||A,F=D?wo(s.length,String):[],V=F.length;for(var te in s)!l&&!So.call(s,te)||D&&(te=="length"||P&&(te=="offset"||te=="parent")||A&&(te=="buffer"||te=="byteLength"||te=="byteOffset")||bi(te,V))||F.push(te);return F};var Tn=Object.prototype;const un=function(s){var l=s&&s.constructor;return s===(typeof l=="function"&&l.prototype||Tn)},xo=function(s,l){return function(c){return s(l(c))}}(Object.keys,Object);var _o=Object.prototype.hasOwnProperty;const Nn=function(s){if(!un(s))return xo(s);var l=[];for(var c in Object(s))_o.call(s,c)&&c!="constructor"&&l.push(c);return l},Ar=function(s){return s!=null&&Lr(s.length)&&!De(s)},bn=function(s){return Ar(s)?Ii(s):Nn(s)},Oi=function(s,l){return s&&Pr(s,l,bn)},Eo=function(){this.__data__=[],this.size=0},Pn=function(s,l){return s===l||s!=s&&l!=l},zt=function(s,l){for(var c=s.length;c--;)if(Pn(s[c][0],l))return c;return-1};var In=Array.prototype.splice;const Li=function(s){var l=this.__data__,c=zt(l,s);return!(c<0)&&(c==l.length-1?l.pop():In.call(l,c,1),--this.size,!0)},Mi=function(s){var l=this.__data__,c=zt(l,s);return c<0?void 0:l[c][1]},Ai=function(s){return zt(this.__data__,s)>-1},To=function(s,l){var c=this.__data__,y=zt(c,s);return y<0?(++this.size,c.push([s,l])):c[y][1]=l,this};function cn(s){var l=-1,c=s==null?0:s.length;for(this.clear();++l<c;){var y=s[l];this.set(y[0],y[1])}}cn.prototype.clear=Eo,cn.prototype.delete=Li,cn.prototype.get=Mi,cn.prototype.has=Ai,cn.prototype.set=To;const On=cn,No=function(){this.__data__=new On,this.size=0},Jt=function(s){var l=this.__data__,c=l.delete(s);return this.size=l.size,c},jr=function(s){return this.__data__.get(s)},ji=function(s){return this.__data__.has(s)},nr=Rt(At,"Map"),rr=Rt(Object,"create"),Ln=function(){this.__data__=rr?rr(null):{},this.size=0},Ss=function(s){var l=this.has(s)&&delete this.__data__[s];return this.size-=l?1:0,l};var Ri=Object.prototype.hasOwnProperty;const bo=function(s){var l=this.__data__;if(rr){var c=l[s];return c==="__lodash_hash_undefined__"?void 0:c}return Ri.call(l,s)?l[s]:void 0};var Di=Object.prototype.hasOwnProperty;const zi=function(s){var l=this.__data__;return rr?l[s]!==void 0:Di.call(l,s)},xs=function(s,l){var c=this.__data__;return this.size+=this.has(s)?0:1,c[s]=rr&&l===void 0?"__lodash_hash_undefined__":l,this};function Ft(s){var l=-1,c=s==null?0:s.length;for(this.clear();++l<c;){var y=s[l];this.set(y[0],y[1])}}Ft.prototype.clear=Ln,Ft.prototype.delete=Ss,Ft.prototype.get=bo,Ft.prototype.has=zi,Ft.prototype.set=xs;const Rr=Ft,_s=function(){this.size=0,this.__data__={hash:new Rr,map:new(nr||On),string:new Rr}},Ut=function(s){var l=typeof s;return l=="string"||l=="number"||l=="symbol"||l=="boolean"?s!=="__proto__":s===null},or=function(s,l){var c=s.__data__;return Ut(l)?c[typeof l=="string"?"string":"hash"]:c.map},Fi=function(s){var l=or(this,s).delete(s);return this.size-=l?1:0,l},Ui=function(s){return or(this,s).get(s)},Es=function(s){return or(this,s).has(s)},ir=function(s,l){var c=or(this,s),y=c.size;return c.set(s,l),this.size+=c.size==y?0:1,this};function tt(s){var l=-1,c=s==null?0:s.length;for(this.clear();++l<c;){var y=s[l];this.set(y[0],y[1])}}tt.prototype.clear=_s,tt.prototype.delete=Fi,tt.prototype.get=Ui,tt.prototype.has=Es,tt.prototype.set=ir;const lr=tt,$i=function(s,l){var c=this.__data__;if(c instanceof On){var y=c.__data__;if(!nr||y.length<199)return y.push([s,l]),this.size=++c.size,this;c=this.__data__=new lr(y)}return c.set(s,l),this.size=c.size,this};function dn(s){var l=this.__data__=new On(s);this.size=l.size}dn.prototype.clear=No,dn.prototype.delete=Jt,dn.prototype.get=jr,dn.prototype.has=ji,dn.prototype.set=$i;const sr=dn,Ts=function(s){return this.__data__.set(s,"__lodash_hash_undefined__"),this},x=function(s){return this.__data__.has(s)};function q(s){var l=-1,c=s==null?0:s.length;for(this.__data__=new lr;++l<c;)this.add(s[l])}q.prototype.add=q.prototype.push=Ts,q.prototype.has=x;const H=q,Ce=function(s,l){for(var c=-1,y=s==null?0:s.length;++c<y;)if(l(s[c],c,s))return!0;return!1},Le=function(s,l){return s.has(l)},Ge=function(s,l,c,y,P,A){var D=1&c,F=s.length,V=l.length;if(F!=V&&!(D&&V>F))return!1;var te=A.get(s),ie=A.get(l);if(te&&ie)return te==l&&ie==s;var ee=-1,ae=!0,ge=2&c?new H:void 0;for(A.set(s,l),A.set(l,s);++ee<F;){var de=s[ee],Se=l[ee];if(y)var Te=D?y(Se,de,ee,l,s,A):y(de,Se,ee,s,l,A);if(Te!==void 0){if(Te)continue;ae=!1;break}if(ge){if(!Ce(l,function(He,Nt){if(!Le(ge,Nt)&&(de===He||P(de,He,c,y,A)))return ge.push(Nt)})){ae=!1;break}}else if(de!==Se&&!P(de,Se,c,y,A)){ae=!1;break}}return A.delete(s),A.delete(l),ae},Be=At.Uint8Array,ar=function(s){var l=-1,c=Array(s.size);return s.forEach(function(y,P){c[++l]=[P,y]}),c},Zt=function(s){var l=-1,c=Array(s.size);return s.forEach(function(y){c[++l]=y}),c};var pt=Gt?Gt.prototype:void 0,Po=pt?pt.valueOf:void 0;const Yu=function(s,l,c,y,P,A,D){switch(c){case"[object DataView]":if(s.byteLength!=l.byteLength||s.byteOffset!=l.byteOffset)return!1;s=s.buffer,l=l.buffer;case"[object ArrayBuffer]":return!(s.byteLength!=l.byteLength||!A(new Be(s),new Be(l)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Pn(+s,+l);case"[object Error]":return s.name==l.name&&s.message==l.message;case"[object RegExp]":case"[object String]":return s==l+"";case"[object Map]":var F=ar;case"[object Set]":var V=1&y;if(F||(F=Zt),s.size!=l.size&&!V)return!1;var te=D.get(s);if(te)return te==l;y|=2,D.set(s,l);var ie=Ge(F(s),F(l),y,P,A,D);return D.delete(s),ie;case"[object Symbol]":if(Po)return Po.call(s)==Po.call(l)}return!1},Xp=function(s,l){for(var c=-1,y=l.length,P=s.length;++c<y;)s[P+c]=l[c];return s},Jp=function(s,l,c){var y=l(s);return ft(s)?y:Xp(y,c(s))},Zp=function(s,l){for(var c=-1,y=s==null?0:s.length,P=0,A=[];++c<y;){var D=s[c];l(D,c,s)&&(A[P++]=D)}return A},eh=function(){return[]};var th=Object.prototype.propertyIsEnumerable,Xu=Object.getOwnPropertySymbols;const nh=Xu?function(s){return s==null?[]:(s=Object(s),Zp(Xu(s),function(l){return th.call(s,l)}))}:eh,Ju=function(s){return Jp(s,bn,nh)};var rh=Object.prototype.hasOwnProperty;const oh=function(s,l,c,y,P,A){var D=1&c,F=Ju(s),V=F.length;if(V!=Ju(l).length&&!D)return!1;for(var te=V;te--;){var ie=F[te];if(!(D?ie in l:rh.call(l,ie)))return!1}var ee=A.get(s),ae=A.get(l);if(ee&&ae)return ee==l&&ae==s;var ge=!0;A.set(s,l),A.set(l,s);for(var de=D;++te<V;){var Se=s[ie=F[te]],Te=l[ie];if(y)var He=D?y(Te,Se,ie,l,s,A):y(Se,Te,ie,s,l,A);if(!(He===void 0?Se===Te||P(Se,Te,c,y,A):He)){ge=!1;break}de||(de=ie=="constructor")}if(ge&&!de){var Nt=s.constructor,cr=l.constructor;Nt==cr||!("constructor"in s)||!("constructor"in l)||typeof Nt=="function"&&Nt instanceof Nt&&typeof cr=="function"&&cr instanceof cr||(ge=!1)}return A.delete(s),A.delete(l),ge},Ns=Rt(At,"DataView"),bs=Rt(At,"Promise"),Ps=Rt(At,"Set"),Is=Rt(At,"WeakMap");var Zu="[object Map]",ec="[object Promise]",tc="[object Set]",nc="[object WeakMap]",rc="[object DataView]",ih=an(Ns),lh=an(nr),sh=an(bs),ah=an(Ps),uh=an(Is),ur=le;(Ns&&ur(new Ns(new ArrayBuffer(1)))!=rc||nr&&ur(new nr)!=Zu||bs&&ur(bs.resolve())!=ec||Ps&&ur(new Ps)!=tc||Is&&ur(new Is)!=nc)&&(ur=function(s){var l=le(s),c=l=="[object Object]"?s.constructor:void 0,y=c?an(c):"";if(y)switch(y){case ih:return rc;case lh:return Zu;case sh:return ec;case ah:return tc;case uh:return nc}return l});const oc=ur;var ic="[object Arguments]",lc="[object Array]",Bi="[object Object]",sc=Object.prototype.hasOwnProperty;const ch=function(s,l,c,y,P,A){var D=ft(s),F=ft(l),V=D?lc:oc(s),te=F?lc:oc(l),ie=(V=V==ic?Bi:V)==Bi,ee=(te=te==ic?Bi:te)==Bi,ae=V==te;if(ae&&ko(s)){if(!ko(l))return!1;D=!0,ie=!1}if(ae&&!ie)return A||(A=new sr),D||Co(s)?Ge(s,l,c,y,P,A):Yu(s,l,V,c,y,P,A);if(!(1&c)){var ge=ie&&sc.call(s,"__wrapped__"),de=ee&&sc.call(l,"__wrapped__");if(ge||de){var Se=ge?s.value():s,Te=de?l.value():l;return A||(A=new sr),P(Se,Te,c,y,A)}}return!!ae&&(A||(A=new sr),oh(s,l,c,y,P,A))},ac=function s(l,c,y,P,A){return l===c||(l==null||c==null||!xn(l)&&!xn(c)?l!=l&&c!=c:ch(l,c,y,P,s,A))},dh=function(s,l,c,y){var P=c.length,A=P,D=!y;if(s==null)return!A;for(s=Object(s);P--;){var F=c[P];if(D&&F[2]?F[1]!==s[F[0]]:!(F[0]in s))return!1}for(;++P<A;){var V=(F=c[P])[0],te=s[V],ie=F[1];if(D&&F[2]){if(te===void 0&&!(V in s))return!1}else{var ee=new sr;if(y)var ae=y(te,ie,V,s,l,ee);if(!(ae===void 0?ac(ie,te,3,y,ee):ae))return!1}}return!0},uc=function(s){return s==s&&!he(s)},fh=function(s){for(var l=bn(s),c=l.length;c--;){var y=l[c],P=s[y];l[c]=[y,P,uc(P)]}return l},cc=function(s,l){return function(c){return c!=null&&c[s]===l&&(l!==void 0||s in Object(c))}},ph=function(s){var l=fh(s);return l.length==1&&l[0][2]?cc(l[0][0],l[0][1]):function(c){return c===s||dh(c,s,l)}},Os=function(s){return typeof s=="symbol"||xn(s)&&le(s)=="[object Symbol]"};var hh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,mh=/^\w*$/;const Ls=function(s,l){if(ft(s))return!1;var c=typeof s;return!(c!="number"&&c!="symbol"&&c!="boolean"&&s!=null&&!Os(s))||mh.test(s)||!hh.test(s)||l!=null&&s in Object(l)};function Ms(s,l){if(typeof s!="function"||l!=null&&typeof l!="function")throw new TypeError("Expected a function");var c=function(){var y=arguments,P=l?l.apply(this,y):y[0],A=c.cache;if(A.has(P))return A.get(P);var D=s.apply(this,y);return c.cache=A.set(P,D)||A,D};return c.cache=new(Ms.Cache||lr),c}Ms.Cache=lr;const gh=Ms;var yh=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,vh=/\\(\\)?/g;const wh=function(s){var l=gh(s,function(y){return c.size===500&&c.clear(),y}),c=l.cache;return l}(function(s){var l=[];return s.charCodeAt(0)===46&&l.push(""),s.replace(yh,function(c,y,P,A){l.push(P?A.replace(vh,"$1"):y||c)}),l}),kh=function(s,l){for(var c=-1,y=s==null?0:s.length,P=Array(y);++c<y;)P[c]=l(s[c],c,s);return P};var dc=Gt?Gt.prototype:void 0,fc=dc?dc.toString:void 0;const Ch=function s(l){if(typeof l=="string")return l;if(ft(l))return kh(l,s)+"";if(Os(l))return fc?fc.call(l):"";var c=l+"";return c=="0"&&1/l==-1/0?"-0":c},Sh=function(s){return s==null?"":Ch(s)},pc=function(s,l){return ft(s)?s:Ls(s,l)?[s]:wh(Sh(s))},Hi=function(s){if(typeof s=="string"||Os(s))return s;var l=s+"";return l=="0"&&1/s==-1/0?"-0":l},hc=function(s,l){for(var c=0,y=(l=pc(l,s)).length;s!=null&&c<y;)s=s[Hi(l[c++])];return c&&c==y?s:void 0},xh=function(s,l,c){var y=s==null?void 0:hc(s,l);return y===void 0?c:y},_h=function(s,l){return s!=null&&l in Object(s)},Eh=function(s,l,c){for(var y=-1,P=(l=pc(l,s)).length,A=!1;++y<P;){var D=Hi(l[y]);if(!(A=s!=null&&c(s,D)))break;s=s[D]}return A||++y!=P?A:!!(P=s==null?0:s.length)&&Lr(P)&&bi(D,P)&&(ft(s)||kt(s))},Th=function(s,l){return s!=null&&Eh(s,l,_h)},Nh=function(s,l){return Ls(s)&&uc(l)?cc(Hi(s),l):function(c){var y=xh(c,s);return y===void 0&&y===l?Th(c,s):ac(l,y,3)}},bh=function(s){return s},Ph=function(s){return function(l){return l?.[s]}},Ih=function(s){return function(l){return hc(l,s)}},Oh=function(s){return Ls(s)?Ph(Hi(s)):Ih(s)},Lh=function(s){return typeof s=="function"?s:s==null?bh:typeof s=="object"?ft(s)?Nh(s[0],s[1]):ph(s):Oh(s)},Mh=function(s,l){var c={};return l=Lh(l),Oi(s,function(y,P,A){_i(c,l(y,P,A),y)}),c};function mc(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}class Ah{constructor(l,c){mc(this,"ctx",void 0),mc(this,"serviceHooks",void 0),this.ctx=l,this.serviceHooks=c,l._execCallableAPI("register-search-service",l.baseInfo.id,c.name,c.options),Object.entries({query:{f:"onQuery",args:["graph","q",!0],reply:!0,transformOutput:y=>(ft(y?.blocks)&&(y.blocks=y.blocks.map(P=>P&&Mh(P,(A,D)=>`block/${D}`))),y)},rebuildBlocksIndice:{f:"onIndiceInit",args:["graph","blocks"]},transactBlocks:{f:"onBlocksChanged",args:["graph","data"]},truncateBlocks:{f:"onIndiceReset",args:["graph"]},removeDb:{f:"onGraph",args:["graph"]}}).forEach(([y,P])=>{const A=(D=>`service:search:${D}:${c.name}`)(y);l.caller.on(A,async D=>{if(De(c?.[P.f])){let F=null;try{F=await c[P.f].apply(c,(P.args||[]).map(V=>{if(D){if(V===!0)return D;if(D.hasOwnProperty(V)){const te=D[V];return delete D[V],te}}})),P.transformOutput&&(F=P.transformOutput(F))}catch(V){console.error("[SearchService] ",V),F=V}finally{P.reply&&l.caller.call(`${A}:reply`,F)}}})})}}function $t(s,l,c){return l in s?Object.defineProperty(s,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[l]=c,s}const jh=Symbol.for("proxy-continue"),Rh=R()("LSPlugin:user"),gc=new I("",{console:!0});function Dr(s,l,c){var y;const{key:P,label:A,desc:D,palette:F,keybinding:V,extras:te}=l;if(typeof c!="function")return this.logger.error(`${P||A}: command action should be function.`),!1;const ie=function(ae){if(typeof ae=="string")return ae.trim().replace(/\s/g,"_").toLowerCase()}(P);if(!ie)return this.logger.error(`${A}: command key is required.`),!1;const ee=`SimpleCommandHook${ie}${++wc}`;this.Editor["on"+ee](c),(y=this.caller)===null||y===void 0||y.call("api:call",{method:"register-plugin-simple-command",args:[this.baseInfo.id,[{key:ie,label:A,type:s,desc:D,keybinding:V,extras:te},["editor/hook",ee]],F]})}function yc(s){return!(typeof(l=s)!="string"||l.length!==36||!/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi.test(l))||(gc.error(`#${s} is not a valid UUID string.`),!1);var l}let Wi=null,vc=new Map;const Dh={async getInfo(s){return Wi||(Wi=await this._execCallableAPIAsync("get-app-info")),typeof s=="string"?Wi[s]:Wi},registerCommand:Dr,registerSearchService(s){if(vc.has(s.name))throw new Error(`SearchService: #${s.name} has registered!`);vc.set(s.name,new Ah(this,s))},registerCommandPalette(s,l){const{key:c,label:y,keybinding:P}=s;return Dr.call(this,"$palette$",{key:c,label:y,palette:!0,keybinding:P},l)},registerCommandShortcut(s,l,c={}){typeof s=="string"&&(s={mode:"global",binding:s});const{binding:y}=s,P="$shortcut$",A=c.key||P+f(y?.toString());return Dr.call(this,P,{...c,key:A,palette:!1,keybinding:s},l)},registerUIItem(s,l){var c;const y=this.baseInfo.id;(c=this.caller)===null||c===void 0||c.call("api:call",{method:"register-plugin-ui-item",args:[y,s,l]})},registerPageMenuItem(s,l){if(typeof l!="function")return!1;const c=s+"_"+this.baseInfo.id,y=s;Dr.call(this,"page-menu-item",{key:c,label:y},l)},onBlockRendererSlotted(s,l){if(!yc(s))return;const c=this.baseInfo.id,y=`hook:editor:${f(`slot:${s}`)}`;return this.caller.on(y,l),this.App._installPluginHook(c,y),()=>{this.caller.off(y,l),this.App._uninstallPluginHook(c,y)}},invokeExternalPlugin(s,...l){var c;if(!(s=(c=s)===null||c===void 0?void 0:c.trim()))return;let[y,P]=s.split(".");if(!["models","commands"].includes(P?.toLowerCase()))throw new Error("Type only support '.models' or '.commands' currently.");const A=s.replace(`${y}.${P}.`,"");if(!y||!P||!A)throw new Error(`Illegal type of #${s} to invoke external plugin.`);return this._execCallableAPIAsync("invoke_external_plugin_cmd",y,P.toLowerCase(),A,l)},setFullScreen(s){const l=(...c)=>this._callWin("setFullScreen",...c);s==="toggle"?this._callWin("isFullScreen").then(c=>{c?l():l(!0)}):s?l(!0):l()}};let wc=0;const zh={newBlockUUID(){return this._execCallableAPIAsync("new_block_uuid")},registerSlashCommand(s,l){var c;Rh("Register slash command #",this.baseInfo.id,s,l),typeof l=="function"&&(l=[["editor/clear-current-slash",!1],["editor/restore-saved-cursor"],["editor/hook",l]]),l=l.map(y=>{const[P,...A]=y;if(P==="editor/hook"){let D=A[0],F=()=>{var te;(te=this.caller)===null||te===void 0||te.callUserModel(D)};typeof D=="function"&&(F=D);const V=`SlashCommandHook${P}${++wc}`;y[1]=V,this.Editor["on"+V](F)}return y}),(c=this.caller)===null||c===void 0||c.call("api:call",{method:"register-plugin-slash-command",args:[this.baseInfo.id,[s,l]]})},registerBlockContextMenuItem(s,l){if(typeof l!="function")return!1;const c=s+"_"+this.baseInfo.id;Dr.call(this,"block-context-menu-item",{key:c,label:s},l)},registerHighlightContextMenuItem(s,l,c){if(typeof l!="function")return!1;const y=s+"_"+this.baseInfo.id;Dr.call(this,"highlight-context-menu-item",{key:y,label:s,extras:c},l)},scrollToBlockInPage(s,l,c){const y="block-content-"+l;c!=null&&c.replaceState?this.App.replaceState("page",{name:s},{anchor:y}):this.App.pushState("page",{name:s},{anchor:y})}},Fh={onBlockChanged(s,l){if(!yc(s))return;const c=this.baseInfo.id,y=`hook:db:${f(`block:${s}`)}`,P=({block:A,txData:D,txMeta:F})=>{A.uuid===s&&l(A,D,F)};return this.caller.on(y,P),this.App._installPluginHook(c,y),()=>{this.caller.off(y,P),this.App._uninstallPluginHook(c,y)}},datascriptQuery(s,...l){return l.pop(),l!=null&&l.some(c=>typeof c=="function")?this.Experiments.ensureHostScope().logseq.api.datascript_query(s,...l):this._execCallableAPIAsync("datascript_query",s,...l)}},Uh={},$h={},Bh={makeSandboxStorage(){return new ve(this,{assets:!0})}};class As extends O(){constructor(l,c){super(),$t(this,"_baseInfo",void 0),$t(this,"_caller",void 0),$t(this,"_version","0.0.17"),$t(this,"_debugTag",""),$t(this,"_settingsSchema",void 0),$t(this,"_connected",!1),$t(this,"_ui",new Map),$t(this,"_mFileStorage",void 0),$t(this,"_mRequest",void 0),$t(this,"_mExperiments",void 0),$t(this,"_beforeunloadCallback",void 0),this._baseInfo=l,this._caller=c,c.on("sys:ui:visible",y=>{y!=null&&y.toggle&&this.toggleMainUI()}),c.on("settings:changed",y=>{const P=Object.assign({},this.settings),A=Object.assign(this._baseInfo.settings,y);this.emit("settings:changed",{...A},P)}),c.on("beforeunload",async y=>{const{actor:P,...A}=y,D=this._beforeunloadCallback;try{D&&await D(A),P?.resolve(null)}catch(F){this.logger.error("[beforeunload] ",F),P?.reject(F)}})}async ready(l,c){var y,P;if(!this._connected)try{var A;typeof l=="function"&&(c=l,l={});let D=await this._caller.connectToParent(l);this._connected=!0,y=this._baseInfo,P=D,D=d()(y,P,{arrayMerge:(F,V)=>V}),this._baseInfo=D,(A=D)!==null&&A!==void 0&&A.id&&(this._debugTag=this._caller.debugTag=`#${D.id} [${D.name}]`,this.logger.setTag(this._debugTag)),this._settingsSchema&&(D.settings=function(F,V){const te=(V||[]).reduce((ie,ee)=>("default"in ee&&(ie[ee.key]=ee.default),ie),{});return Object.assign(te,F)}(D.settings,this._settingsSchema),await this.useSettingsSchema(this._settingsSchema));try{await this._execCallableAPIAsync("setSDKMetadata",{version:this._version})}catch(F){console.warn(F)}c&&c.call(this,D)}catch(D){console.error(`${this._debugTag} [Ready Error]`,D)}}ensureConnected(){if(!this._connected)throw new Error("not connected")}beforeunload(l){typeof l=="function"&&(this._beforeunloadCallback=l)}provideModel(l){return this.caller._extendUserModel(l),this}provideTheme(l){return this.caller.call("provider:theme",l),this}provideStyle(l){return this.caller.call("provider:style",l),this}provideUI(l){return this.caller.call("provider:ui",l),this}useSettingsSchema(l){return this.connected&&this.caller.call("settings:schema",{schema:l,isSync:!0}),this._settingsSchema=l,this}updateSettings(l){this.caller.call("settings:update",l)}onSettingsChanged(l){const c="settings:changed";return this.on(c,l),()=>this.off(c,l)}showSettingsUI(){this.caller.call("settings:visible:changed",{visible:!0})}hideSettingsUI(){this.caller.call("settings:visible:changed",{visible:!1})}setMainUIAttrs(l){this.caller.call("main-ui:attrs",l)}setMainUIInlineStyle(l){this.caller.call("main-ui:style",l)}hideMainUI(l){const c={key:0,visible:!1,cursor:l?.restoreEditingCursor};this.caller.call("main-ui:visible",c),this.emit("ui:visible:changed",c),this._ui.set(c.key,c)}showMainUI(l){const c={key:0,visible:!0,autoFocus:l?.autoFocus};this.caller.call("main-ui:visible",c),this.emit("ui:visible:changed",c),this._ui.set(c.key,c)}toggleMainUI(){const c=this._ui.get(0);c&&c.visible?this.hideMainUI():this.showMainUI()}get version(){return this._version}get isMainUIVisible(){const l=this._ui.get(0);return!!(l&&l.visible)}get connected(){return this._connected}get baseInfo(){return this._baseInfo}get effect(){return(l=this)&&(((c=l.baseInfo)===null||c===void 0?void 0:c.effect)||!((y=l.baseInfo)!==null&&y!==void 0&&y.iir));var l,c,y}get logger(){return gc}get settings(){var l;return(l=this.baseInfo)===null||l===void 0?void 0:l.settings}get caller(){return this._caller}resolveResourceFullUrl(l){if(this.ensureConnected(),l)return l=l.replace(/^[.\\/]+/,""),N(this._baseInfo.lsr,l)}_makeUserProxy(l,c){const y=this,P=this.caller;return new Proxy(l,{get(A,D,F){const V=A[D];return function(...te){if(V){const ee=V.apply(y,te.concat(c));if(ee!==jh)return ee}if(c){const ee=D.toString().match(/^(once|off|on)/i);if(ee!=null){const ae=ee[0].toLowerCase(),ge=ee.input,de=ae==="off",Se=y.baseInfo.id;let Te=ge.slice(ae.length),He=te[0],Nt=te[1];typeof He=="string"&&typeof Nt=="function"&&(He=He.replace(/^logseq./,":"),Te=`${Te}${He}`,He=Nt,Nt=te[2]),Te=`hook:${c}:${f(Te)}`,P[ae](Te,He);const cr=()=>{P.off(Te,He),P.listenerCount(Te)||y.App._uninstallPluginHook(Se,Te)};return de?void cr():(y.App._installPluginHook(Se,Te,Nt),cr)}}let ie=D;return["git","ui","assets"].includes(c)&&(ie=c+"_"+ie),P.callAsync("api:call",{tag:c,method:ie,args:te})}}})}_execCallableAPIAsync(l,...c){return this._caller.callAsync("api:call",{method:l,args:c})}_execCallableAPI(l,...c){this._caller.call("api:call",{method:l,args:c})}_callWin(...l){return this._execCallableAPIAsync("_callMainWin",...l)}get App(){return this._makeUserProxy(Dh,"app")}get Editor(){return this._makeUserProxy(zh,"editor")}get DB(){return this._makeUserProxy(Fh,"db")}get Git(){return this._makeUserProxy(Uh,"git")}get UI(){return this._makeUserProxy($h,"ui")}get Assets(){return this._makeUserProxy(Bh,"assets")}get FileStorage(){let l=this._mFileStorage;return l||(l=this._mFileStorage=new ve(this)),l}get Request(){let l=this._mRequest;return l||(l=this._mRequest=new wt(this)),l}get Experiments(){let l=this._mExperiments;return l||(l=this._mExperiments=new Ee(this)),l}}function kc(s,l){return new As(s,l)}if(window.__LSP__HOST__==null){const s=new Y(null);window.logseq=kc({},s)}})(),i})())})(Sl,Sl.exports);Sl.exports;var Wd={exports:{}},Et={},Vd={exports:{}},qd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(z,v){var b=z.length;z.push(v);e:for(;0<b;){var U=b-1>>>1,re=z[U];if(0<o(re,v))z[U]=v,z[b]=re,b=U;else break e}}function n(z){return z.length===0?null:z[0]}function r(z){if(z.length===0)return null;var v=z[0],b=z.pop();if(b!==v){z[0]=b;e:for(var U=0,re=z.length,fe=re>>>1;U<fe;){var B=2*(U+1)-1,Q=z[B],se=B+1,Re=z[se];if(0>o(Q,b))se<re&&0>o(Re,Q)?(z[U]=Re,z[se]=b,U=se):(z[U]=Q,z[B]=b,U=B);else if(se<re&&0>o(Re,b))z[U]=Re,z[se]=b,U=se;else break e}}return v}function o(z,v){var b=z.sortIndex-v.sortIndex;return b!==0?b:z.id-v.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,u=a.now();e.unstable_now=function(){return a.now()-u}}var d=[],m=[],w=1,_=null,E=3,S=!1,C=!1,T=!1,O=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(z){for(var v=n(m);v!==null;){if(v.callback===null)r(m);else if(v.startTime<=z)r(m),v.sortIndex=v.expirationTime,t(d,v);else break;v=n(m)}}function I(z){if(T=!1,f(z),!C)if(n(d)!==null)C=!0,_e(N);else{var v=n(m);v!==null&&we(I,v.startTime-z)}}function N(z,v){C=!1,T&&(T=!1,g(k),k=-1),S=!0;var b=E;try{for(f(v),_=n(d);_!==null&&(!(_.expirationTime>v)||z&&!G());){var U=_.callback;if(typeof U=="function"){_.callback=null,E=_.priorityLevel;var re=U(_.expirationTime<=v);v=e.unstable_now(),typeof re=="function"?_.callback=re:_===n(d)&&r(d),f(v)}else r(d);_=n(d)}if(_!==null)var fe=!0;else{var B=n(m);B!==null&&we(I,B.startTime-v),fe=!1}return fe}finally{_=null,E=b,S=!1}}var L=!1,h=null,k=-1,R=5,M=-1;function G(){return!(e.unstable_now()-M<R)}function ce(){if(h!==null){var z=e.unstable_now();M=z;var v=!0;try{v=h(!0,z)}finally{v?ue():(L=!1,h=null)}}else L=!1}var ue;if(typeof p=="function")ue=function(){p(ce)};else if(typeof MessageChannel<"u"){var pe=new MessageChannel,oe=pe.port2;pe.port1.onmessage=ce,ue=function(){oe.postMessage(null)}}else ue=function(){O(ce,0)};function _e(z){h=z,L||(L=!0,ue())}function we(z,v){k=O(function(){z(e.unstable_now())},v)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(z){z.callback=null},e.unstable_continueExecution=function(){C||S||(C=!0,_e(N))},e.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<z?Math.floor(1e3/z):5},e.unstable_getCurrentPriorityLevel=function(){return E},e.unstable_getFirstCallbackNode=function(){return n(d)},e.unstable_next=function(z){switch(E){case 1:case 2:case 3:var v=3;break;default:v=E}var b=E;E=v;try{return z()}finally{E=b}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(z,v){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var b=E;E=z;try{return v()}finally{E=b}},e.unstable_scheduleCallback=function(z,v,b){var U=e.unstable_now();switch(typeof b=="object"&&b!==null?(b=b.delay,b=typeof b=="number"&&0<b?U+b:U):b=U,z){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=b+re,z={id:w++,callback:v,priorityLevel:z,startTime:b,expirationTime:re,sortIndex:-1},b>U?(z.sortIndex=b,t(m,z),n(d)===null&&z===n(m)&&(T?(g(k),k=-1):T=!0,we(I,b-U))):(z.sortIndex=re,t(d,z),C||S||(C=!0,_e(N))),z},e.unstable_shouldYield=G,e.unstable_wrapCallback=function(z){var v=E;return function(){var b=E;E=v;try{return z.apply(this,arguments)}finally{E=b}}}})(qd);Vd.exports=qd;var fm=Vd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pm=ne,_t=fm;function $(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Kd=new Set,Jo={};function Er(e,t){lo(e,t),lo(e+"Capture",t)}function lo(e,t){for(Jo[e]=t,e=0;e<t.length;e++)Kd.add(t[e])}var yn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),aa=Object.prototype.hasOwnProperty,hm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,_c={},Ec={};function mm(e){return aa.call(Ec,e)?!0:aa.call(_c,e)?!1:hm.test(e)?Ec[e]=!0:(_c[e]=!0,!1)}function gm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ym(e,t,n,r){if(t===null||typeof t>"u"||gm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ct(e,t,n,r,o,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var et={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){et[e]=new ct(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];et[t]=new ct(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){et[e]=new ct(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){et[e]=new ct(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){et[e]=new ct(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){et[e]=new ct(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){et[e]=new ct(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){et[e]=new ct(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){et[e]=new ct(e,5,!1,e.toLowerCase(),null,!1,!1)});var lu=/[\-:]([a-z])/g;function su(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(lu,su);et[t]=new ct(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(lu,su);et[t]=new ct(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(lu,su);et[t]=new ct(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){et[e]=new ct(e,1,!1,e.toLowerCase(),null,!1,!1)});et.xlinkHref=new ct("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){et[e]=new ct(e,1,!1,e.toLowerCase(),null,!0,!0)});function au(e,t,n,r){var o=et.hasOwnProperty(t)?et[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ym(t,n,o,r)&&(n=null),r||o===null?mm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Cn=pm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ki=Symbol.for("react.element"),Fr=Symbol.for("react.portal"),Ur=Symbol.for("react.fragment"),uu=Symbol.for("react.strict_mode"),ua=Symbol.for("react.profiler"),Qd=Symbol.for("react.provider"),Gd=Symbol.for("react.context"),cu=Symbol.for("react.forward_ref"),ca=Symbol.for("react.suspense"),da=Symbol.for("react.suspense_list"),du=Symbol.for("react.memo"),An=Symbol.for("react.lazy"),Yd=Symbol.for("react.offscreen"),Tc=Symbol.iterator;function Io(e){return e===null||typeof e!="object"?null:(e=Tc&&e[Tc]||e["@@iterator"],typeof e=="function"?e:null)}var je=Object.assign,Rs;function Fo(e){if(Rs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Rs=t&&t[1]||""}return`
`+Rs+e}var Ds=!1;function zs(e,t){if(!e||Ds)return"";Ds=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(m){var r=m}Reflect.construct(e,[],t)}else{try{t.call()}catch(m){r=m}e.call(t.prototype)}else{try{throw Error()}catch(m){r=m}e()}}catch(m){if(m&&r&&typeof m.stack=="string"){for(var o=m.stack.split(`
`),i=r.stack.split(`
`),a=o.length-1,u=i.length-1;1<=a&&0<=u&&o[a]!==i[u];)u--;for(;1<=a&&0<=u;a--,u--)if(o[a]!==i[u]){if(a!==1||u!==1)do if(a--,u--,0>u||o[a]!==i[u]){var d=`
`+o[a].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),d}while(1<=a&&0<=u);break}}}finally{Ds=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Fo(e):""}function vm(e){switch(e.tag){case 5:return Fo(e.type);case 16:return Fo("Lazy");case 13:return Fo("Suspense");case 19:return Fo("SuspenseList");case 0:case 2:case 15:return e=zs(e.type,!1),e;case 11:return e=zs(e.type.render,!1),e;case 1:return e=zs(e.type,!0),e;default:return""}}function fa(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ur:return"Fragment";case Fr:return"Portal";case ua:return"Profiler";case uu:return"StrictMode";case ca:return"Suspense";case da:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Gd:return(e.displayName||"Context")+".Consumer";case Qd:return(e._context.displayName||"Context")+".Provider";case cu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case du:return t=e.displayName||null,t!==null?t:fa(e.type)||"Memo";case An:t=e._payload,e=e._init;try{return fa(e(t))}catch{}}return null}function wm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fa(t);case 8:return t===uu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Qn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Xd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function km(e){var t=Xd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Qi(e){e._valueTracker||(e._valueTracker=km(e))}function Jd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Xd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function xl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function pa(e,t){var n=t.checked;return je({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Nc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Qn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Zd(e,t){t=t.checked,t!=null&&au(e,"checked",t,!1)}function ha(e,t){Zd(e,t);var n=Qn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ma(e,t.type,n):t.hasOwnProperty("defaultValue")&&ma(e,t.type,Qn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function bc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ma(e,t,n){(t!=="number"||xl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Uo=Array.isArray;function Zr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Qn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ga(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error($(91));return je({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error($(92));if(Uo(n)){if(1<n.length)throw Error($(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Qn(n)}}function ef(e,t){var n=Qn(t.value),r=Qn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ic(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function tf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ya(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?tf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Gi,nf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Gi=Gi||document.createElement("div"),Gi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Gi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Zo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ho={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Cm=["Webkit","ms","Moz","O"];Object.keys(Ho).forEach(function(e){Cm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ho[t]=Ho[e]})});function rf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ho.hasOwnProperty(e)&&Ho[e]?(""+t).trim():t+"px"}function of(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=rf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Sm=je({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function va(e,t){if(t){if(Sm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error($(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error($(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error($(61))}if(t.style!=null&&typeof t.style!="object")throw Error($(62))}}function wa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ka=null;function fu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ca=null,eo=null,to=null;function Oc(e){if(e=vi(e)){if(typeof Ca!="function")throw Error($(280));var t=e.stateNode;t&&(t=Zl(t),Ca(e.stateNode,e.type,t))}}function lf(e){eo?to?to.push(e):to=[e]:eo=e}function sf(){if(eo){var e=eo,t=to;if(to=eo=null,Oc(e),t)for(e=0;e<t.length;e++)Oc(t[e])}}function af(e,t){return e(t)}function uf(){}var Fs=!1;function cf(e,t,n){if(Fs)return e(t,n);Fs=!0;try{return af(e,t,n)}finally{Fs=!1,(eo!==null||to!==null)&&(uf(),sf())}}function ei(e,t){var n=e.stateNode;if(n===null)return null;var r=Zl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error($(231,t,typeof n));return n}var Sa=!1;if(yn)try{var Oo={};Object.defineProperty(Oo,"passive",{get:function(){Sa=!0}}),window.addEventListener("test",Oo,Oo),window.removeEventListener("test",Oo,Oo)}catch{Sa=!1}function xm(e,t,n,r,o,i,a,u,d){var m=Array.prototype.slice.call(arguments,3);try{t.apply(n,m)}catch(w){this.onError(w)}}var Wo=!1,_l=null,El=!1,xa=null,_m={onError:function(e){Wo=!0,_l=e}};function Em(e,t,n,r,o,i,a,u,d){Wo=!1,_l=null,xm.apply(_m,arguments)}function Tm(e,t,n,r,o,i,a,u,d){if(Em.apply(this,arguments),Wo){if(Wo){var m=_l;Wo=!1,_l=null}else throw Error($(198));El||(El=!0,xa=m)}}function Tr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function df(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Lc(e){if(Tr(e)!==e)throw Error($(188))}function Nm(e){var t=e.alternate;if(!t){if(t=Tr(e),t===null)throw Error($(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Lc(o),e;if(i===r)return Lc(o),t;i=i.sibling}throw Error($(188))}if(n.return!==r.return)n=o,r=i;else{for(var a=!1,u=o.child;u;){if(u===n){a=!0,n=o,r=i;break}if(u===r){a=!0,r=o,n=i;break}u=u.sibling}if(!a){for(u=i.child;u;){if(u===n){a=!0,n=i,r=o;break}if(u===r){a=!0,r=i,n=o;break}u=u.sibling}if(!a)throw Error($(189))}}if(n.alternate!==r)throw Error($(190))}if(n.tag!==3)throw Error($(188));return n.stateNode.current===n?e:t}function ff(e){return e=Nm(e),e!==null?pf(e):null}function pf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=pf(e);if(t!==null)return t;e=e.sibling}return null}var hf=_t.unstable_scheduleCallback,Mc=_t.unstable_cancelCallback,bm=_t.unstable_shouldYield,Pm=_t.unstable_requestPaint,Fe=_t.unstable_now,Im=_t.unstable_getCurrentPriorityLevel,pu=_t.unstable_ImmediatePriority,mf=_t.unstable_UserBlockingPriority,Tl=_t.unstable_NormalPriority,Om=_t.unstable_LowPriority,gf=_t.unstable_IdlePriority,Gl=null,on=null;function Lm(e){if(on&&typeof on.onCommitFiberRoot=="function")try{on.onCommitFiberRoot(Gl,e,void 0,(e.current.flags&128)===128)}catch{}}var qt=Math.clz32?Math.clz32:jm,Mm=Math.log,Am=Math.LN2;function jm(e){return e>>>=0,e===0?32:31-(Mm(e)/Am|0)|0}var Yi=64,Xi=4194304;function $o(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Nl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var u=a&~o;u!==0?r=$o(u):(i&=a,i!==0&&(r=$o(i)))}else a=n&~o,a!==0?r=$o(a):i!==0&&(r=$o(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qt(t),o=1<<n,r|=e[n],t&=~o;return r}function Rm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Dm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-qt(i),u=1<<a,d=o[a];d===-1?(!(u&n)||u&r)&&(o[a]=Rm(u,t)):d<=t&&(e.expiredLanes|=u),i&=~u}}function _a(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function yf(){var e=Yi;return Yi<<=1,!(Yi&4194240)&&(Yi=64),e}function Us(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qt(t),e[t]=n}function zm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-qt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function hu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var xe=0;function vf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var wf,mu,kf,Cf,Sf,Ea=!1,Ji=[],Un=null,$n=null,Bn=null,ti=new Map,ni=new Map,Rn=[],Fm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ac(e,t){switch(e){case"focusin":case"focusout":Un=null;break;case"dragenter":case"dragleave":$n=null;break;case"mouseover":case"mouseout":Bn=null;break;case"pointerover":case"pointerout":ti.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ni.delete(t.pointerId)}}function Lo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=vi(t),t!==null&&mu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Um(e,t,n,r,o){switch(t){case"focusin":return Un=Lo(Un,e,t,n,r,o),!0;case"dragenter":return $n=Lo($n,e,t,n,r,o),!0;case"mouseover":return Bn=Lo(Bn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return ti.set(i,Lo(ti.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,ni.set(i,Lo(ni.get(i)||null,e,t,n,r,o)),!0}return!1}function xf(e){var t=pr(e.target);if(t!==null){var n=Tr(t);if(n!==null){if(t=n.tag,t===13){if(t=df(n),t!==null){e.blockedOn=t,Sf(e.priority,function(){kf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function fl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ta(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ka=r,n.target.dispatchEvent(r),ka=null}else return t=vi(n),t!==null&&mu(t),e.blockedOn=n,!1;t.shift()}return!0}function jc(e,t,n){fl(e)&&n.delete(t)}function $m(){Ea=!1,Un!==null&&fl(Un)&&(Un=null),$n!==null&&fl($n)&&($n=null),Bn!==null&&fl(Bn)&&(Bn=null),ti.forEach(jc),ni.forEach(jc)}function Mo(e,t){e.blockedOn===t&&(e.blockedOn=null,Ea||(Ea=!0,_t.unstable_scheduleCallback(_t.unstable_NormalPriority,$m)))}function ri(e){function t(o){return Mo(o,e)}if(0<Ji.length){Mo(Ji[0],e);for(var n=1;n<Ji.length;n++){var r=Ji[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Un!==null&&Mo(Un,e),$n!==null&&Mo($n,e),Bn!==null&&Mo(Bn,e),ti.forEach(t),ni.forEach(t),n=0;n<Rn.length;n++)r=Rn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Rn.length&&(n=Rn[0],n.blockedOn===null);)xf(n),n.blockedOn===null&&Rn.shift()}var no=Cn.ReactCurrentBatchConfig,bl=!0;function Bm(e,t,n,r){var o=xe,i=no.transition;no.transition=null;try{xe=1,gu(e,t,n,r)}finally{xe=o,no.transition=i}}function Hm(e,t,n,r){var o=xe,i=no.transition;no.transition=null;try{xe=4,gu(e,t,n,r)}finally{xe=o,no.transition=i}}function gu(e,t,n,r){if(bl){var o=Ta(e,t,n,r);if(o===null)Ys(e,t,r,Pl,n),Ac(e,r);else if(Um(o,e,t,n,r))r.stopPropagation();else if(Ac(e,r),t&4&&-1<Fm.indexOf(e)){for(;o!==null;){var i=vi(o);if(i!==null&&wf(i),i=Ta(e,t,n,r),i===null&&Ys(e,t,r,Pl,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ys(e,t,r,null,n)}}var Pl=null;function Ta(e,t,n,r){if(Pl=null,e=fu(r),e=pr(e),e!==null)if(t=Tr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=df(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Pl=e,null}function _f(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Im()){case pu:return 1;case mf:return 4;case Tl:case Om:return 16;case gf:return 536870912;default:return 16}default:return 16}}var zn=null,yu=null,pl=null;function Ef(){if(pl)return pl;var e,t=yu,n=t.length,r,o="value"in zn?zn.value:zn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[i-r];r++);return pl=o.slice(e,1<r?1-r:void 0)}function hl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Zi(){return!0}function Rc(){return!1}function Tt(e){function t(n,r,o,i,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(n=e[u],this[u]=n?n(i):i[u]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Zi:Rc,this.isPropagationStopped=Rc,this}return je(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Zi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Zi)},persist:function(){},isPersistent:Zi}),t}var mo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vu=Tt(mo),yi=je({},mo,{view:0,detail:0}),Wm=Tt(yi),$s,Bs,Ao,Yl=je({},yi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ao&&(Ao&&e.type==="mousemove"?($s=e.screenX-Ao.screenX,Bs=e.screenY-Ao.screenY):Bs=$s=0,Ao=e),$s)},movementY:function(e){return"movementY"in e?e.movementY:Bs}}),Dc=Tt(Yl),Vm=je({},Yl,{dataTransfer:0}),qm=Tt(Vm),Km=je({},yi,{relatedTarget:0}),Hs=Tt(Km),Qm=je({},mo,{animationName:0,elapsedTime:0,pseudoElement:0}),Gm=Tt(Qm),Ym=je({},mo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Xm=Tt(Ym),Jm=je({},mo,{data:0}),zc=Tt(Jm),Zm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},eg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},tg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ng(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=tg[e])?!!t[e]:!1}function wu(){return ng}var rg=je({},yi,{key:function(e){if(e.key){var t=Zm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=hl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?eg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wu,charCode:function(e){return e.type==="keypress"?hl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?hl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),og=Tt(rg),ig=je({},Yl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Fc=Tt(ig),lg=je({},yi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wu}),sg=Tt(lg),ag=je({},mo,{propertyName:0,elapsedTime:0,pseudoElement:0}),ug=Tt(ag),cg=je({},Yl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),dg=Tt(cg),fg=[9,13,27,32],ku=yn&&"CompositionEvent"in window,Vo=null;yn&&"documentMode"in document&&(Vo=document.documentMode);var pg=yn&&"TextEvent"in window&&!Vo,Tf=yn&&(!ku||Vo&&8<Vo&&11>=Vo),Uc=String.fromCharCode(32),$c=!1;function Nf(e,t){switch(e){case"keyup":return fg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function bf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var $r=!1;function hg(e,t){switch(e){case"compositionend":return bf(t);case"keypress":return t.which!==32?null:($c=!0,Uc);case"textInput":return e=t.data,e===Uc&&$c?null:e;default:return null}}function mg(e,t){if($r)return e==="compositionend"||!ku&&Nf(e,t)?(e=Ef(),pl=yu=zn=null,$r=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Tf&&t.locale!=="ko"?null:t.data;default:return null}}var gg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!gg[e.type]:t==="textarea"}function Pf(e,t,n,r){lf(r),t=Il(t,"onChange"),0<t.length&&(n=new vu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qo=null,oi=null;function yg(e){Uf(e,0)}function Xl(e){var t=Wr(e);if(Jd(t))return e}function vg(e,t){if(e==="change")return t}var If=!1;if(yn){var Ws;if(yn){var Vs="oninput"in document;if(!Vs){var Hc=document.createElement("div");Hc.setAttribute("oninput","return;"),Vs=typeof Hc.oninput=="function"}Ws=Vs}else Ws=!1;If=Ws&&(!document.documentMode||9<document.documentMode)}function Wc(){qo&&(qo.detachEvent("onpropertychange",Of),oi=qo=null)}function Of(e){if(e.propertyName==="value"&&Xl(oi)){var t=[];Pf(t,oi,e,fu(e)),cf(yg,t)}}function wg(e,t,n){e==="focusin"?(Wc(),qo=t,oi=n,qo.attachEvent("onpropertychange",Of)):e==="focusout"&&Wc()}function kg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Xl(oi)}function Cg(e,t){if(e==="click")return Xl(t)}function Sg(e,t){if(e==="input"||e==="change")return Xl(t)}function xg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qt=typeof Object.is=="function"?Object.is:xg;function ii(e,t){if(Qt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!aa.call(t,o)||!Qt(e[o],t[o]))return!1}return!0}function Vc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function qc(e,t){var n=Vc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Vc(n)}}function Lf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Lf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Mf(){for(var e=window,t=xl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=xl(e.document)}return t}function Cu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function _g(e){var t=Mf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Lf(n.ownerDocument.documentElement,n)){if(r!==null&&Cu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=qc(n,i);var a=qc(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Eg=yn&&"documentMode"in document&&11>=document.documentMode,Br=null,Na=null,Ko=null,ba=!1;function Kc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ba||Br==null||Br!==xl(r)||(r=Br,"selectionStart"in r&&Cu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ko&&ii(Ko,r)||(Ko=r,r=Il(Na,"onSelect"),0<r.length&&(t=new vu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Br)))}function el(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Hr={animationend:el("Animation","AnimationEnd"),animationiteration:el("Animation","AnimationIteration"),animationstart:el("Animation","AnimationStart"),transitionend:el("Transition","TransitionEnd")},qs={},Af={};yn&&(Af=document.createElement("div").style,"AnimationEvent"in window||(delete Hr.animationend.animation,delete Hr.animationiteration.animation,delete Hr.animationstart.animation),"TransitionEvent"in window||delete Hr.transitionend.transition);function Jl(e){if(qs[e])return qs[e];if(!Hr[e])return e;var t=Hr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Af)return qs[e]=t[n];return e}var jf=Jl("animationend"),Rf=Jl("animationiteration"),Df=Jl("animationstart"),zf=Jl("transitionend"),Ff=new Map,Qc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yn(e,t){Ff.set(e,t),Er(t,[e])}for(var Ks=0;Ks<Qc.length;Ks++){var Qs=Qc[Ks],Tg=Qs.toLowerCase(),Ng=Qs[0].toUpperCase()+Qs.slice(1);Yn(Tg,"on"+Ng)}Yn(jf,"onAnimationEnd");Yn(Rf,"onAnimationIteration");Yn(Df,"onAnimationStart");Yn("dblclick","onDoubleClick");Yn("focusin","onFocus");Yn("focusout","onBlur");Yn(zf,"onTransitionEnd");lo("onMouseEnter",["mouseout","mouseover"]);lo("onMouseLeave",["mouseout","mouseover"]);lo("onPointerEnter",["pointerout","pointerover"]);lo("onPointerLeave",["pointerout","pointerover"]);Er("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Er("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Er("onBeforeInput",["compositionend","keypress","textInput","paste"]);Er("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Er("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Er("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Bo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),bg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Bo));function Gc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Tm(r,t,void 0,e),e.currentTarget=null}function Uf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var u=r[a],d=u.instance,m=u.currentTarget;if(u=u.listener,d!==i&&o.isPropagationStopped())break e;Gc(o,u,m),i=d}else for(a=0;a<r.length;a++){if(u=r[a],d=u.instance,m=u.currentTarget,u=u.listener,d!==i&&o.isPropagationStopped())break e;Gc(o,u,m),i=d}}}if(El)throw e=xa,El=!1,xa=null,e}function be(e,t){var n=t[Ma];n===void 0&&(n=t[Ma]=new Set);var r=e+"__bubble";n.has(r)||($f(t,e,2,!1),n.add(r))}function Gs(e,t,n){var r=0;t&&(r|=4),$f(n,e,r,t)}var tl="_reactListening"+Math.random().toString(36).slice(2);function li(e){if(!e[tl]){e[tl]=!0,Kd.forEach(function(n){n!=="selectionchange"&&(bg.has(n)||Gs(n,!1,e),Gs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[tl]||(t[tl]=!0,Gs("selectionchange",!1,t))}}function $f(e,t,n,r){switch(_f(t)){case 1:var o=Bm;break;case 4:o=Hm;break;default:o=gu}n=o.bind(null,t,n,e),o=void 0,!Sa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ys(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var u=r.stateNode.containerInfo;if(u===o||u.nodeType===8&&u.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var d=a.tag;if((d===3||d===4)&&(d=a.stateNode.containerInfo,d===o||d.nodeType===8&&d.parentNode===o))return;a=a.return}for(;u!==null;){if(a=pr(u),a===null)return;if(d=a.tag,d===5||d===6){r=i=a;continue e}u=u.parentNode}}r=r.return}cf(function(){var m=i,w=fu(n),_=[];e:{var E=Ff.get(e);if(E!==void 0){var S=vu,C=e;switch(e){case"keypress":if(hl(n)===0)break e;case"keydown":case"keyup":S=og;break;case"focusin":C="focus",S=Hs;break;case"focusout":C="blur",S=Hs;break;case"beforeblur":case"afterblur":S=Hs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=Dc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=qm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=sg;break;case jf:case Rf:case Df:S=Gm;break;case zf:S=ug;break;case"scroll":S=Wm;break;case"wheel":S=dg;break;case"copy":case"cut":case"paste":S=Xm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=Fc}var T=(t&4)!==0,O=!T&&e==="scroll",g=T?E!==null?E+"Capture":null:E;T=[];for(var p=m,f;p!==null;){f=p;var I=f.stateNode;if(f.tag===5&&I!==null&&(f=I,g!==null&&(I=ei(p,g),I!=null&&T.push(si(p,I,f)))),O)break;p=p.return}0<T.length&&(E=new S(E,C,null,n,w),_.push({event:E,listeners:T}))}}if(!(t&7)){e:{if(E=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",E&&n!==ka&&(C=n.relatedTarget||n.fromElement)&&(pr(C)||C[vn]))break e;if((S||E)&&(E=w.window===w?w:(E=w.ownerDocument)?E.defaultView||E.parentWindow:window,S?(C=n.relatedTarget||n.toElement,S=m,C=C?pr(C):null,C!==null&&(O=Tr(C),C!==O||C.tag!==5&&C.tag!==6)&&(C=null)):(S=null,C=m),S!==C)){if(T=Dc,I="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(T=Fc,I="onPointerLeave",g="onPointerEnter",p="pointer"),O=S==null?E:Wr(S),f=C==null?E:Wr(C),E=new T(I,p+"leave",S,n,w),E.target=O,E.relatedTarget=f,I=null,pr(w)===m&&(T=new T(g,p+"enter",C,n,w),T.target=f,T.relatedTarget=O,I=T),O=I,S&&C)t:{for(T=S,g=C,p=0,f=T;f;f=zr(f))p++;for(f=0,I=g;I;I=zr(I))f++;for(;0<p-f;)T=zr(T),p--;for(;0<f-p;)g=zr(g),f--;for(;p--;){if(T===g||g!==null&&T===g.alternate)break t;T=zr(T),g=zr(g)}T=null}else T=null;S!==null&&Yc(_,E,S,T,!1),C!==null&&O!==null&&Yc(_,O,C,T,!0)}}e:{if(E=m?Wr(m):window,S=E.nodeName&&E.nodeName.toLowerCase(),S==="select"||S==="input"&&E.type==="file")var N=vg;else if(Bc(E))if(If)N=Sg;else{N=kg;var L=wg}else(S=E.nodeName)&&S.toLowerCase()==="input"&&(E.type==="checkbox"||E.type==="radio")&&(N=Cg);if(N&&(N=N(e,m))){Pf(_,N,n,w);break e}L&&L(e,E,m),e==="focusout"&&(L=E._wrapperState)&&L.controlled&&E.type==="number"&&ma(E,"number",E.value)}switch(L=m?Wr(m):window,e){case"focusin":(Bc(L)||L.contentEditable==="true")&&(Br=L,Na=m,Ko=null);break;case"focusout":Ko=Na=Br=null;break;case"mousedown":ba=!0;break;case"contextmenu":case"mouseup":case"dragend":ba=!1,Kc(_,n,w);break;case"selectionchange":if(Eg)break;case"keydown":case"keyup":Kc(_,n,w)}var h;if(ku)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else $r?Nf(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(Tf&&n.locale!=="ko"&&($r||k!=="onCompositionStart"?k==="onCompositionEnd"&&$r&&(h=Ef()):(zn=w,yu="value"in zn?zn.value:zn.textContent,$r=!0)),L=Il(m,k),0<L.length&&(k=new zc(k,e,null,n,w),_.push({event:k,listeners:L}),h?k.data=h:(h=bf(n),h!==null&&(k.data=h)))),(h=pg?hg(e,n):mg(e,n))&&(m=Il(m,"onBeforeInput"),0<m.length&&(w=new zc("onBeforeInput","beforeinput",null,n,w),_.push({event:w,listeners:m}),w.data=h))}Uf(_,t)})}function si(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Il(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=ei(e,n),i!=null&&r.unshift(si(e,i,o)),i=ei(e,t),i!=null&&r.push(si(e,i,o))),e=e.return}return r}function zr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Yc(e,t,n,r,o){for(var i=t._reactName,a=[];n!==null&&n!==r;){var u=n,d=u.alternate,m=u.stateNode;if(d!==null&&d===r)break;u.tag===5&&m!==null&&(u=m,o?(d=ei(n,i),d!=null&&a.unshift(si(n,d,u))):o||(d=ei(n,i),d!=null&&a.push(si(n,d,u)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Pg=/\r\n?/g,Ig=/\u0000|\uFFFD/g;function Xc(e){return(typeof e=="string"?e:""+e).replace(Pg,`
`).replace(Ig,"")}function nl(e,t,n){if(t=Xc(t),Xc(e)!==t&&n)throw Error($(425))}function Ol(){}var Pa=null,Ia=null;function Oa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var La=typeof setTimeout=="function"?setTimeout:void 0,Og=typeof clearTimeout=="function"?clearTimeout:void 0,Jc=typeof Promise=="function"?Promise:void 0,Lg=typeof queueMicrotask=="function"?queueMicrotask:typeof Jc<"u"?function(e){return Jc.resolve(null).then(e).catch(Mg)}:La;function Mg(e){setTimeout(function(){throw e})}function Xs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),ri(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ri(t)}function Hn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Zc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var go=Math.random().toString(36).slice(2),rn="__reactFiber$"+go,ai="__reactProps$"+go,vn="__reactContainer$"+go,Ma="__reactEvents$"+go,Ag="__reactListeners$"+go,jg="__reactHandles$"+go;function pr(e){var t=e[rn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[vn]||n[rn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Zc(e);e!==null;){if(n=e[rn])return n;e=Zc(e)}return t}e=n,n=e.parentNode}return null}function vi(e){return e=e[rn]||e[vn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Wr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error($(33))}function Zl(e){return e[ai]||null}var Aa=[],Vr=-1;function Xn(e){return{current:e}}function Pe(e){0>Vr||(e.current=Aa[Vr],Aa[Vr]=null,Vr--)}function Ne(e,t){Vr++,Aa[Vr]=e.current,e.current=t}var Gn={},it=Xn(Gn),gt=Xn(!1),kr=Gn;function so(e,t){var n=e.type.contextTypes;if(!n)return Gn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function yt(e){return e=e.childContextTypes,e!=null}function Ll(){Pe(gt),Pe(it)}function ed(e,t,n){if(it.current!==Gn)throw Error($(168));Ne(it,t),Ne(gt,n)}function Bf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error($(108,wm(e)||"Unknown",o));return je({},n,r)}function Ml(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gn,kr=it.current,Ne(it,e),Ne(gt,gt.current),!0}function td(e,t,n){var r=e.stateNode;if(!r)throw Error($(169));n?(e=Bf(e,t,kr),r.__reactInternalMemoizedMergedChildContext=e,Pe(gt),Pe(it),Ne(it,e)):Pe(gt),Ne(gt,n)}var pn=null,es=!1,Js=!1;function Hf(e){pn===null?pn=[e]:pn.push(e)}function Rg(e){es=!0,Hf(e)}function Jn(){if(!Js&&pn!==null){Js=!0;var e=0,t=xe;try{var n=pn;for(xe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}pn=null,es=!1}catch(o){throw pn!==null&&(pn=pn.slice(e+1)),hf(pu,Jn),o}finally{xe=t,Js=!1}}return null}var qr=[],Kr=0,Al=null,jl=0,bt=[],Pt=0,Cr=null,hn=1,mn="";function dr(e,t){qr[Kr++]=jl,qr[Kr++]=Al,Al=e,jl=t}function Wf(e,t,n){bt[Pt++]=hn,bt[Pt++]=mn,bt[Pt++]=Cr,Cr=e;var r=hn;e=mn;var o=32-qt(r)-1;r&=~(1<<o),n+=1;var i=32-qt(t)+o;if(30<i){var a=o-o%5;i=(r&(1<<a)-1).toString(32),r>>=a,o-=a,hn=1<<32-qt(t)+o|n<<o|r,mn=i+e}else hn=1<<i|n<<o|r,mn=e}function Su(e){e.return!==null&&(dr(e,1),Wf(e,1,0))}function xu(e){for(;e===Al;)Al=qr[--Kr],qr[Kr]=null,jl=qr[--Kr],qr[Kr]=null;for(;e===Cr;)Cr=bt[--Pt],bt[Pt]=null,mn=bt[--Pt],bt[Pt]=null,hn=bt[--Pt],bt[Pt]=null}var xt=null,St=null,Oe=!1,Vt=null;function Vf(e,t){var n=It(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function nd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xt=e,St=Hn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xt=e,St=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Cr!==null?{id:hn,overflow:mn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=It(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,xt=e,St=null,!0):!1;default:return!1}}function ja(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ra(e){if(Oe){var t=St;if(t){var n=t;if(!nd(e,t)){if(ja(e))throw Error($(418));t=Hn(n.nextSibling);var r=xt;t&&nd(e,t)?Vf(r,n):(e.flags=e.flags&-4097|2,Oe=!1,xt=e)}}else{if(ja(e))throw Error($(418));e.flags=e.flags&-4097|2,Oe=!1,xt=e}}}function rd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xt=e}function rl(e){if(e!==xt)return!1;if(!Oe)return rd(e),Oe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Oa(e.type,e.memoizedProps)),t&&(t=St)){if(ja(e))throw qf(),Error($(418));for(;t;)Vf(e,t),t=Hn(t.nextSibling)}if(rd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error($(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){St=Hn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}St=null}}else St=xt?Hn(e.stateNode.nextSibling):null;return!0}function qf(){for(var e=St;e;)e=Hn(e.nextSibling)}function ao(){St=xt=null,Oe=!1}function _u(e){Vt===null?Vt=[e]:Vt.push(e)}var Dg=Cn.ReactCurrentBatchConfig;function jo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error($(309));var r=n.stateNode}if(!r)throw Error($(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var u=o.refs;a===null?delete u[i]:u[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error($(284));if(!n._owner)throw Error($(290,e))}return e}function ol(e,t){throw e=Object.prototype.toString.call(t),Error($(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function od(e){var t=e._init;return t(e._payload)}function Kf(e){function t(g,p){if(e){var f=g.deletions;f===null?(g.deletions=[p],g.flags|=16):f.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function o(g,p){return g=Kn(g,p),g.index=0,g.sibling=null,g}function i(g,p,f){return g.index=f,e?(f=g.alternate,f!==null?(f=f.index,f<p?(g.flags|=2,p):f):(g.flags|=2,p)):(g.flags|=1048576,p)}function a(g){return e&&g.alternate===null&&(g.flags|=2),g}function u(g,p,f,I){return p===null||p.tag!==6?(p=ia(f,g.mode,I),p.return=g,p):(p=o(p,f),p.return=g,p)}function d(g,p,f,I){var N=f.type;return N===Ur?w(g,p,f.props.children,I,f.key):p!==null&&(p.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===An&&od(N)===p.type)?(I=o(p,f.props),I.ref=jo(g,p,f),I.return=g,I):(I=Cl(f.type,f.key,f.props,null,g.mode,I),I.ref=jo(g,p,f),I.return=g,I)}function m(g,p,f,I){return p===null||p.tag!==4||p.stateNode.containerInfo!==f.containerInfo||p.stateNode.implementation!==f.implementation?(p=la(f,g.mode,I),p.return=g,p):(p=o(p,f.children||[]),p.return=g,p)}function w(g,p,f,I,N){return p===null||p.tag!==7?(p=vr(f,g.mode,I,N),p.return=g,p):(p=o(p,f),p.return=g,p)}function _(g,p,f){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ia(""+p,g.mode,f),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Ki:return f=Cl(p.type,p.key,p.props,null,g.mode,f),f.ref=jo(g,null,p),f.return=g,f;case Fr:return p=la(p,g.mode,f),p.return=g,p;case An:var I=p._init;return _(g,I(p._payload),f)}if(Uo(p)||Io(p))return p=vr(p,g.mode,f,null),p.return=g,p;ol(g,p)}return null}function E(g,p,f,I){var N=p!==null?p.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return N!==null?null:u(g,p,""+f,I);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Ki:return f.key===N?d(g,p,f,I):null;case Fr:return f.key===N?m(g,p,f,I):null;case An:return N=f._init,E(g,p,N(f._payload),I)}if(Uo(f)||Io(f))return N!==null?null:w(g,p,f,I,null);ol(g,f)}return null}function S(g,p,f,I,N){if(typeof I=="string"&&I!==""||typeof I=="number")return g=g.get(f)||null,u(p,g,""+I,N);if(typeof I=="object"&&I!==null){switch(I.$$typeof){case Ki:return g=g.get(I.key===null?f:I.key)||null,d(p,g,I,N);case Fr:return g=g.get(I.key===null?f:I.key)||null,m(p,g,I,N);case An:var L=I._init;return S(g,p,f,L(I._payload),N)}if(Uo(I)||Io(I))return g=g.get(f)||null,w(p,g,I,N,null);ol(p,I)}return null}function C(g,p,f,I){for(var N=null,L=null,h=p,k=p=0,R=null;h!==null&&k<f.length;k++){h.index>k?(R=h,h=null):R=h.sibling;var M=E(g,h,f[k],I);if(M===null){h===null&&(h=R);break}e&&h&&M.alternate===null&&t(g,h),p=i(M,p,k),L===null?N=M:L.sibling=M,L=M,h=R}if(k===f.length)return n(g,h),Oe&&dr(g,k),N;if(h===null){for(;k<f.length;k++)h=_(g,f[k],I),h!==null&&(p=i(h,p,k),L===null?N=h:L.sibling=h,L=h);return Oe&&dr(g,k),N}for(h=r(g,h);k<f.length;k++)R=S(h,g,k,f[k],I),R!==null&&(e&&R.alternate!==null&&h.delete(R.key===null?k:R.key),p=i(R,p,k),L===null?N=R:L.sibling=R,L=R);return e&&h.forEach(function(G){return t(g,G)}),Oe&&dr(g,k),N}function T(g,p,f,I){var N=Io(f);if(typeof N!="function")throw Error($(150));if(f=N.call(f),f==null)throw Error($(151));for(var L=N=null,h=p,k=p=0,R=null,M=f.next();h!==null&&!M.done;k++,M=f.next()){h.index>k?(R=h,h=null):R=h.sibling;var G=E(g,h,M.value,I);if(G===null){h===null&&(h=R);break}e&&h&&G.alternate===null&&t(g,h),p=i(G,p,k),L===null?N=G:L.sibling=G,L=G,h=R}if(M.done)return n(g,h),Oe&&dr(g,k),N;if(h===null){for(;!M.done;k++,M=f.next())M=_(g,M.value,I),M!==null&&(p=i(M,p,k),L===null?N=M:L.sibling=M,L=M);return Oe&&dr(g,k),N}for(h=r(g,h);!M.done;k++,M=f.next())M=S(h,g,k,M.value,I),M!==null&&(e&&M.alternate!==null&&h.delete(M.key===null?k:M.key),p=i(M,p,k),L===null?N=M:L.sibling=M,L=M);return e&&h.forEach(function(ce){return t(g,ce)}),Oe&&dr(g,k),N}function O(g,p,f,I){if(typeof f=="object"&&f!==null&&f.type===Ur&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Ki:e:{for(var N=f.key,L=p;L!==null;){if(L.key===N){if(N=f.type,N===Ur){if(L.tag===7){n(g,L.sibling),p=o(L,f.props.children),p.return=g,g=p;break e}}else if(L.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===An&&od(N)===L.type){n(g,L.sibling),p=o(L,f.props),p.ref=jo(g,L,f),p.return=g,g=p;break e}n(g,L);break}else t(g,L);L=L.sibling}f.type===Ur?(p=vr(f.props.children,g.mode,I,f.key),p.return=g,g=p):(I=Cl(f.type,f.key,f.props,null,g.mode,I),I.ref=jo(g,p,f),I.return=g,g=I)}return a(g);case Fr:e:{for(L=f.key;p!==null;){if(p.key===L)if(p.tag===4&&p.stateNode.containerInfo===f.containerInfo&&p.stateNode.implementation===f.implementation){n(g,p.sibling),p=o(p,f.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=la(f,g.mode,I),p.return=g,g=p}return a(g);case An:return L=f._init,O(g,p,L(f._payload),I)}if(Uo(f))return C(g,p,f,I);if(Io(f))return T(g,p,f,I);ol(g,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,p!==null&&p.tag===6?(n(g,p.sibling),p=o(p,f),p.return=g,g=p):(n(g,p),p=ia(f,g.mode,I),p.return=g,g=p),a(g)):n(g,p)}return O}var uo=Kf(!0),Qf=Kf(!1),Rl=Xn(null),Dl=null,Qr=null,Eu=null;function Tu(){Eu=Qr=Dl=null}function Nu(e){var t=Rl.current;Pe(Rl),e._currentValue=t}function Da(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ro(e,t){Dl=e,Eu=Qr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(mt=!0),e.firstContext=null)}function Lt(e){var t=e._currentValue;if(Eu!==e)if(e={context:e,memoizedValue:t,next:null},Qr===null){if(Dl===null)throw Error($(308));Qr=e,Dl.dependencies={lanes:0,firstContext:e}}else Qr=Qr.next=e;return t}var hr=null;function bu(e){hr===null?hr=[e]:hr.push(e)}function Gf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,bu(t)):(n.next=o.next,o.next=n),t.interleaved=n,wn(e,r)}function wn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var jn=!1;function Pu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Yf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function gn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Wn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ye&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,wn(e,n)}return o=r.interleaved,o===null?(t.next=t,bu(r)):(t.next=o.next,o.next=t),r.interleaved=t,wn(e,n)}function ml(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hu(e,n)}}function id(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function zl(e,t,n,r){var o=e.updateQueue;jn=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,u=o.shared.pending;if(u!==null){o.shared.pending=null;var d=u,m=d.next;d.next=null,a===null?i=m:a.next=m,a=d;var w=e.alternate;w!==null&&(w=w.updateQueue,u=w.lastBaseUpdate,u!==a&&(u===null?w.firstBaseUpdate=m:u.next=m,w.lastBaseUpdate=d))}if(i!==null){var _=o.baseState;a=0,w=m=d=null,u=i;do{var E=u.lane,S=u.eventTime;if((r&E)===E){w!==null&&(w=w.next={eventTime:S,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var C=e,T=u;switch(E=t,S=n,T.tag){case 1:if(C=T.payload,typeof C=="function"){_=C.call(S,_,E);break e}_=C;break e;case 3:C.flags=C.flags&-65537|128;case 0:if(C=T.payload,E=typeof C=="function"?C.call(S,_,E):C,E==null)break e;_=je({},_,E);break e;case 2:jn=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,E=o.effects,E===null?o.effects=[u]:E.push(u))}else S={eventTime:S,lane:E,tag:u.tag,payload:u.payload,callback:u.callback,next:null},w===null?(m=w=S,d=_):w=w.next=S,a|=E;if(u=u.next,u===null){if(u=o.shared.pending,u===null)break;E=u,u=E.next,E.next=null,o.lastBaseUpdate=E,o.shared.pending=null}}while(1);if(w===null&&(d=_),o.baseState=d,o.firstBaseUpdate=m,o.lastBaseUpdate=w,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);xr|=a,e.lanes=a,e.memoizedState=_}}function ld(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error($(191,o));o.call(r)}}}var wi={},ln=Xn(wi),ui=Xn(wi),ci=Xn(wi);function mr(e){if(e===wi)throw Error($(174));return e}function Iu(e,t){switch(Ne(ci,t),Ne(ui,e),Ne(ln,wi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ya(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ya(t,e)}Pe(ln),Ne(ln,t)}function co(){Pe(ln),Pe(ui),Pe(ci)}function Xf(e){mr(ci.current);var t=mr(ln.current),n=ya(t,e.type);t!==n&&(Ne(ui,e),Ne(ln,n))}function Ou(e){ui.current===e&&(Pe(ln),Pe(ui))}var Me=Xn(0);function Fl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Zs=[];function Lu(){for(var e=0;e<Zs.length;e++)Zs[e]._workInProgressVersionPrimary=null;Zs.length=0}var gl=Cn.ReactCurrentDispatcher,ea=Cn.ReactCurrentBatchConfig,Sr=0,Ae=null,Ke=null,Ye=null,Ul=!1,Qo=!1,di=0,zg=0;function nt(){throw Error($(321))}function Mu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qt(e[n],t[n]))return!1;return!0}function Au(e,t,n,r,o,i){if(Sr=i,Ae=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,gl.current=e===null||e.memoizedState===null?Bg:Hg,e=n(r,o),Qo){i=0;do{if(Qo=!1,di=0,25<=i)throw Error($(301));i+=1,Ye=Ke=null,t.updateQueue=null,gl.current=Wg,e=n(r,o)}while(Qo)}if(gl.current=$l,t=Ke!==null&&Ke.next!==null,Sr=0,Ye=Ke=Ae=null,Ul=!1,t)throw Error($(300));return e}function ju(){var e=di!==0;return di=0,e}function tn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ye===null?Ae.memoizedState=Ye=e:Ye=Ye.next=e,Ye}function Mt(){if(Ke===null){var e=Ae.alternate;e=e!==null?e.memoizedState:null}else e=Ke.next;var t=Ye===null?Ae.memoizedState:Ye.next;if(t!==null)Ye=t,Ke=e;else{if(e===null)throw Error($(310));Ke=e,e={memoizedState:Ke.memoizedState,baseState:Ke.baseState,baseQueue:Ke.baseQueue,queue:Ke.queue,next:null},Ye===null?Ae.memoizedState=Ye=e:Ye=Ye.next=e}return Ye}function fi(e,t){return typeof t=="function"?t(e):t}function ta(e){var t=Mt(),n=t.queue;if(n===null)throw Error($(311));n.lastRenderedReducer=e;var r=Ke,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var a=o.next;o.next=i.next,i.next=a}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var u=a=null,d=null,m=i;do{var w=m.lane;if((Sr&w)===w)d!==null&&(d=d.next={lane:0,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null}),r=m.hasEagerState?m.eagerState:e(r,m.action);else{var _={lane:w,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null};d===null?(u=d=_,a=r):d=d.next=_,Ae.lanes|=w,xr|=w}m=m.next}while(m!==null&&m!==i);d===null?a=r:d.next=u,Qt(r,t.memoizedState)||(mt=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=d,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,Ae.lanes|=i,xr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function na(e){var t=Mt(),n=t.queue;if(n===null)throw Error($(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do i=e(i,a.action),a=a.next;while(a!==o);Qt(i,t.memoizedState)||(mt=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Jf(){}function Zf(e,t){var n=Ae,r=Mt(),o=t(),i=!Qt(r.memoizedState,o);if(i&&(r.memoizedState=o,mt=!0),r=r.queue,Ru(np.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ye!==null&&Ye.memoizedState.tag&1){if(n.flags|=2048,pi(9,tp.bind(null,n,r,o,t),void 0,null),Xe===null)throw Error($(349));Sr&30||ep(n,t,o)}return o}function ep(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ae.updateQueue,t===null?(t={lastEffect:null,stores:null},Ae.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function tp(e,t,n,r){t.value=n,t.getSnapshot=r,rp(t)&&op(e)}function np(e,t,n){return n(function(){rp(t)&&op(e)})}function rp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qt(e,n)}catch{return!0}}function op(e){var t=wn(e,1);t!==null&&Kt(t,e,1,-1)}function sd(e){var t=tn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:fi,lastRenderedState:e},t.queue=e,e=e.dispatch=$g.bind(null,Ae,e),[t.memoizedState,e]}function pi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Ae.updateQueue,t===null?(t={lastEffect:null,stores:null},Ae.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ip(){return Mt().memoizedState}function yl(e,t,n,r){var o=tn();Ae.flags|=e,o.memoizedState=pi(1|t,n,void 0,r===void 0?null:r)}function ts(e,t,n,r){var o=Mt();r=r===void 0?null:r;var i=void 0;if(Ke!==null){var a=Ke.memoizedState;if(i=a.destroy,r!==null&&Mu(r,a.deps)){o.memoizedState=pi(t,n,i,r);return}}Ae.flags|=e,o.memoizedState=pi(1|t,n,i,r)}function ad(e,t){return yl(8390656,8,e,t)}function Ru(e,t){return ts(2048,8,e,t)}function lp(e,t){return ts(4,2,e,t)}function sp(e,t){return ts(4,4,e,t)}function ap(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function up(e,t,n){return n=n!=null?n.concat([e]):null,ts(4,4,ap.bind(null,t,e),n)}function Du(){}function cp(e,t){var n=Mt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Mu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function dp(e,t){var n=Mt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Mu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function fp(e,t,n){return Sr&21?(Qt(n,t)||(n=yf(),Ae.lanes|=n,xr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,mt=!0),e.memoizedState=n)}function Fg(e,t){var n=xe;xe=n!==0&&4>n?n:4,e(!0);var r=ea.transition;ea.transition={};try{e(!1),t()}finally{xe=n,ea.transition=r}}function pp(){return Mt().memoizedState}function Ug(e,t,n){var r=qn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},hp(e))mp(t,n);else if(n=Gf(e,t,n,r),n!==null){var o=at();Kt(n,e,r,o),gp(n,t,r)}}function $g(e,t,n){var r=qn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(hp(e))mp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,u=i(a,n);if(o.hasEagerState=!0,o.eagerState=u,Qt(u,a)){var d=t.interleaved;d===null?(o.next=o,bu(t)):(o.next=d.next,d.next=o),t.interleaved=o;return}}catch{}finally{}n=Gf(e,t,o,r),n!==null&&(o=at(),Kt(n,e,r,o),gp(n,t,r))}}function hp(e){var t=e.alternate;return e===Ae||t!==null&&t===Ae}function mp(e,t){Qo=Ul=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function gp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hu(e,n)}}var $l={readContext:Lt,useCallback:nt,useContext:nt,useEffect:nt,useImperativeHandle:nt,useInsertionEffect:nt,useLayoutEffect:nt,useMemo:nt,useReducer:nt,useRef:nt,useState:nt,useDebugValue:nt,useDeferredValue:nt,useTransition:nt,useMutableSource:nt,useSyncExternalStore:nt,useId:nt,unstable_isNewReconciler:!1},Bg={readContext:Lt,useCallback:function(e,t){return tn().memoizedState=[e,t===void 0?null:t],e},useContext:Lt,useEffect:ad,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,yl(4194308,4,ap.bind(null,t,e),n)},useLayoutEffect:function(e,t){return yl(4194308,4,e,t)},useInsertionEffect:function(e,t){return yl(4,2,e,t)},useMemo:function(e,t){var n=tn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ug.bind(null,Ae,e),[r.memoizedState,e]},useRef:function(e){var t=tn();return e={current:e},t.memoizedState=e},useState:sd,useDebugValue:Du,useDeferredValue:function(e){return tn().memoizedState=e},useTransition:function(){var e=sd(!1),t=e[0];return e=Fg.bind(null,e[1]),tn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Ae,o=tn();if(Oe){if(n===void 0)throw Error($(407));n=n()}else{if(n=t(),Xe===null)throw Error($(349));Sr&30||ep(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,ad(np.bind(null,r,i,e),[e]),r.flags|=2048,pi(9,tp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=tn(),t=Xe.identifierPrefix;if(Oe){var n=mn,r=hn;n=(r&~(1<<32-qt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=di++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=zg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Hg={readContext:Lt,useCallback:cp,useContext:Lt,useEffect:Ru,useImperativeHandle:up,useInsertionEffect:lp,useLayoutEffect:sp,useMemo:dp,useReducer:ta,useRef:ip,useState:function(){return ta(fi)},useDebugValue:Du,useDeferredValue:function(e){var t=Mt();return fp(t,Ke.memoizedState,e)},useTransition:function(){var e=ta(fi)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:Jf,useSyncExternalStore:Zf,useId:pp,unstable_isNewReconciler:!1},Wg={readContext:Lt,useCallback:cp,useContext:Lt,useEffect:Ru,useImperativeHandle:up,useInsertionEffect:lp,useLayoutEffect:sp,useMemo:dp,useReducer:na,useRef:ip,useState:function(){return na(fi)},useDebugValue:Du,useDeferredValue:function(e){var t=Mt();return Ke===null?t.memoizedState=e:fp(t,Ke.memoizedState,e)},useTransition:function(){var e=na(fi)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:Jf,useSyncExternalStore:Zf,useId:pp,unstable_isNewReconciler:!1};function Ht(e,t){if(e&&e.defaultProps){t=je({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function za(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:je({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ns={isMounted:function(e){return(e=e._reactInternals)?Tr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=at(),o=qn(e),i=gn(r,o);i.payload=t,n!=null&&(i.callback=n),t=Wn(e,i,o),t!==null&&(Kt(t,e,o,r),ml(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=at(),o=qn(e),i=gn(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Wn(e,i,o),t!==null&&(Kt(t,e,o,r),ml(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=at(),r=qn(e),o=gn(n,r);o.tag=2,t!=null&&(o.callback=t),t=Wn(e,o,r),t!==null&&(Kt(t,e,r,n),ml(t,e,r))}};function ud(e,t,n,r,o,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!ii(n,r)||!ii(o,i):!0}function yp(e,t,n){var r=!1,o=Gn,i=t.contextType;return typeof i=="object"&&i!==null?i=Lt(i):(o=yt(t)?kr:it.current,r=t.contextTypes,i=(r=r!=null)?so(e,o):Gn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ns,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function cd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ns.enqueueReplaceState(t,t.state,null)}function Fa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Pu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Lt(i):(i=yt(t)?kr:it.current,o.context=so(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(za(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ns.enqueueReplaceState(o,o.state,null),zl(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function fo(e,t){try{var n="",r=t;do n+=vm(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ra(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ua(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Vg=typeof WeakMap=="function"?WeakMap:Map;function vp(e,t,n){n=gn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hl||(Hl=!0,Ya=r),Ua(e,t)},n}function wp(e,t,n){n=gn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ua(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ua(e,t),typeof r!="function"&&(Vn===null?Vn=new Set([this]):Vn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function dd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Vg;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=iy.bind(null,e,t,n),t.then(e,e))}function fd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function pd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=gn(-1,1),t.tag=2,Wn(n,t,1))),n.lanes|=1),e)}var qg=Cn.ReactCurrentOwner,mt=!1;function st(e,t,n,r){t.child=e===null?Qf(t,null,n,r):uo(t,e.child,n,r)}function hd(e,t,n,r,o){n=n.render;var i=t.ref;return ro(t,o),r=Au(e,t,n,r,i,o),n=ju(),e!==null&&!mt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,kn(e,t,o)):(Oe&&n&&Su(t),t.flags|=1,st(e,t,r,o),t.child)}function md(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Vu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,kp(e,t,i,r,o)):(e=Cl(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:ii,n(a,r)&&e.ref===t.ref)return kn(e,t,o)}return t.flags|=1,e=Kn(i,r),e.ref=t.ref,e.return=t,t.child=e}function kp(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ii(i,r)&&e.ref===t.ref)if(mt=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(mt=!0);else return t.lanes=e.lanes,kn(e,t,o)}return $a(e,t,n,r,o)}function Cp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ne(Yr,Ct),Ct|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ne(Yr,Ct),Ct|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Ne(Yr,Ct),Ct|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Ne(Yr,Ct),Ct|=r;return st(e,t,o,n),t.child}function Sp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function $a(e,t,n,r,o){var i=yt(n)?kr:it.current;return i=so(t,i),ro(t,o),n=Au(e,t,n,r,i,o),r=ju(),e!==null&&!mt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,kn(e,t,o)):(Oe&&r&&Su(t),t.flags|=1,st(e,t,n,o),t.child)}function gd(e,t,n,r,o){if(yt(n)){var i=!0;Ml(t)}else i=!1;if(ro(t,o),t.stateNode===null)vl(e,t),yp(t,n,r),Fa(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,u=t.memoizedProps;a.props=u;var d=a.context,m=n.contextType;typeof m=="object"&&m!==null?m=Lt(m):(m=yt(n)?kr:it.current,m=so(t,m));var w=n.getDerivedStateFromProps,_=typeof w=="function"||typeof a.getSnapshotBeforeUpdate=="function";_||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(u!==r||d!==m)&&cd(t,a,r,m),jn=!1;var E=t.memoizedState;a.state=E,zl(t,r,a,o),d=t.memoizedState,u!==r||E!==d||gt.current||jn?(typeof w=="function"&&(za(t,n,w,r),d=t.memoizedState),(u=jn||ud(t,n,u,r,E,d,m))?(_||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=d),a.props=r,a.state=d,a.context=m,r=u):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Yf(e,t),u=t.memoizedProps,m=t.type===t.elementType?u:Ht(t.type,u),a.props=m,_=t.pendingProps,E=a.context,d=n.contextType,typeof d=="object"&&d!==null?d=Lt(d):(d=yt(n)?kr:it.current,d=so(t,d));var S=n.getDerivedStateFromProps;(w=typeof S=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(u!==_||E!==d)&&cd(t,a,r,d),jn=!1,E=t.memoizedState,a.state=E,zl(t,r,a,o);var C=t.memoizedState;u!==_||E!==C||gt.current||jn?(typeof S=="function"&&(za(t,n,S,r),C=t.memoizedState),(m=jn||ud(t,n,m,r,E,C,d)||!1)?(w||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,C,d),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,C,d)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||u===e.memoizedProps&&E===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&E===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=C),a.props=r,a.state=C,a.context=d,r=m):(typeof a.componentDidUpdate!="function"||u===e.memoizedProps&&E===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&E===e.memoizedState||(t.flags|=1024),r=!1)}return Ba(e,t,n,r,i,o)}function Ba(e,t,n,r,o,i){Sp(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&td(t,n,!1),kn(e,t,i);r=t.stateNode,qg.current=t;var u=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=uo(t,e.child,null,i),t.child=uo(t,null,u,i)):st(e,t,u,i),t.memoizedState=r.state,o&&td(t,n,!0),t.child}function xp(e){var t=e.stateNode;t.pendingContext?ed(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ed(e,t.context,!1),Iu(e,t.containerInfo)}function yd(e,t,n,r,o){return ao(),_u(o),t.flags|=256,st(e,t,n,r),t.child}var Ha={dehydrated:null,treeContext:null,retryLane:0};function Wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function _p(e,t,n){var r=t.pendingProps,o=Me.current,i=!1,a=(t.flags&128)!==0,u;if((u=a)||(u=e!==null&&e.memoizedState===null?!1:(o&2)!==0),u?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Ne(Me,o&1),e===null)return Ra(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=is(a,r,0,null),e=vr(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Wa(n),t.memoizedState=Ha,e):zu(t,a));if(o=e.memoizedState,o!==null&&(u=o.dehydrated,u!==null))return Kg(e,t,a,r,u,o,n);if(i){i=r.fallback,a=t.mode,o=e.child,u=o.sibling;var d={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=d,t.deletions=null):(r=Kn(o,d),r.subtreeFlags=o.subtreeFlags&14680064),u!==null?i=Kn(u,i):(i=vr(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?Wa(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=Ha,r}return i=e.child,e=i.sibling,r=Kn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function zu(e,t){return t=is({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function il(e,t,n,r){return r!==null&&_u(r),uo(t,e.child,null,n),e=zu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Kg(e,t,n,r,o,i,a){if(n)return t.flags&256?(t.flags&=-257,r=ra(Error($(422))),il(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=is({mode:"visible",children:r.children},o,0,null),i=vr(i,o,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&uo(t,e.child,null,a),t.child.memoizedState=Wa(a),t.memoizedState=Ha,i);if(!(t.mode&1))return il(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var u=r.dgst;return r=u,i=Error($(419)),r=ra(i,r,void 0),il(e,t,a,r)}if(u=(a&e.childLanes)!==0,mt||u){if(r=Xe,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,wn(e,o),Kt(r,e,o,-1))}return Wu(),r=ra(Error($(421))),il(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=ly.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,St=Hn(o.nextSibling),xt=t,Oe=!0,Vt=null,e!==null&&(bt[Pt++]=hn,bt[Pt++]=mn,bt[Pt++]=Cr,hn=e.id,mn=e.overflow,Cr=t),t=zu(t,r.children),t.flags|=4096,t)}function vd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Da(e.return,t,n)}function oa(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Ep(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(st(e,t,r.children,n),r=Me.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&vd(e,n,t);else if(e.tag===19)vd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ne(Me,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Fl(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),oa(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Fl(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}oa(t,!0,n,null,i);break;case"together":oa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function vl(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function kn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),xr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error($(153));if(t.child!==null){for(e=t.child,n=Kn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Kn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Qg(e,t,n){switch(t.tag){case 3:xp(t),ao();break;case 5:Xf(t);break;case 1:yt(t.type)&&Ml(t);break;case 4:Iu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Ne(Rl,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Ne(Me,Me.current&1),t.flags|=128,null):n&t.child.childLanes?_p(e,t,n):(Ne(Me,Me.current&1),e=kn(e,t,n),e!==null?e.sibling:null);Ne(Me,Me.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ep(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Ne(Me,Me.current),r)break;return null;case 22:case 23:return t.lanes=0,Cp(e,t,n)}return kn(e,t,n)}var Tp,Va,Np,bp;Tp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Va=function(){};Np=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,mr(ln.current);var i=null;switch(n){case"input":o=pa(e,o),r=pa(e,r),i=[];break;case"select":o=je({},o,{value:void 0}),r=je({},r,{value:void 0}),i=[];break;case"textarea":o=ga(e,o),r=ga(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ol)}va(n,r);var a;n=null;for(m in o)if(!r.hasOwnProperty(m)&&o.hasOwnProperty(m)&&o[m]!=null)if(m==="style"){var u=o[m];for(a in u)u.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else m!=="dangerouslySetInnerHTML"&&m!=="children"&&m!=="suppressContentEditableWarning"&&m!=="suppressHydrationWarning"&&m!=="autoFocus"&&(Jo.hasOwnProperty(m)?i||(i=[]):(i=i||[]).push(m,null));for(m in r){var d=r[m];if(u=o?.[m],r.hasOwnProperty(m)&&d!==u&&(d!=null||u!=null))if(m==="style")if(u){for(a in u)!u.hasOwnProperty(a)||d&&d.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in d)d.hasOwnProperty(a)&&u[a]!==d[a]&&(n||(n={}),n[a]=d[a])}else n||(i||(i=[]),i.push(m,n)),n=d;else m==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,u=u?u.__html:void 0,d!=null&&u!==d&&(i=i||[]).push(m,d)):m==="children"?typeof d!="string"&&typeof d!="number"||(i=i||[]).push(m,""+d):m!=="suppressContentEditableWarning"&&m!=="suppressHydrationWarning"&&(Jo.hasOwnProperty(m)?(d!=null&&m==="onScroll"&&be("scroll",e),i||u===d||(i=[])):(i=i||[]).push(m,d))}n&&(i=i||[]).push("style",n);var m=i;(t.updateQueue=m)&&(t.flags|=4)}};bp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Ro(e,t){if(!Oe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function rt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Gg(e,t,n){var r=t.pendingProps;switch(xu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rt(t),null;case 1:return yt(t.type)&&Ll(),rt(t),null;case 3:return r=t.stateNode,co(),Pe(gt),Pe(it),Lu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(rl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Vt!==null&&(Za(Vt),Vt=null))),Va(e,t),rt(t),null;case 5:Ou(t);var o=mr(ci.current);if(n=t.type,e!==null&&t.stateNode!=null)Np(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error($(166));return rt(t),null}if(e=mr(ln.current),rl(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[rn]=t,r[ai]=i,e=(t.mode&1)!==0,n){case"dialog":be("cancel",r),be("close",r);break;case"iframe":case"object":case"embed":be("load",r);break;case"video":case"audio":for(o=0;o<Bo.length;o++)be(Bo[o],r);break;case"source":be("error",r);break;case"img":case"image":case"link":be("error",r),be("load",r);break;case"details":be("toggle",r);break;case"input":Nc(r,i),be("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},be("invalid",r);break;case"textarea":Pc(r,i),be("invalid",r)}va(n,i),o=null;for(var a in i)if(i.hasOwnProperty(a)){var u=i[a];a==="children"?typeof u=="string"?r.textContent!==u&&(i.suppressHydrationWarning!==!0&&nl(r.textContent,u,e),o=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(i.suppressHydrationWarning!==!0&&nl(r.textContent,u,e),o=["children",""+u]):Jo.hasOwnProperty(a)&&u!=null&&a==="onScroll"&&be("scroll",r)}switch(n){case"input":Qi(r),bc(r,i,!0);break;case"textarea":Qi(r),Ic(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Ol)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=tf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[rn]=t,e[ai]=r,Tp(e,t,!1,!1),t.stateNode=e;e:{switch(a=wa(n,r),n){case"dialog":be("cancel",e),be("close",e),o=r;break;case"iframe":case"object":case"embed":be("load",e),o=r;break;case"video":case"audio":for(o=0;o<Bo.length;o++)be(Bo[o],e);o=r;break;case"source":be("error",e),o=r;break;case"img":case"image":case"link":be("error",e),be("load",e),o=r;break;case"details":be("toggle",e),o=r;break;case"input":Nc(e,r),o=pa(e,r),be("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=je({},r,{value:void 0}),be("invalid",e);break;case"textarea":Pc(e,r),o=ga(e,r),be("invalid",e);break;default:o=r}va(n,o),u=o;for(i in u)if(u.hasOwnProperty(i)){var d=u[i];i==="style"?of(e,d):i==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,d!=null&&nf(e,d)):i==="children"?typeof d=="string"?(n!=="textarea"||d!=="")&&Zo(e,d):typeof d=="number"&&Zo(e,""+d):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Jo.hasOwnProperty(i)?d!=null&&i==="onScroll"&&be("scroll",e):d!=null&&au(e,i,d,a))}switch(n){case"input":Qi(e),bc(e,r,!1);break;case"textarea":Qi(e),Ic(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Qn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Zr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Zr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ol)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return rt(t),null;case 6:if(e&&t.stateNode!=null)bp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error($(166));if(n=mr(ci.current),mr(ln.current),rl(t)){if(r=t.stateNode,n=t.memoizedProps,r[rn]=t,(i=r.nodeValue!==n)&&(e=xt,e!==null))switch(e.tag){case 3:nl(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&nl(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[rn]=t,t.stateNode=r}return rt(t),null;case 13:if(Pe(Me),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Oe&&St!==null&&t.mode&1&&!(t.flags&128))qf(),ao(),t.flags|=98560,i=!1;else if(i=rl(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error($(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error($(317));i[rn]=t}else ao(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;rt(t),i=!1}else Vt!==null&&(Za(Vt),Vt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Me.current&1?Qe===0&&(Qe=3):Wu())),t.updateQueue!==null&&(t.flags|=4),rt(t),null);case 4:return co(),Va(e,t),e===null&&li(t.stateNode.containerInfo),rt(t),null;case 10:return Nu(t.type._context),rt(t),null;case 17:return yt(t.type)&&Ll(),rt(t),null;case 19:if(Pe(Me),i=t.memoizedState,i===null)return rt(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)Ro(i,!1);else{if(Qe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Fl(e),a!==null){for(t.flags|=128,Ro(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ne(Me,Me.current&1|2),t.child}e=e.sibling}i.tail!==null&&Fe()>po&&(t.flags|=128,r=!0,Ro(i,!1),t.lanes=4194304)}else{if(!r)if(e=Fl(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ro(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!Oe)return rt(t),null}else 2*Fe()-i.renderingStartTime>po&&n!==1073741824&&(t.flags|=128,r=!0,Ro(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Fe(),t.sibling=null,n=Me.current,Ne(Me,r?n&1|2:n&1),t):(rt(t),null);case 22:case 23:return Hu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ct&1073741824&&(rt(t),t.subtreeFlags&6&&(t.flags|=8192)):rt(t),null;case 24:return null;case 25:return null}throw Error($(156,t.tag))}function Yg(e,t){switch(xu(t),t.tag){case 1:return yt(t.type)&&Ll(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return co(),Pe(gt),Pe(it),Lu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ou(t),null;case 13:if(Pe(Me),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error($(340));ao()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Pe(Me),null;case 4:return co(),null;case 10:return Nu(t.type._context),null;case 22:case 23:return Hu(),null;case 24:return null;default:return null}}var ll=!1,ot=!1,Xg=typeof WeakSet=="function"?WeakSet:Set,X=null;function Gr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ze(e,t,r)}else n.current=null}function qa(e,t,n){try{n()}catch(r){ze(e,t,r)}}var wd=!1;function Jg(e,t){if(Pa=bl,e=Mf(),Cu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,u=-1,d=-1,m=0,w=0,_=e,E=null;t:for(;;){for(var S;_!==n||o!==0&&_.nodeType!==3||(u=a+o),_!==i||r!==0&&_.nodeType!==3||(d=a+r),_.nodeType===3&&(a+=_.nodeValue.length),(S=_.firstChild)!==null;)E=_,_=S;for(;;){if(_===e)break t;if(E===n&&++m===o&&(u=a),E===i&&++w===r&&(d=a),(S=_.nextSibling)!==null)break;_=E,E=_.parentNode}_=S}n=u===-1||d===-1?null:{start:u,end:d}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ia={focusedElem:e,selectionRange:n},bl=!1,X=t;X!==null;)if(t=X,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,X=e;else for(;X!==null;){t=X;try{var C=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(C!==null){var T=C.memoizedProps,O=C.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?T:Ht(t.type,T),O);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error($(163))}}catch(I){ze(t,t.return,I)}if(e=t.sibling,e!==null){e.return=t.return,X=e;break}X=t.return}return C=wd,wd=!1,C}function Go(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&qa(t,n,i)}o=o.next}while(o!==r)}}function rs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ka(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Pp(e){var t=e.alternate;t!==null&&(e.alternate=null,Pp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[rn],delete t[ai],delete t[Ma],delete t[Ag],delete t[jg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ip(e){return e.tag===5||e.tag===3||e.tag===4}function kd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ip(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Qa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ol));else if(r!==4&&(e=e.child,e!==null))for(Qa(e,t,n),e=e.sibling;e!==null;)Qa(e,t,n),e=e.sibling}function Ga(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ga(e,t,n),e=e.sibling;e!==null;)Ga(e,t,n),e=e.sibling}var Je=null,Wt=!1;function Mn(e,t,n){for(n=n.child;n!==null;)Op(e,t,n),n=n.sibling}function Op(e,t,n){if(on&&typeof on.onCommitFiberUnmount=="function")try{on.onCommitFiberUnmount(Gl,n)}catch{}switch(n.tag){case 5:ot||Gr(n,t);case 6:var r=Je,o=Wt;Je=null,Mn(e,t,n),Je=r,Wt=o,Je!==null&&(Wt?(e=Je,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Je.removeChild(n.stateNode));break;case 18:Je!==null&&(Wt?(e=Je,n=n.stateNode,e.nodeType===8?Xs(e.parentNode,n):e.nodeType===1&&Xs(e,n),ri(e)):Xs(Je,n.stateNode));break;case 4:r=Je,o=Wt,Je=n.stateNode.containerInfo,Wt=!0,Mn(e,t,n),Je=r,Wt=o;break;case 0:case 11:case 14:case 15:if(!ot&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&qa(n,t,a),o=o.next}while(o!==r)}Mn(e,t,n);break;case 1:if(!ot&&(Gr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(u){ze(n,t,u)}Mn(e,t,n);break;case 21:Mn(e,t,n);break;case 22:n.mode&1?(ot=(r=ot)||n.memoizedState!==null,Mn(e,t,n),ot=r):Mn(e,t,n);break;default:Mn(e,t,n)}}function Cd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Xg),t.forEach(function(r){var o=sy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,a=t,u=a;e:for(;u!==null;){switch(u.tag){case 5:Je=u.stateNode,Wt=!1;break e;case 3:Je=u.stateNode.containerInfo,Wt=!0;break e;case 4:Je=u.stateNode.containerInfo,Wt=!0;break e}u=u.return}if(Je===null)throw Error($(160));Op(i,a,o),Je=null,Wt=!1;var d=o.alternate;d!==null&&(d.return=null),o.return=null}catch(m){ze(o,t,m)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Lp(t,e),t=t.sibling}function Lp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),en(e),r&4){try{Go(3,e,e.return),rs(3,e)}catch(T){ze(e,e.return,T)}try{Go(5,e,e.return)}catch(T){ze(e,e.return,T)}}break;case 1:Bt(t,e),en(e),r&512&&n!==null&&Gr(n,n.return);break;case 5:if(Bt(t,e),en(e),r&512&&n!==null&&Gr(n,n.return),e.flags&32){var o=e.stateNode;try{Zo(o,"")}catch(T){ze(e,e.return,T)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,u=e.type,d=e.updateQueue;if(e.updateQueue=null,d!==null)try{u==="input"&&i.type==="radio"&&i.name!=null&&Zd(o,i),wa(u,a);var m=wa(u,i);for(a=0;a<d.length;a+=2){var w=d[a],_=d[a+1];w==="style"?of(o,_):w==="dangerouslySetInnerHTML"?nf(o,_):w==="children"?Zo(o,_):au(o,w,_,m)}switch(u){case"input":ha(o,i);break;case"textarea":ef(o,i);break;case"select":var E=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var S=i.value;S!=null?Zr(o,!!i.multiple,S,!1):E!==!!i.multiple&&(i.defaultValue!=null?Zr(o,!!i.multiple,i.defaultValue,!0):Zr(o,!!i.multiple,i.multiple?[]:"",!1))}o[ai]=i}catch(T){ze(e,e.return,T)}}break;case 6:if(Bt(t,e),en(e),r&4){if(e.stateNode===null)throw Error($(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(T){ze(e,e.return,T)}}break;case 3:if(Bt(t,e),en(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ri(t.containerInfo)}catch(T){ze(e,e.return,T)}break;case 4:Bt(t,e),en(e);break;case 13:Bt(t,e),en(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||($u=Fe())),r&4&&Cd(e);break;case 22:if(w=n!==null&&n.memoizedState!==null,e.mode&1?(ot=(m=ot)||w,Bt(t,e),ot=m):Bt(t,e),en(e),r&8192){if(m=e.memoizedState!==null,(e.stateNode.isHidden=m)&&!w&&e.mode&1)for(X=e,w=e.child;w!==null;){for(_=X=w;X!==null;){switch(E=X,S=E.child,E.tag){case 0:case 11:case 14:case 15:Go(4,E,E.return);break;case 1:Gr(E,E.return);var C=E.stateNode;if(typeof C.componentWillUnmount=="function"){r=E,n=E.return;try{t=r,C.props=t.memoizedProps,C.state=t.memoizedState,C.componentWillUnmount()}catch(T){ze(r,n,T)}}break;case 5:Gr(E,E.return);break;case 22:if(E.memoizedState!==null){xd(_);continue}}S!==null?(S.return=E,X=S):xd(_)}w=w.sibling}e:for(w=null,_=e;;){if(_.tag===5){if(w===null){w=_;try{o=_.stateNode,m?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(u=_.stateNode,d=_.memoizedProps.style,a=d!=null&&d.hasOwnProperty("display")?d.display:null,u.style.display=rf("display",a))}catch(T){ze(e,e.return,T)}}}else if(_.tag===6){if(w===null)try{_.stateNode.nodeValue=m?"":_.memoizedProps}catch(T){ze(e,e.return,T)}}else if((_.tag!==22&&_.tag!==23||_.memoizedState===null||_===e)&&_.child!==null){_.child.return=_,_=_.child;continue}if(_===e)break e;for(;_.sibling===null;){if(_.return===null||_.return===e)break e;w===_&&(w=null),_=_.return}w===_&&(w=null),_.sibling.return=_.return,_=_.sibling}}break;case 19:Bt(t,e),en(e),r&4&&Cd(e);break;case 21:break;default:Bt(t,e),en(e)}}function en(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ip(n)){var r=n;break e}n=n.return}throw Error($(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Zo(o,""),r.flags&=-33);var i=kd(e);Ga(e,i,o);break;case 3:case 4:var a=r.stateNode.containerInfo,u=kd(e);Qa(e,u,a);break;default:throw Error($(161))}}catch(d){ze(e,e.return,d)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zg(e,t,n){X=e,Mp(e)}function Mp(e,t,n){for(var r=(e.mode&1)!==0;X!==null;){var o=X,i=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||ll;if(!a){var u=o.alternate,d=u!==null&&u.memoizedState!==null||ot;u=ll;var m=ot;if(ll=a,(ot=d)&&!m)for(X=o;X!==null;)a=X,d=a.child,a.tag===22&&a.memoizedState!==null?_d(o):d!==null?(d.return=a,X=d):_d(o);for(;i!==null;)X=i,Mp(i),i=i.sibling;X=o,ll=u,ot=m}Sd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,X=i):Sd(e)}}function Sd(e){for(;X!==null;){var t=X;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ot||rs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ot)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ht(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ld(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ld(t,a,n)}break;case 5:var u=t.stateNode;if(n===null&&t.flags&4){n=u;var d=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":d.autoFocus&&n.focus();break;case"img":d.src&&(n.src=d.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var m=t.alternate;if(m!==null){var w=m.memoizedState;if(w!==null){var _=w.dehydrated;_!==null&&ri(_)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error($(163))}ot||t.flags&512&&Ka(t)}catch(E){ze(t,t.return,E)}}if(t===e){X=null;break}if(n=t.sibling,n!==null){n.return=t.return,X=n;break}X=t.return}}function xd(e){for(;X!==null;){var t=X;if(t===e){X=null;break}var n=t.sibling;if(n!==null){n.return=t.return,X=n;break}X=t.return}}function _d(e){for(;X!==null;){var t=X;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(d){ze(t,n,d)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(d){ze(t,o,d)}}var i=t.return;try{Ka(t)}catch(d){ze(t,i,d)}break;case 5:var a=t.return;try{Ka(t)}catch(d){ze(t,a,d)}}}catch(d){ze(t,t.return,d)}if(t===e){X=null;break}var u=t.sibling;if(u!==null){u.return=t.return,X=u;break}X=t.return}}var ey=Math.ceil,Bl=Cn.ReactCurrentDispatcher,Fu=Cn.ReactCurrentOwner,Ot=Cn.ReactCurrentBatchConfig,ye=0,Xe=null,We=null,Ze=0,Ct=0,Yr=Xn(0),Qe=0,hi=null,xr=0,os=0,Uu=0,Yo=null,ht=null,$u=0,po=1/0,fn=null,Hl=!1,Ya=null,Vn=null,sl=!1,Fn=null,Wl=0,Xo=0,Xa=null,wl=-1,kl=0;function at(){return ye&6?Fe():wl!==-1?wl:wl=Fe()}function qn(e){return e.mode&1?ye&2&&Ze!==0?Ze&-Ze:Dg.transition!==null?(kl===0&&(kl=yf()),kl):(e=xe,e!==0||(e=window.event,e=e===void 0?16:_f(e.type)),e):1}function Kt(e,t,n,r){if(50<Xo)throw Xo=0,Xa=null,Error($(185));gi(e,n,r),(!(ye&2)||e!==Xe)&&(e===Xe&&(!(ye&2)&&(os|=n),Qe===4&&Dn(e,Ze)),vt(e,r),n===1&&ye===0&&!(t.mode&1)&&(po=Fe()+500,es&&Jn()))}function vt(e,t){var n=e.callbackNode;Dm(e,t);var r=Nl(e,e===Xe?Ze:0);if(r===0)n!==null&&Mc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Mc(n),t===1)e.tag===0?Rg(Ed.bind(null,e)):Hf(Ed.bind(null,e)),Lg(function(){!(ye&6)&&Jn()}),n=null;else{switch(vf(r)){case 1:n=pu;break;case 4:n=mf;break;case 16:n=Tl;break;case 536870912:n=gf;break;default:n=Tl}n=$p(n,Ap.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Ap(e,t){if(wl=-1,kl=0,ye&6)throw Error($(327));var n=e.callbackNode;if(oo()&&e.callbackNode!==n)return null;var r=Nl(e,e===Xe?Ze:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Vl(e,r);else{t=r;var o=ye;ye|=2;var i=Rp();(Xe!==e||Ze!==t)&&(fn=null,po=Fe()+500,yr(e,t));do try{ry();break}catch(u){jp(e,u)}while(1);Tu(),Bl.current=i,ye=o,We!==null?t=0:(Xe=null,Ze=0,t=Qe)}if(t!==0){if(t===2&&(o=_a(e),o!==0&&(r=o,t=Ja(e,o))),t===1)throw n=hi,yr(e,0),Dn(e,r),vt(e,Fe()),n;if(t===6)Dn(e,r);else{if(o=e.current.alternate,!(r&30)&&!ty(o)&&(t=Vl(e,r),t===2&&(i=_a(e),i!==0&&(r=i,t=Ja(e,i))),t===1))throw n=hi,yr(e,0),Dn(e,r),vt(e,Fe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error($(345));case 2:fr(e,ht,fn);break;case 3:if(Dn(e,r),(r&130023424)===r&&(t=$u+500-Fe(),10<t)){if(Nl(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){at(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=La(fr.bind(null,e,ht,fn),t);break}fr(e,ht,fn);break;case 4:if(Dn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-qt(r);i=1<<a,a=t[a],a>o&&(o=a),r&=~i}if(r=o,r=Fe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ey(r/1960))-r,10<r){e.timeoutHandle=La(fr.bind(null,e,ht,fn),r);break}fr(e,ht,fn);break;case 5:fr(e,ht,fn);break;default:throw Error($(329))}}}return vt(e,Fe()),e.callbackNode===n?Ap.bind(null,e):null}function Ja(e,t){var n=Yo;return e.current.memoizedState.isDehydrated&&(yr(e,t).flags|=256),e=Vl(e,t),e!==2&&(t=ht,ht=n,t!==null&&Za(t)),e}function Za(e){ht===null?ht=e:ht.push.apply(ht,e)}function ty(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Qt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Dn(e,t){for(t&=~Uu,t&=~os,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qt(t),r=1<<n;e[n]=-1,t&=~r}}function Ed(e){if(ye&6)throw Error($(327));oo();var t=Nl(e,0);if(!(t&1))return vt(e,Fe()),null;var n=Vl(e,t);if(e.tag!==0&&n===2){var r=_a(e);r!==0&&(t=r,n=Ja(e,r))}if(n===1)throw n=hi,yr(e,0),Dn(e,t),vt(e,Fe()),n;if(n===6)throw Error($(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,fr(e,ht,fn),vt(e,Fe()),null}function Bu(e,t){var n=ye;ye|=1;try{return e(t)}finally{ye=n,ye===0&&(po=Fe()+500,es&&Jn())}}function _r(e){Fn!==null&&Fn.tag===0&&!(ye&6)&&oo();var t=ye;ye|=1;var n=Ot.transition,r=xe;try{if(Ot.transition=null,xe=1,e)return e()}finally{xe=r,Ot.transition=n,ye=t,!(ye&6)&&Jn()}}function Hu(){Ct=Yr.current,Pe(Yr)}function yr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Og(n)),We!==null)for(n=We.return;n!==null;){var r=n;switch(xu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ll();break;case 3:co(),Pe(gt),Pe(it),Lu();break;case 5:Ou(r);break;case 4:co();break;case 13:Pe(Me);break;case 19:Pe(Me);break;case 10:Nu(r.type._context);break;case 22:case 23:Hu()}n=n.return}if(Xe=e,We=e=Kn(e.current,null),Ze=Ct=t,Qe=0,hi=null,Uu=os=xr=0,ht=Yo=null,hr!==null){for(t=0;t<hr.length;t++)if(n=hr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=o,r.next=a}n.pending=r}hr=null}return e}function jp(e,t){do{var n=We;try{if(Tu(),gl.current=$l,Ul){for(var r=Ae.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ul=!1}if(Sr=0,Ye=Ke=Ae=null,Qo=!1,di=0,Fu.current=null,n===null||n.return===null){Qe=1,hi=t,We=null;break}e:{var i=e,a=n.return,u=n,d=t;if(t=Ze,u.flags|=32768,d!==null&&typeof d=="object"&&typeof d.then=="function"){var m=d,w=u,_=w.tag;if(!(w.mode&1)&&(_===0||_===11||_===15)){var E=w.alternate;E?(w.updateQueue=E.updateQueue,w.memoizedState=E.memoizedState,w.lanes=E.lanes):(w.updateQueue=null,w.memoizedState=null)}var S=fd(a);if(S!==null){S.flags&=-257,pd(S,a,u,i,t),S.mode&1&&dd(i,m,t),t=S,d=m;var C=t.updateQueue;if(C===null){var T=new Set;T.add(d),t.updateQueue=T}else C.add(d);break e}else{if(!(t&1)){dd(i,m,t),Wu();break e}d=Error($(426))}}else if(Oe&&u.mode&1){var O=fd(a);if(O!==null){!(O.flags&65536)&&(O.flags|=256),pd(O,a,u,i,t),_u(fo(d,u));break e}}i=d=fo(d,u),Qe!==4&&(Qe=2),Yo===null?Yo=[i]:Yo.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=vp(i,d,t);id(i,g);break e;case 1:u=d;var p=i.type,f=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Vn===null||!Vn.has(f)))){i.flags|=65536,t&=-t,i.lanes|=t;var I=wp(i,u,t);id(i,I);break e}}i=i.return}while(i!==null)}zp(n)}catch(N){t=N,We===n&&n!==null&&(We=n=n.return);continue}break}while(1)}function Rp(){var e=Bl.current;return Bl.current=$l,e===null?$l:e}function Wu(){(Qe===0||Qe===3||Qe===2)&&(Qe=4),Xe===null||!(xr&268435455)&&!(os&268435455)||Dn(Xe,Ze)}function Vl(e,t){var n=ye;ye|=2;var r=Rp();(Xe!==e||Ze!==t)&&(fn=null,yr(e,t));do try{ny();break}catch(o){jp(e,o)}while(1);if(Tu(),ye=n,Bl.current=r,We!==null)throw Error($(261));return Xe=null,Ze=0,Qe}function ny(){for(;We!==null;)Dp(We)}function ry(){for(;We!==null&&!bm();)Dp(We)}function Dp(e){var t=Up(e.alternate,e,Ct);e.memoizedProps=e.pendingProps,t===null?zp(e):We=t,Fu.current=null}function zp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Yg(n,t),n!==null){n.flags&=32767,We=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Qe=6,We=null;return}}else if(n=Gg(n,t,Ct),n!==null){We=n;return}if(t=t.sibling,t!==null){We=t;return}We=t=e}while(t!==null);Qe===0&&(Qe=5)}function fr(e,t,n){var r=xe,o=Ot.transition;try{Ot.transition=null,xe=1,oy(e,t,n,r)}finally{Ot.transition=o,xe=r}return null}function oy(e,t,n,r){do oo();while(Fn!==null);if(ye&6)throw Error($(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error($(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(zm(e,i),e===Xe&&(We=Xe=null,Ze=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||sl||(sl=!0,$p(Tl,function(){return oo(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ot.transition,Ot.transition=null;var a=xe;xe=1;var u=ye;ye|=4,Fu.current=null,Jg(e,n),Lp(n,e),_g(Ia),bl=!!Pa,Ia=Pa=null,e.current=n,Zg(n),Pm(),ye=u,xe=a,Ot.transition=i}else e.current=n;if(sl&&(sl=!1,Fn=e,Wl=o),i=e.pendingLanes,i===0&&(Vn=null),Lm(n.stateNode),vt(e,Fe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hl)throw Hl=!1,e=Ya,Ya=null,e;return Wl&1&&e.tag!==0&&oo(),i=e.pendingLanes,i&1?e===Xa?Xo++:(Xo=0,Xa=e):Xo=0,Jn(),null}function oo(){if(Fn!==null){var e=vf(Wl),t=Ot.transition,n=xe;try{if(Ot.transition=null,xe=16>e?16:e,Fn===null)var r=!1;else{if(e=Fn,Fn=null,Wl=0,ye&6)throw Error($(331));var o=ye;for(ye|=4,X=e.current;X!==null;){var i=X,a=i.child;if(X.flags&16){var u=i.deletions;if(u!==null){for(var d=0;d<u.length;d++){var m=u[d];for(X=m;X!==null;){var w=X;switch(w.tag){case 0:case 11:case 15:Go(8,w,i)}var _=w.child;if(_!==null)_.return=w,X=_;else for(;X!==null;){w=X;var E=w.sibling,S=w.return;if(Pp(w),w===m){X=null;break}if(E!==null){E.return=S,X=E;break}X=S}}}var C=i.alternate;if(C!==null){var T=C.child;if(T!==null){C.child=null;do{var O=T.sibling;T.sibling=null,T=O}while(T!==null)}}X=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,X=a;else e:for(;X!==null;){if(i=X,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Go(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,X=g;break e}X=i.return}}var p=e.current;for(X=p;X!==null;){a=X;var f=a.child;if(a.subtreeFlags&2064&&f!==null)f.return=a,X=f;else e:for(a=p;X!==null;){if(u=X,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:rs(9,u)}}catch(N){ze(u,u.return,N)}if(u===a){X=null;break e}var I=u.sibling;if(I!==null){I.return=u.return,X=I;break e}X=u.return}}if(ye=o,Jn(),on&&typeof on.onPostCommitFiberRoot=="function")try{on.onPostCommitFiberRoot(Gl,e)}catch{}r=!0}return r}finally{xe=n,Ot.transition=t}}return!1}function Td(e,t,n){t=fo(n,t),t=vp(e,t,1),e=Wn(e,t,1),t=at(),e!==null&&(gi(e,1,t),vt(e,t))}function ze(e,t,n){if(e.tag===3)Td(e,e,n);else for(;t!==null;){if(t.tag===3){Td(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Vn===null||!Vn.has(r))){e=fo(n,e),e=wp(t,e,1),t=Wn(t,e,1),e=at(),t!==null&&(gi(t,1,e),vt(t,e));break}}t=t.return}}function iy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=at(),e.pingedLanes|=e.suspendedLanes&n,Xe===e&&(Ze&n)===n&&(Qe===4||Qe===3&&(Ze&130023424)===Ze&&500>Fe()-$u?yr(e,0):Uu|=n),vt(e,t)}function Fp(e,t){t===0&&(e.mode&1?(t=Xi,Xi<<=1,!(Xi&130023424)&&(Xi=4194304)):t=1);var n=at();e=wn(e,t),e!==null&&(gi(e,t,n),vt(e,n))}function ly(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Fp(e,n)}function sy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error($(314))}r!==null&&r.delete(t),Fp(e,n)}var Up;Up=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||gt.current)mt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return mt=!1,Qg(e,t,n);mt=!!(e.flags&131072)}else mt=!1,Oe&&t.flags&1048576&&Wf(t,jl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;vl(e,t),e=t.pendingProps;var o=so(t,it.current);ro(t,n),o=Au(null,t,r,e,o,n);var i=ju();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,yt(r)?(i=!0,Ml(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Pu(t),o.updater=ns,t.stateNode=o,o._reactInternals=t,Fa(t,r,e,n),t=Ba(null,t,r,!0,i,n)):(t.tag=0,Oe&&i&&Su(t),st(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(vl(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=uy(r),e=Ht(r,e),o){case 0:t=$a(null,t,r,e,n);break e;case 1:t=gd(null,t,r,e,n);break e;case 11:t=hd(null,t,r,e,n);break e;case 14:t=md(null,t,r,Ht(r.type,e),n);break e}throw Error($(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),$a(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),gd(e,t,r,o,n);case 3:e:{if(xp(t),e===null)throw Error($(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Yf(e,t),zl(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=fo(Error($(423)),t),t=yd(e,t,r,n,o);break e}else if(r!==o){o=fo(Error($(424)),t),t=yd(e,t,r,n,o);break e}else for(St=Hn(t.stateNode.containerInfo.firstChild),xt=t,Oe=!0,Vt=null,n=Qf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ao(),r===o){t=kn(e,t,n);break e}st(e,t,r,n)}t=t.child}return t;case 5:return Xf(t),e===null&&Ra(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,a=o.children,Oa(r,o)?a=null:i!==null&&Oa(r,i)&&(t.flags|=32),Sp(e,t),st(e,t,a,n),t.child;case 6:return e===null&&Ra(t),null;case 13:return _p(e,t,n);case 4:return Iu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=uo(t,null,r,n):st(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),hd(e,t,r,o,n);case 7:return st(e,t,t.pendingProps,n),t.child;case 8:return st(e,t,t.pendingProps.children,n),t.child;case 12:return st(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,a=o.value,Ne(Rl,r._currentValue),r._currentValue=a,i!==null)if(Qt(i.value,a)){if(i.children===o.children&&!gt.current){t=kn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var u=i.dependencies;if(u!==null){a=i.child;for(var d=u.firstContext;d!==null;){if(d.context===r){if(i.tag===1){d=gn(-1,n&-n),d.tag=2;var m=i.updateQueue;if(m!==null){m=m.shared;var w=m.pending;w===null?d.next=d:(d.next=w.next,w.next=d),m.pending=d}}i.lanes|=n,d=i.alternate,d!==null&&(d.lanes|=n),Da(i.return,n,t),u.lanes|=n;break}d=d.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error($(341));a.lanes|=n,u=a.alternate,u!==null&&(u.lanes|=n),Da(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}st(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,ro(t,n),o=Lt(o),r=r(o),t.flags|=1,st(e,t,r,n),t.child;case 14:return r=t.type,o=Ht(r,t.pendingProps),o=Ht(r.type,o),md(e,t,r,o,n);case 15:return kp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),vl(e,t),t.tag=1,yt(r)?(e=!0,Ml(t)):e=!1,ro(t,n),yp(t,r,o),Fa(t,r,o,n),Ba(null,t,r,!0,e,n);case 19:return Ep(e,t,n);case 22:return Cp(e,t,n)}throw Error($(156,t.tag))};function $p(e,t){return hf(e,t)}function ay(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function It(e,t,n,r){return new ay(e,t,n,r)}function Vu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function uy(e){if(typeof e=="function")return Vu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===cu)return 11;if(e===du)return 14}return 2}function Kn(e,t){var n=e.alternate;return n===null?(n=It(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Cl(e,t,n,r,o,i){var a=2;if(r=e,typeof e=="function")Vu(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Ur:return vr(n.children,o,i,t);case uu:a=8,o|=8;break;case ua:return e=It(12,n,t,o|2),e.elementType=ua,e.lanes=i,e;case ca:return e=It(13,n,t,o),e.elementType=ca,e.lanes=i,e;case da:return e=It(19,n,t,o),e.elementType=da,e.lanes=i,e;case Yd:return is(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Qd:a=10;break e;case Gd:a=9;break e;case cu:a=11;break e;case du:a=14;break e;case An:a=16,r=null;break e}throw Error($(130,e==null?e:typeof e,""))}return t=It(a,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function vr(e,t,n,r){return e=It(7,e,r,t),e.lanes=n,e}function is(e,t,n,r){return e=It(22,e,r,t),e.elementType=Yd,e.lanes=n,e.stateNode={isHidden:!1},e}function ia(e,t,n){return e=It(6,e,null,t),e.lanes=n,e}function la(e,t,n){return t=It(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function cy(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Us(0),this.expirationTimes=Us(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Us(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function qu(e,t,n,r,o,i,a,u,d){return e=new cy(e,t,n,u,d),t===1?(t=1,i===!0&&(t|=8)):t=0,i=It(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Pu(i),e}function dy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Fr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Bp(e){if(!e)return Gn;e=e._reactInternals;e:{if(Tr(e)!==e||e.tag!==1)throw Error($(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(yt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error($(171))}if(e.tag===1){var n=e.type;if(yt(n))return Bf(e,n,t)}return t}function Hp(e,t,n,r,o,i,a,u,d){return e=qu(n,r,!0,e,o,i,a,u,d),e.context=Bp(null),n=e.current,r=at(),o=qn(n),i=gn(r,o),i.callback=t??null,Wn(n,i,o),e.current.lanes=o,gi(e,o,r),vt(e,r),e}function ls(e,t,n,r){var o=t.current,i=at(),a=qn(o);return n=Bp(n),t.context===null?t.context=n:t.pendingContext=n,t=gn(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Wn(o,t,a),e!==null&&(Kt(e,o,a,i),ml(e,o,a)),a}function ql(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Nd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ku(e,t){Nd(e,t),(e=e.alternate)&&Nd(e,t)}function fy(){return null}var Wp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Qu(e){this._internalRoot=e}ss.prototype.render=Qu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error($(409));ls(e,t,null,null)};ss.prototype.unmount=Qu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;_r(function(){ls(null,e,null,null)}),t[vn]=null}};function ss(e){this._internalRoot=e}ss.prototype.unstable_scheduleHydration=function(e){if(e){var t=Cf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rn.length&&t!==0&&t<Rn[n].priority;n++);Rn.splice(n,0,e),n===0&&xf(e)}};function Gu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function as(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function bd(){}function py(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var m=ql(a);i.call(m)}}var a=Hp(t,r,e,0,null,!1,!1,"",bd);return e._reactRootContainer=a,e[vn]=a.current,li(e.nodeType===8?e.parentNode:e),_r(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var u=r;r=function(){var m=ql(d);u.call(m)}}var d=qu(e,0,!1,null,null,!1,!1,"",bd);return e._reactRootContainer=d,e[vn]=d.current,li(e.nodeType===8?e.parentNode:e),_r(function(){ls(t,d,n,r)}),d}function us(e,t,n,r,o){var i=n._reactRootContainer;if(i){var a=i;if(typeof o=="function"){var u=o;o=function(){var d=ql(a);u.call(d)}}ls(t,a,e,o)}else a=py(n,t,e,o,r);return ql(a)}wf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=$o(t.pendingLanes);n!==0&&(hu(t,n|1),vt(t,Fe()),!(ye&6)&&(po=Fe()+500,Jn()))}break;case 13:_r(function(){var r=wn(e,1);if(r!==null){var o=at();Kt(r,e,1,o)}}),Ku(e,1)}};mu=function(e){if(e.tag===13){var t=wn(e,134217728);if(t!==null){var n=at();Kt(t,e,134217728,n)}Ku(e,134217728)}};kf=function(e){if(e.tag===13){var t=qn(e),n=wn(e,t);if(n!==null){var r=at();Kt(n,e,t,r)}Ku(e,t)}};Cf=function(){return xe};Sf=function(e,t){var n=xe;try{return xe=e,t()}finally{xe=n}};Ca=function(e,t,n){switch(t){case"input":if(ha(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Zl(r);if(!o)throw Error($(90));Jd(r),ha(r,o)}}}break;case"textarea":ef(e,n);break;case"select":t=n.value,t!=null&&Zr(e,!!n.multiple,t,!1)}};af=Bu;uf=_r;var hy={usingClientEntryPoint:!1,Events:[vi,Wr,Zl,lf,sf,Bu]},Do={findFiberByHostInstance:pr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},my={bundleType:Do.bundleType,version:Do.version,rendererPackageName:Do.rendererPackageName,rendererConfig:Do.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Cn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ff(e),e===null?null:e.stateNode},findFiberByHostInstance:Do.findFiberByHostInstance||fy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var al=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!al.isDisabled&&al.supportsFiber)try{Gl=al.inject(my),on=al}catch{}}Et.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=hy;Et.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Gu(t))throw Error($(200));return dy(e,t,null,n)};Et.createRoot=function(e,t){if(!Gu(e))throw Error($(299));var n=!1,r="",o=Wp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=qu(e,1,!1,null,null,n,!1,r,o),e[vn]=t.current,li(e.nodeType===8?e.parentNode:e),new Qu(t)};Et.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error($(188)):(e=Object.keys(e).join(","),Error($(268,e)));return e=ff(t),e=e===null?null:e.stateNode,e};Et.flushSync=function(e){return _r(e)};Et.hydrate=function(e,t,n){if(!as(t))throw Error($(200));return us(null,e,t,!0,n)};Et.hydrateRoot=function(e,t,n){if(!Gu(e))throw Error($(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",a=Wp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Hp(t,null,e,1,n??null,o,!1,i,a),e[vn]=t.current,li(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ss(t)};Et.render=function(e,t,n){if(!as(t))throw Error($(200));return us(null,e,t,!1,n)};Et.unmountComponentAtNode=function(e){if(!as(e))throw Error($(40));return e._reactRootContainer?(_r(function(){us(null,null,e,!1,function(){e._reactRootContainer=null,e[vn]=null})}),!0):!1};Et.unstable_batchedUpdates=Bu;Et.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!as(n))throw Error($(200));if(e==null||e._reactInternals===void 0)throw Error($(38));return us(e,t,n,!1,r)};Et.version="18.3.1-next-f1338f8080-20240426";function Vp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Vp)}catch(e){console.error(e)}}Vp(),Wd.exports=Et;var gy=Wd.exports,qp,Pd=gy;qp=Pd.createRoot,Pd.hydrateRoot;let Kp=logseq.isMainUIVisible;function yy(e,t){return logseq.on(e,t),()=>{logseq.off(e,t)}}const vy=e=>yy("ui:visible:changed",({visible:t})=>{Kp=t,e()}),wy=()=>Bd.useSyncExternalStore(vy,()=>Kp),nn={apiUrl:"https://api.openai.com/v1/chat/completions",apiKey:"",modelName:"gpt-3.5-turbo",temperature:.7,maxTokens:2e3,enableHistory:!1,customPrompts:[{name:"总结",prompt:"请总结以下内容的要点："},{name:"扩展",prompt:"请基于以下内容进行扩展和补充："}],keybindings:{openChat:{binding:"ctrl+g",mac:"cmd+g",description:"打开AI聊天"},quickReply:{binding:"ctrl+shift+g",mac:"cmd+shift+g",description:"快速AI回复"},openChatWindow:{binding:"ctrl+alt+g",mac:"cmd+alt+g",description:"打开聊天窗口"}}};async function ky(){const e=logseq.settings||{},t=Object.assign({},nn,e);return Array.isArray(t.customPrompts)||(t.customPrompts=nn.customPrompts),t.temperature===void 0&&(t.temperature=nn.temperature),t.maxTokens===void 0&&(t.maxTokens=nn.maxTokens),t.keybindings?(t.keybindings.openChat||(t.keybindings.openChat=nn.keybindings.openChat),t.keybindings.quickReply||(t.keybindings.quickReply=nn.keybindings.quickReply),t.keybindings.openChatWindow||(t.keybindings.openChatWindow=nn.keybindings.openChatWindow)):t.keybindings=nn.keybindings,await logseq.updateSettings(t),t}async function Cy(e){await logseq.updateSettings(e)}async function ki(){const e=logseq.settings||{};return Object.assign({},nn,e)}function Sy(e){if(!e||typeof e!="string")return{isValid:!1,error:"快捷键不能为空"};const t=e.trim();if(!t)return{isValid:!1,error:"快捷键不能为空"};const n=["ctrl","cmd","alt","shift","meta"],r=t.toLowerCase().split("+");if(r.length===0)return{isValid:!1,error:"快捷键格式无效"};const o=r[r.length-1];if(!o||o.length===0)return{isValid:!1,error:"缺少按键部分"};const i=r.slice(0,-1);for(const d of i)if(!n.includes(d))return{isValid:!1,error:`无效的修饰键: ${d}`};return[...new Set(i)].length!==i.length?{isValid:!1,error:"不能有重复的修饰键"}:/^[a-z0-9]$|^f[1-9]$|^f1[0-2]$|^(space|enter|tab|escape|backspace|delete|home|end|pageup|pagedown|insert|up|down|left|right)$/.test(o)?{isValid:!0}:{isValid:!1,error:`无效的按键: ${o}`}}function Id(e,t=!1){const n=t?e.mac:e.binding;return n?n.split("+").map(r=>{switch(r.toLowerCase()){case"ctrl":return t?"⌃":"Ctrl";case"cmd":return"⌘";case"alt":return t?"⌥":"Alt";case"shift":return t?"⇧":"Shift";case"meta":return t?"⌘":"Win";default:return r.toUpperCase()}}).join(t?"":"+"):""}function xy(e,t){const n=e.toLowerCase().trim();if(!n)return{hasConflict:!1};const r=["ctrl+c","cmd+c","ctrl+v","cmd+v","ctrl+x","cmd+x","ctrl+z","cmd+z","ctrl+y","cmd+y","ctrl+a","cmd+a","ctrl+s","cmd+s","ctrl+f","cmd+f","ctrl+n","cmd+n","ctrl+o","cmd+o","ctrl+p","cmd+p","ctrl+w","cmd+w","ctrl+t","cmd+t","ctrl+r","cmd+r","ctrl+l","cmd+l","ctrl+d","cmd+d","ctrl+h","cmd+h","ctrl+j","cmd+j","ctrl+k","cmd+k","ctrl+u","cmd+u","alt+f4","cmd+q","f5","cmd+r","f11","f12"],o=["ctrl+shift+p","cmd+shift+p","ctrl+k","cmd+k","ctrl+shift+k","cmd+shift+k","ctrl+/","cmd+/","ctrl+shift+/","cmd+shift+/","ctrl+e","cmd+e","ctrl+shift+e","cmd+shift+e"];if(r.includes(n))return{hasConflict:!0,type:"system",message:`可能与系统快捷键冲突: ${n}`};if(o.includes(n))return{hasConflict:!0,type:"system",message:`可能与Logseq快捷键冲突: ${n}`};if(t){for(const[i,a]of Object.entries(t))if(a.binding.toLowerCase()===n||a.mac.toLowerCase()===n)return{hasConflict:!0,type:"internal",message:`与 "${a.description}" 快捷键冲突`}}return{hasConflict:!1}}class Xr{static instance;currentTheme;listeners=[];mediaQuery;constructor(){this.mediaQuery=window.matchMedia("(prefers-color-scheme: dark)"),this.currentTheme={mode:"auto",isDark:!1,systemPrefersDark:this.mediaQuery.matches},this.mediaQuery.addEventListener("change",this.handleSystemThemeChange.bind(this)),this.initializeTheme()}static getInstance(){return Xr.instance||(Xr.instance=new Xr),Xr.instance}async initializeTheme(){try{const t=await logseq.App.getStateFromStore("ui/theme");this.currentTheme.isDark=t==="dark",this.currentTheme.mode=t==="dark"?"dark":"light",this.applyTheme(),logseq.App.onThemeModeChanged(({mode:n})=>{this.currentTheme.isDark=n==="dark",this.currentTheme.mode=n==="dark"?"dark":"light",this.applyTheme(),this.notifyListeners()})}catch(t){console.error("主题初始化失败:",t),this.currentTheme.isDark=this.currentTheme.systemPrefersDark,this.applyTheme()}}handleSystemThemeChange=t=>{this.currentTheme.systemPrefersDark=t.matches,this.currentTheme.mode==="auto"&&(this.currentTheme.isDark=t.matches,this.applyTheme(),this.notifyListeners())};applyTheme(){const t=document.documentElement;this.currentTheme.isDark?t.classList.add("dark"):t.classList.remove("dark"),this.setCSSVariables()}setCSSVariables(){const t=document.documentElement;this.currentTheme.isDark?(t.style.setProperty("--theme-bg-primary","#1a1a1a"),t.style.setProperty("--theme-bg-secondary","#2a2a2a"),t.style.setProperty("--theme-bg-tertiary","#3a3a3a"),t.style.setProperty("--theme-text-primary","#e0e0e0"),t.style.setProperty("--theme-text-secondary","#a0a0a0"),t.style.setProperty("--theme-text-muted","#666666"),t.style.setProperty("--theme-border-primary","#404040"),t.style.setProperty("--theme-border-secondary","#505050"),t.style.setProperty("--theme-accent","#0A84FF"),t.style.setProperty("--theme-accent-hover","#0066CC")):(t.style.setProperty("--theme-bg-primary","#ffffff"),t.style.setProperty("--theme-bg-secondary","#f8f9fa"),t.style.setProperty("--theme-bg-tertiary","#e9ecef"),t.style.setProperty("--theme-text-primary","#333333"),t.style.setProperty("--theme-text-secondary","#666666"),t.style.setProperty("--theme-text-muted","#999999"),t.style.setProperty("--theme-border-primary","#e0e0e0"),t.style.setProperty("--theme-border-secondary","#f0f0f0"),t.style.setProperty("--theme-accent","#007AFF"),t.style.setProperty("--theme-accent-hover","#0056CC"))}getTheme(){return{...this.currentTheme}}setTheme(t){switch(this.currentTheme.mode=t,t){case"light":this.currentTheme.isDark=!1;break;case"dark":this.currentTheme.isDark=!0;break;case"auto":this.currentTheme.isDark=this.currentTheme.systemPrefersDark;break}this.applyTheme(),this.notifyListeners()}toggleTheme(){const t=this.currentTheme.isDark?"light":"dark";this.setTheme(t)}addListener(t){this.listeners.push(t)}removeListener(t){const n=this.listeners.indexOf(t);n>-1&&this.listeners.splice(n,1)}notifyListeners(){this.listeners.forEach(t=>{try{t(this.getTheme())}catch(n){console.error("主题监听器执行失败:",n)}})}getThemeClasses(){const t=["theme-transition"];return this.currentTheme.isDark&&t.push("dark"),t.join(" ")}isDarkMode(){return this.currentTheme.isDark}getThemeColor(t){const n=this.currentTheme.isDark?{primary:"#e0e0e0",secondary:"#a0a0a0",muted:"#666666",accent:"#0A84FF",background:"#1a1a1a",surface:"#2a2a2a",border:"#404040"}:{primary:"#333333",secondary:"#666666",muted:"#999999",accent:"#007AFF",background:"#ffffff",surface:"#f8f9fa",border:"#e0e0e0"};return n[t]||n.primary}destroy(){this.mediaQuery.removeEventListener("change",this.handleSystemThemeChange),this.listeners=[]}}const Nr=()=>Xr.getInstance(),_y=({className:e="",showLabel:t=!0})=>{const n=Nr(),[r,o]=ne.useState(n.getTheme());ne.useEffect(()=>{const d=m=>{o(m)};return n.addListener(d),()=>{n.removeListener(d)}},[n]);const i=d=>{n.setTheme(d)},a=d=>{switch(d){case"light":return"☀️";case"dark":return"🌙";case"auto":return"🔄";default:return"☀️"}},u=d=>{switch(d){case"light":return"亮色模式";case"dark":return"暗色模式";case"auto":return"跟随系统";default:return"亮色模式"}};return Z("div",{className:`theme-toggle ${e}`,children:[t&&j("label",{className:"block text-sm font-medium mb-2",style:{color:n.getThemeColor("primary")},children:"主题设置"}),j("div",{className:"flex items-center space-x-2",children:["light","dark","auto"].map(d=>Z("button",{onClick:()=>i(d),className:`
              flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
              ${r.mode===d?"ring-2 ring-offset-2":"hover:opacity-80"}
            `,style:{backgroundColor:r.mode===d?n.getThemeColor("accent"):n.getThemeColor("surface"),color:r.mode===d?"#ffffff":n.getThemeColor("primary")},children:[j("span",{className:"text-base",children:a(d)}),j("span",{children:u(d)})]},d))}),r.mode==="auto"&&Z("div",{className:"mt-2 text-xs opacity-75",style:{color:n.getThemeColor("secondary")},children:["当前: ",r.isDark?"暗色":"亮色",r.systemPrefersDark?" (系统偏好暗色)":" (系统偏好亮色)"]})]})},Ey=({className:e=""})=>{const t=Nr(),[n,r]=ne.useState(t.getTheme());return ne.useEffect(()=>{const o=i=>{r(i)};return t.addListener(o),()=>{t.removeListener(o)}},[t]),Z("div",{className:`flex items-center space-x-2 text-sm ${e}`,style:{color:t.getThemeColor("secondary")},children:[j("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:n.isDark?"#4A90E2":"#F5A623"}}),j("span",{children:n.mode==="auto"?`自动 (${n.isDark?"暗色":"亮色"})`:n.isDark?"暗色模式":"亮色模式"})]})},Ty=({onBackToChat:e})=>{const[t,n]=ne.useState(nn),[r,o]=ne.useState(!0),[i,a]=ne.useState(!1),[u,d]=ne.useState(""),[m,w]=ne.useState({name:"",prompt:""}),[_,E]=ne.useState(null),[S,C]=ne.useState({}),[T,O]=ne.useState({}),[g,p]=ne.useState(!1),f=Nr(),[I,N]=ne.useState(f.isDarkMode());ne.useEffect(()=>{const z=v=>{N(v.isDark)};return f.addListener(z),()=>{f.removeListener(z)}},[f]),ne.useEffect(()=>{p(navigator.platform.toUpperCase().indexOf("MAC")>=0)},[]),ne.useEffect(()=>{(async()=>{try{const v=await ki();n(v)}catch(v){console.error("加载设置出错:",v)}finally{o(!1)}})()},[]),ne.useEffect(()=>{const z=v=>{v.key==="Escape"&&we()};return window.addEventListener("keydown",z),()=>{window.removeEventListener("keydown",z)}},[]);const L=z=>{const{name:v,value:b}=z.target;n(U=>({...U,[v]:b}))},h=z=>{const{name:v,checked:b}=z.target;n(U=>({...U,[v]:b}))},k=(z,v,b)=>{const U={...t.keybindings[z],[v]:b},re=Sy(b),fe=`${z}.${v}`;!re.isValid&&b.trim()!==""?C(Q=>({...Q,[fe]:re.error||"快捷键格式无效"})):C(Q=>{const se={...Q};return delete se[fe],se});const B=xy(b,t.keybindings);B.hasConflict?O(Q=>({...Q,[fe]:B.message||"存在快捷键冲突"})):O(Q=>{const se={...Q};return delete se[fe],se}),n(Q=>({...Q,keybindings:{...Q.keybindings,[z]:U}}))},R=z=>{const{name:v,value:b}=z.target;w(U=>({...U,[v]:b}))},M=(z,v)=>{const{name:b,value:U}=z.target;n(re=>{const fe=[...re.customPrompts];return fe[v]={...fe[v],[b]:U},{...re,customPrompts:fe}})},G=()=>{!m.name||!m.prompt||(n(z=>({...z,customPrompts:[...z.customPrompts,{...m}]})),w({name:"",prompt:""}))},ce=z=>{n(v=>({...v,customPrompts:v.customPrompts.filter((b,U)=>U!==z)})),_===z&&E(null)},ue=z=>{E(z)},pe=()=>{E(null)},oe=()=>{E(null)},_e=async()=>{a(!0),d("");try{await Cy(t),window.reloadKeybindings&&await window.reloadKeybindings(),d("设置已保存！快捷键将在下次使用时生效。"),setTimeout(()=>d(""),5e3)}catch(z){console.error("保存设置出错:",z),d("保存设置失败，请重试。")}finally{a(!1)}},we=()=>{window.logseq&&window.logseq.hideMainUI()};return r?j("div",{className:"p-4 text-center",style:{color:f.getThemeColor("primary")},children:"加载中..."}):Z("div",{className:"p-4 max-w-2xl mx-auto theme-transition",style:{backgroundColor:f.getThemeColor("background"),color:f.getThemeColor("primary")},children:[Z("div",{className:"flex justify-between items-center mb-6",children:[j("h1",{className:"text-2xl font-bold",style:{color:f.getThemeColor("primary")},children:"AI 聊天设置"}),Z("div",{className:"flex items-center space-x-2",children:[e&&j("button",{onClick:e,className:"p-2 rounded-full transition-colors",style:{color:f.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:z=>{z.currentTarget.style.backgroundColor=f.getThemeColor("surface")},onMouseLeave:z=>{z.currentTarget.style.backgroundColor="transparent"},title:"返回聊天",children:j("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:j("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),j("button",{onClick:we,className:"p-2 rounded-full transition-colors",style:{color:f.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:z=>{z.currentTarget.style.backgroundColor=f.getThemeColor("surface")},onMouseLeave:z=>{z.currentTarget.style.backgroundColor="transparent"},title:"关闭设置",children:j("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:j("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),Z("div",{className:"mb-6",children:[j("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"界面设置"}),j("div",{className:"mb-4",children:j(_y,{})}),j("div",{className:"mb-4",children:j(Ey,{})})]}),Z("div",{className:"mb-6",children:[j("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"快捷键设置"}),Z("div",{className:"mb-4",children:[j("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"打开AI聊天"}),Z("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[Z("div",{children:[j("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Windows/Linux"}),j("input",{type:"text",value:t.keybindings.openChat.binding,onChange:z=>k("openChat","binding",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:S["openChat.binding"]?"#ef4444":f.getThemeColor("border")},placeholder:"ctrl+g"}),S["openChat.binding"]&&j("p",{className:"text-red-500 text-xs mt-1",children:S["openChat.binding"]}),T["openChat.binding"]&&j("p",{className:"text-yellow-500 text-xs mt-1",children:T["openChat.binding"]})]}),Z("div",{children:[j("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Mac"}),j("input",{type:"text",value:t.keybindings.openChat.mac,onChange:z=>k("openChat","mac",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:S["openChat.mac"]?"#ef4444":f.getThemeColor("border")},placeholder:"cmd+g"}),S["openChat.mac"]&&j("p",{className:"text-red-500 text-xs mt-1",children:S["openChat.mac"]}),T["openChat.mac"]&&j("p",{className:"text-yellow-500 text-xs mt-1",children:T["openChat.mac"]})]})]}),Z("div",{className:"mt-2",children:[j("span",{className:"text-sm",style:{color:f.getThemeColor("secondary")},children:"当前快捷键:"}),j("span",{className:"text-sm font-mono ml-1 px-2 py-1 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("accent")},children:Id(t.keybindings.openChat,g)})]})]}),Z("div",{className:"mb-4",children:[j("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"快速AI回复"}),Z("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[Z("div",{children:[j("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Windows/Linux"}),j("input",{type:"text",value:t.keybindings.quickReply.binding,onChange:z=>k("quickReply","binding",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:S["quickReply.binding"]?"#ef4444":f.getThemeColor("border")},placeholder:"ctrl+shift+g"}),S["quickReply.binding"]&&j("p",{className:"text-red-500 text-xs mt-1",children:S["quickReply.binding"]}),T["quickReply.binding"]&&j("p",{className:"text-yellow-500 text-xs mt-1",children:T["quickReply.binding"]})]}),Z("div",{children:[j("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Mac"}),j("input",{type:"text",value:t.keybindings.quickReply.mac,onChange:z=>k("quickReply","mac",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:S["quickReply.mac"]?"#ef4444":f.getThemeColor("border")},placeholder:"cmd+shift+g"}),S["quickReply.mac"]&&j("p",{className:"text-red-500 text-xs mt-1",children:S["quickReply.mac"]}),T["quickReply.mac"]&&j("p",{className:"text-yellow-500 text-xs mt-1",children:T["quickReply.mac"]})]})]}),Z("div",{className:"mt-2",children:[j("span",{className:"text-sm",style:{color:f.getThemeColor("secondary")},children:"当前快捷键:"}),j("span",{className:"text-sm font-mono ml-1 px-2 py-1 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("accent")},children:Id(t.keybindings.quickReply,g)})]})]}),Z("div",{className:"p-3 rounded-lg text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("secondary")},children:[j("p",{className:"mb-2",children:j("strong",{children:"快捷键格式说明："})}),Z("ul",{className:"list-disc list-inside space-y-1",children:[j("li",{children:"使用 + 号连接修饰键和按键，如：ctrl+g"}),j("li",{children:"支持的修饰键：ctrl, cmd, alt, shift, meta"}),j("li",{children:"按键可以是字母、数字或功能键"}),j("li",{children:"修改后需要重新加载插件才能生效"})]})]})]}),Z("div",{className:"mb-6",children:[j("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"API 配置"}),Z("div",{className:"mb-4",children:[j("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"API URL"}),j("input",{type:"text",name:"apiUrl",value:t.apiUrl,onChange:L,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"https://api.openai.com/v1/chat/completions"})]}),Z("div",{className:"mb-4",children:[j("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"API Key"}),j("input",{type:"password",name:"apiKey",value:t.apiKey,onChange:L,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"sk-..."})]}),Z("div",{className:"mb-4",children:[j("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"模型名称"}),j("input",{type:"text",name:"modelName",value:t.modelName,onChange:L,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"gpt-3.5-turbo"})]})]}),Z("div",{className:"mb-6",children:[j("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"历史记录设置"}),Z("div",{className:"flex items-center",children:[j("input",{type:"checkbox",id:"enableHistory",name:"enableHistory",checked:t.enableHistory,onChange:h,className:"mr-2",style:{accentColor:f.getThemeColor("accent")}}),j("label",{htmlFor:"enableHistory",className:"font-medium",style:{color:f.getThemeColor("primary")},children:"启用会话历史记录"})]}),j("p",{className:"text-sm mt-1",style:{color:f.getThemeColor("secondary")},children:"启用后，将在同一聊天会话中保存历史对话记录。"})]}),Z("div",{className:"mb-6",children:[j("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"自定义提示词"}),Z("div",{className:"p-4 rounded mb-4",style:{backgroundColor:f.getThemeColor("surface")},children:[j("h3",{className:"font-medium mb-2",style:{color:f.getThemeColor("primary")},children:"添加新提示词"}),Z("div",{className:"mb-3",children:[j("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("primary")},children:"提示词名称"}),j("input",{type:"text",name:"name",value:m.name,onChange:R,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("background"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"提示名称，如：总结、翻译等"})]}),Z("div",{className:"mb-3",children:[j("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("primary")},children:"提示词内容"}),j("textarea",{name:"prompt",value:m.prompt,onChange:R,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("background"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},rows:3,placeholder:"提示词内容，如：请总结以下内容的要点："})]}),j("button",{onClick:G,disabled:!m.name||!m.prompt,className:"apple-button px-3 py-1 rounded",style:{backgroundColor:!m.name||!m.prompt?f.getThemeColor("muted"):f.getThemeColor("accent"),color:"#ffffff",opacity:!m.name||!m.prompt?.5:1},children:"添加提示词"})]}),Z("div",{children:[j("h3",{className:"font-medium mb-2",style:{color:f.getThemeColor("primary")},children:"现有提示词"}),t.customPrompts.length===0?j("p",{style:{color:f.getThemeColor("secondary")},children:"暂无自定义提示词"}):j("div",{className:"space-y-3",children:t.customPrompts.map((z,v)=>j("div",{className:"rounded-lg overflow-hidden",style:{border:`1px solid ${f.getThemeColor("border")}`,backgroundColor:f.getThemeColor("surface")},children:_===v?Z("div",{className:"p-3",children:[Z("div",{className:"mb-2",children:[j("label",{className:"block mb-1 text-sm",children:"提示词名称"}),j("input",{type:"text",name:"name",value:z.name,onChange:b=>M(b,v),className:"w-full p-2 border border-gray-300 rounded"})]}),Z("div",{className:"mb-2",children:[j("label",{className:"block mb-1 text-sm",children:"提示词内容"}),j("textarea",{name:"prompt",value:z.prompt,onChange:b=>M(b,v),className:"w-full p-2 border border-gray-300 rounded",rows:3})]}),Z("div",{className:"flex space-x-2",children:[j("button",{onClick:oe,className:"px-2 py-1 bg-green-500 text-white text-sm rounded",children:"完成"}),j("button",{onClick:pe,className:"px-2 py-1 bg-gray-300 text-gray-700 text-sm rounded",children:"取消"})]})]}):Z("div",{className:"flex justify-between p-3",children:[Z("div",{children:[j("div",{className:"font-medium",style:{color:f.getThemeColor("primary")},children:z.name}),j("div",{className:"text-sm mt-1 line-clamp-2",style:{color:f.getThemeColor("secondary")},children:z.prompt})]}),Z("div",{className:"flex items-start space-x-2",children:[j("button",{onClick:()=>ue(v),className:"transition-colors",style:{color:f.getThemeColor("accent")},title:"编辑提示词",onMouseEnter:b=>{b.currentTarget.style.opacity="0.7"},onMouseLeave:b=>{b.currentTarget.style.opacity="1"},children:j("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:j("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),j("button",{onClick:()=>ce(v),className:"transition-colors",style:{color:"#EF4444"},title:"删除提示词",onMouseEnter:b=>{b.currentTarget.style.opacity="0.7"},onMouseLeave:b=>{b.currentTarget.style.opacity="1"},children:j("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:j("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},v))})]})]}),Z("div",{className:"mt-6 flex items-center",children:[j("button",{onClick:_e,disabled:i,className:"apple-button px-4 py-2 rounded font-medium",style:{backgroundColor:i?f.getThemeColor("muted"):"#10B981",color:"#ffffff",opacity:i?.5:1,cursor:i?"not-allowed":"pointer"},children:i?"保存中...":"保存设置"}),u&&j("span",{className:"ml-3",style:{color:"#10B981"},children:u})]})]})};class ul extends Error{constructor(t){super(t),this.name="ApiConnectionError"}}function Ny(e,t,n,r){const o=[];n?o.push({role:"system",content:n}):o.push({role:"system",content:"你是一个有用的助手，可以帮助用户处理和分析文本内容。提供简洁、准确的回答。"}),r&&r.length>0&&r.forEach(a=>{o.push({role:a.role,content:a.content})});let i=e;if(t&&t.content&&!e.includes(t.content)){let a="";switch(t.type){case"selection":a=`以下是选中的文本：

`;break;case"block":a=`以下是当前块的内容：

`;break;case"blocks":a=`以下是选中的多个块：

`;break}i=`${a}${t.content}

${e}`}return o.push({role:"user",content:i}),o}function sa(e){try{const t=e.split(`
`).filter(r=>r.trim()!==""&&r.trim()!=="data: [DONE]");let n="";for(const r of t)if(r.startsWith("data: ")){const o=r.replace(/^data: /,"");if(!o||o==="[DONE]")continue;try{const i=JSON.parse(o);i.choices&&i.choices[0]?i.choices[0].delta&&i.choices[0].delta.content?n+=i.choices[0].delta.content:i.choices[0].message&&i.choices[0].message.content&&(n+=i.choices[0].message.content):i.content?n+=i.content:i.text&&(n+=i.text)}catch{console.warn("解析 JSON 失败:",o)}}return n}catch(t){return console.error("解析数据块失败:",t),""}}async function Qp(e,t,n,r,o){try{const i=await ki();if(!i.apiUrl||!i.apiKey)throw new ul("API URL 或 API Key 未配置");const u={messages:Ny(e,t,r,o),stream:!0,model:i.modelName};i.temperature!==void 0&&(u.temperature=i.temperature),i.maxTokens!==void 0&&i.maxTokens>0&&(u.max_tokens=i.maxTokens);const d=await fetch(i.apiUrl,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i.apiKey}`},body:JSON.stringify(u)});if(!d.ok){let m=`API 请求失败: ${d.status} ${d.statusText}`;try{const w=await d.json();m=`${m}. ${w.error?.message||JSON.stringify(w)}`}catch{}throw new ul(m)}if(d.body){const m=d.body.getReader(),w=new TextDecoder("utf-8");let _="",E="",S=Date.now();const C=50;for(;;){const{done:T,value:O}=await m.read();if(T){if(E.length>0){const N=sa(E);N&&(_+=N,n.onChunk(N))}n.onComplete(_);break}const g=w.decode(O,{stream:!0});E+=g;const p=E.split(`

`),f=p.pop()||"";if(p.length>0){const N=sa(p.join(`

`));N&&(_+=N,n.onChunk(N))}E=f;const I=Date.now();if(I-S>=C&&E.includes("data: ")){const N=sa(E);N&&(_+=N,n.onChunk(N),E=""),S=I}}}else throw new ul("API 响应中没有正文")}catch(i){i instanceof ul?n.onError(i.message):n.onError(`请求失败: ${i instanceof Error?i.message:String(i)}`)}}function Kl(e){if(!e||typeof e!="string")return[];e=e.replace(/^>+\s/gm,"");const t=e.split(/\r?\n/),n=[];let r="",o=!1,i="",a=0;const u=t.filter(m=>m.trim().length>0).map(m=>{const w=m.match(/^(\s+)/);return w?w[1].length:0}).filter(m=>m>0);let d=2;if(u.length>0){const m=u.filter(w=>w>0);m.length>0&&(d=Math.min(...m))}for(let m=0;m<t.length;m++){const w=t[m],_=w.match(/^(\s+)/),E=_?_[1].length:0,S=Math.floor(E/d);if(w.trim().startsWith("```")){if(o){o=!1;const C=a>0?"  ".repeat(a):"";n.push(`${C}\`\`\``)}else{if(r.trim()){const g=a>0?"  ".repeat(a):"";n.push(`${g}${r.trim()}`),r=""}o=!0,i="    ";const C=w.trim().match(/^```(.*)$/),T=C?C[1].trim():"",O=S>0?"  ".repeat(S):"";T?r=`\`\`\` ${T}`:r="```",n.push(`${O}${r}`),r="",a=S}continue}if(o){const C=a>0?"  ".repeat(a):"";n.push(`${C}${i}${w.trimStart()}`);continue}if(w.trim().length>0&&(a=S),w.trim().match(/^(\d+\.|-|\*|\+)\s/)){if(r.trim()){const O=a>0?"  ".repeat(a):"";n.push(`${O}${r.trim()}`),r=""}const C=S>0?"  ".repeat(S):"",T=w.trim().replace(/^(\d+\.|-|\*|\+)\s+/,"");n.push(`${C}${T}`);continue}if(w.trim().match(/^#{1,6}\s/)){if(r.trim()){const T=a>0?"  ".repeat(a):"";n.push(`${T}${r.trim()}`),r=""}const C=S>0?"  ".repeat(S):"";n.push(`${C}${w.trim()}`);continue}if(!w.trim()){if(r.trim()){const C=a>0?"  ".repeat(a):"";n.push(`${C}${r.trim()}`),r=""}continue}r?r+=" "+w.trim():r=w.trim()}if(r.trim()){const m=a>0?"  ".repeat(a):"";n.push(`${m}${r.trim()}`)}return n}const by=({context:e,onClose:t,onReplace:n,onInsert:r,onOpenSettings:o})=>{const[i,a]=ne.useState(""),[u,d]=ne.useState(!1),[m,w]=ne.useState(""),[_,E]=ne.useState(null),[S,C]=ne.useState("custom"),[T,O]=ne.useState(""),[g,p]=ne.useState(!0),[f,I]=ne.useState(0),[N,L]=ne.useState([]),[h,k]=ne.useState(-1),[R,M]=ne.useState(!1),G=ne.useRef(null),ce=ne.useRef(null),ue=ne.useRef(null),pe=ne.useRef(null),oe=Nr(),[_e,we]=ne.useState(oe.isDarkMode());ne.useEffect(()=>{const Y=J=>{we(J.isDark)};return oe.addListener(Y),()=>{oe.removeListener(Y)}},[oe]),ne.useEffect(()=>{(async()=>{try{const J=await ki();E(J)}catch(J){console.error("加载设置出错:",J),w("无法加载插件设置，请检查配置")}})()},[]),ne.useEffect(()=>{S!=="default"&&S!=="custom"&&e?.content&&B(),S==="custom"?(p(!0),setTimeout(()=>{ce.current?.focus()},50)):p(!1)},[S]),ne.useEffect(()=>{g&&setTimeout(()=>{ce.current?.focus()},50)},[]),ne.useEffect(()=>{const Y=J=>{pe.current&&!pe.current.contains(J.target)&&M(!1)};return document.addEventListener("mousedown",Y),()=>{document.removeEventListener("mousedown",Y)}},[]);const z=Y=>{C(Y.target.value)},v=Y=>{const J=Y.target.value;if(O(J),_&&J.trim()!==""){const ve=_.customPrompts.filter(Ee=>Ee.name.toLowerCase().includes(J.toLowerCase()));L(ve),M(ve.length>0),k(-1)}else L([]),M(!1)},b=(Y,J)=>{const ve=`使用提示词: ${Y}`;O(ve),C("custom"),M(!1),setTimeout(()=>{B(J)},100)},U=Y=>{if(Y.key==="Escape"){M(!1);return}if(!R){Y.key==="Enter"&&T.trim()&&!u&&(Y.preventDefault(),B());return}if(Y.key==="ArrowDown")Y.preventDefault(),k(J=>J<N.length-1?J+1:J);else if(Y.key==="ArrowUp")Y.preventDefault(),k(J=>J>0?J-1:0);else if(Y.key==="Enter"&&h>=0){Y.preventDefault();const J=N[h];b(J.name,J.prompt)}},re=()=>{T.trim()&&!u&&B()},fe=Y=>{Y.key==="Enter"&&T.trim()&&!u&&!R&&(Y.preventDefault(),B())},B=async Y=>{if(!(!e?.content||u)){if(!_||!_.apiKey){w("请先在设置中配置API密钥");return}d(!0),w(""),a(""),I(0),M(!1);try{let J;if(Y)J=Y;else if(S==="custom"){if(!T.trim()){w("请输入自定义提示词"),d(!1);return}J=T}else if(S!=="default"&&_){const Ee=_.customPrompts.find(qe=>qe.name===S);Ee&&(J=Ee.prompt)}const ve={onChunk:Ee=>{a(qe=>{const dt=qe+Ee;return setTimeout(()=>{I(dt.length)},10),dt})},onComplete:Ee=>{d(!1),I(Ee.length)},onError:Ee=>{w(`API 请求失败: ${Ee}`),d(!1)}};await Qp(e.content,e,ve,J)}catch(J){console.error("发送消息出错:",J),w("发送消息失败，请重试"),d(!1)}}},Q=()=>i?Z("div",{className:"typewriter-text",children:[i.slice(0,f),u&&f===i.length&&j("span",{className:"blinking-cursor",children:"|"})]}):null,se=()=>_?Z(dm,{children:[j("option",{value:"custom",children:"自定义提示词"}),_.customPrompts.map((Y,J)=>j("option",{value:Y.name,children:Y.name},J))]}):null,Re=()=>!R||N.length===0?null:j("div",{ref:pe,className:"absolute z-10 left-4 right-16 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto",children:N.map((Y,J)=>j("div",{className:`px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${J===h?"bg-gray-100 dark:bg-gray-700":""}`,onClick:()=>b(Y.name,Y.prompt),children:j("div",{className:"font-medium text-gray-900 dark:text-white",children:Y.name})},J))}),Ve=async Y=>{try{if(Kl(Y).length===0||e.blockUUIDs.length===0){w("无法替换原文：无有效块数据或无原始块 UUID");return}n(Y)}catch(J){console.error("替换原文出错:",J),w("替换原文失败，请重试")}},lt=async Y=>{try{if(Kl(Y).length===0||e.blockUUIDs.length===0){w("无法插入子块：无有效块数据或无原始块 UUID");return}r(Y)}catch(J){console.error("插入子块出错:",J),w("插入子块失败，请重试")}};return Z("div",{ref:G,className:`apple-modal rounded-xl shadow-lg w-full flex flex-col ${oe.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",maxHeight:"60vh",border:`1px solid ${oe.getThemeColor("border")}`,backgroundColor:oe.getThemeColor("background"),color:oe.getThemeColor("primary")},children:[Z("div",{className:"p-4 border-b flex justify-between items-center",style:{borderColor:oe.getThemeColor("border")},children:[j("h2",{className:"text-lg font-medium",style:{color:oe.getThemeColor("primary")},children:"AI 聊天"}),Z("div",{className:"flex items-center",children:[j("select",{value:S,onChange:z,className:"apple-select mr-3 text-sm rounded-md border-none h-8 px-3",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary")},children:se()}),o&&j("button",{onClick:o,className:"h-8 w-8 flex items-center justify-center rounded-full transition-colors mr-2",style:{color:oe.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:Y=>{Y.currentTarget.style.backgroundColor=oe.getThemeColor("surface")},onMouseLeave:Y=>{Y.currentTarget.style.backgroundColor="transparent"},title:"打开设置",children:"⚙️"}),j("button",{onClick:t,className:"h-8 w-8 flex items-center justify-center rounded-full transition-colors",style:{color:oe.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:Y=>{Y.currentTarget.style.backgroundColor=oe.getThemeColor("surface")},onMouseLeave:Y=>{Y.currentTarget.style.backgroundColor="transparent"},title:"关闭",children:"✕"})]})]}),g&&Z("div",{className:"p-4 border-b relative",style:{borderColor:oe.getThemeColor("border")},children:[Z("div",{className:"flex items-center",children:[j("input",{ref:ce,type:"text",value:T,onChange:v,onKeyPress:fe,onKeyDown:U,placeholder:"输入提示词，支持自动补全",className:"apple-input flex-grow text-sm rounded-lg px-4 py-2",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary"),borderColor:oe.getThemeColor("border")}}),j("button",{onClick:re,disabled:!T.trim()||u,className:"apple-button ml-2 px-4 py-2 rounded-lg text-sm font-medium",style:{backgroundColor:!T.trim()||u?oe.getThemeColor("muted"):oe.getThemeColor("accent"),color:"#ffffff",opacity:!T.trim()||u?.5:1,cursor:!T.trim()||u?"not-allowed":"pointer"},children:"发送"})]}),Re()]}),Z("div",{ref:ue,className:"p-4 flex-grow overflow-y-auto max-h-[40vh] relative",children:[m&&j("div",{className:"mb-4 p-3 rounded-lg text-sm",style:{backgroundColor:"rgba(239, 68, 68, 0.1)",borderColor:"rgba(239, 68, 68, 0.3)",color:_e?"#FCA5A5":"#DC2626",border:"1px solid"},children:m}),u&&!i&&j("div",{className:"flex justify-center items-center py-8",children:j("div",{className:"apple-spinner w-6 h-6 border-2 rounded-full animate-spin",style:{borderColor:oe.getThemeColor("border"),borderTopColor:oe.getThemeColor("accent")}})}),i&&j("div",{className:"ai-response rounded-lg p-4 text-sm whitespace-pre-wrap relative",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary")},children:Q()})]}),i&&!u&&Z("div",{className:"p-3 border-t flex justify-end space-x-2",style:{borderColor:oe.getThemeColor("border")},children:[j("button",{onClick:()=>Ve(i),className:"apple-button-secondary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary"),border:`1px solid ${oe.getThemeColor("border")}`},onMouseEnter:Y=>{Y.currentTarget.style.backgroundColor=oe.getThemeColor("border")},onMouseLeave:Y=>{Y.currentTarget.style.backgroundColor=oe.getThemeColor("surface")},children:"替换原文"}),j("button",{onClick:()=>lt(i),className:"apple-button-primary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:oe.getThemeColor("accent"),color:"#ffffff"},onMouseEnter:Y=>{Y.currentTarget.style.opacity="0.9"},onMouseLeave:Y=>{Y.currentTarget.style.opacity="1"},children:"插入子块"})]})]})};class Jr{static instance;STORAGE_KEY="ailiaotian_chat_history";MAX_SESSIONS=50;MAX_MESSAGES_PER_SESSION=100;constructor(){}static getInstance(){return Jr.instance||(Jr.instance=new Jr),Jr.instance}getSessions(){try{const t=localStorage.getItem(this.STORAGE_KEY);return t?JSON.parse(t).sort((r,o)=>o.updatedAt-r.updatedAt):[]}catch(t){return console.error("获取聊天历史失败:",t),[]}}getSession(t){return this.getSessions().find(r=>r.id===t)||null}createSession(t){const n={id:this.generateId(),title:t||"新对话",createdAt:Date.now(),updatedAt:Date.now(),messages:[]},r=this.getSessions();return r.unshift(n),r.length>this.MAX_SESSIONS&&r.splice(this.MAX_SESSIONS),this.saveSessions(r),n}addMessage(t,n,r){const o=this.getSessions(),i=o.findIndex(d=>d.id===t);if(i===-1)throw new Error(`会话 ${t} 不存在`);const a={id:this.generateId(),role:n,content:r,timestamp:Date.now(),sessionId:t},u=o[i];return u.messages.push(a),u.updatedAt=Date.now(),u.messages.length>this.MAX_MESSAGES_PER_SESSION&&u.messages.splice(0,u.messages.length-this.MAX_MESSAGES_PER_SESSION),u.title==="新对话"&&n==="user"&&u.messages.length===1&&(u.title=this.generateSessionTitle(r)),this.saveSessions(o),a}updateSessionTitle(t,n){const r=this.getSessions(),o=r.find(i=>i.id===t);o&&(o.title=n,o.updatedAt=Date.now(),this.saveSessions(r))}deleteSession(t){const r=this.getSessions().filter(o=>o.id!==t);this.saveSessions(r)}clearAllHistory(){localStorage.removeItem(this.STORAGE_KEY)}getSessionContext(t,n=10){const r=this.getSession(t);return r?r.messages.slice(-n):[]}searchMessages(t){const n=this.getSessions(),r=[];return n.forEach(o=>{o.messages.forEach(i=>{i.content.toLowerCase().includes(t.toLowerCase())&&r.push({session:o,message:i})})}),r.sort((o,i)=>i.message.timestamp-o.message.timestamp)}exportHistory(){const t=this.getSessions();return JSON.stringify(t,null,2)}importHistory(t){try{const n=JSON.parse(t);if(!Array.isArray(n))throw new Error("无效的数据格式");const r=this.getSessions(),i=[...n,...r].filter((a,u,d)=>d.findIndex(m=>m.id===a.id)===u).slice(0,this.MAX_SESSIONS);this.saveSessions(i)}catch(n){throw console.error("导入聊天历史失败:",n),new Error("导入失败：数据格式错误")}}saveSessions(t){try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t))}catch(n){console.error("保存聊天历史失败:",n)}}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}generateSessionTitle(t){const n=t.trim().substring(0,30);return n.length<t.trim().length?n+"...":n}}const Py=()=>Jr.getInstance(),Iy=({onClose:e,onOpenSettings:t})=>{const[n,r]=ne.useState(null),[o,i]=ne.useState(!1),[a,u]=ne.useState(""),[d,m]=ne.useState(""),[w,_]=ne.useState(null),[E,S]=ne.useState([]),[C,T]=ne.useState(!0),[O,g]=ne.useState(""),[p,f]=ne.useState(!1),I=ne.useRef(null),N=ne.useRef(null),L=ne.useRef(null),h=Nr(),k=Py();ne.useEffect(()=>{R(),M(),setTimeout(()=>{L.current?.focus()},100)},[]),ne.useEffect(()=>{_e()},[w?.messages,O]);const R=async()=>{try{const v=await ki();r(v)}catch(v){console.error("初始化设置失败:",v),u("初始化失败，请检查设置")}},M=()=>{const v=k.getSessions();S(v),!w&&v.length===0?G():!w&&v.length>0&&_(v[0])},G=()=>{const v=k.createSession();_(v),S([v,...E])},ce=v=>{_(v),g(""),u("")},ue=v=>{k.deleteSession(v);const b=E.filter(U=>U.id!==v);S(b),w?.id===v&&(b.length>0?_(b[0]):G())},pe=async()=>{if(!d.trim()||!n||o)return;const v=d.trim();m(""),u(""),i(!0),f(!0),g("");try{let b=w;b||(b=k.createSession(),_(b),S(Q=>[b,...Q])),k.addMessage(b.id,"user",v);const U=k.getSession(b.id);U&&(_(U),oe(U));const fe=k.getSessionContext(b.id,10).map(Q=>({role:Q.role,content:Q.content}));await Qp(v,null,{onChunk:Q=>{g(se=>se+Q)},onComplete:Q=>{k.addMessage(b.id,"assistant",Q);const se=k.getSession(b.id);se&&(_(se),oe(se)),g(""),i(!1),f(!1)},onError:Q=>{u(`发送失败: ${Q}`),i(!1),f(!1),g("")}},void 0,fe)}catch(b){console.error("发送消息失败:",b),u("发送消息失败，请重试"),i(!1),f(!1),g("")}},oe=v=>{S(b=>b.map(U=>U.id===v.id?v:U))},_e=()=>{N.current?.scrollIntoView({behavior:"smooth"})},we=v=>{v.key==="Enter"&&!v.shiftKey&&(v.preventDefault(),pe())},z=v=>{const b=new Date(v),re=new Date().getTime()-b.getTime(),fe=Math.floor(re/(1e3*60*60*24));return fe===0?b.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):fe===1?"昨天":fe<7?`${fe}天前`:b.toLocaleDateString("zh-CN")};return n?Z("div",{ref:I,className:`apple-modal rounded-xl shadow-lg w-full flex ${h.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",height:"70vh",maxHeight:"600px",minHeight:"400px",border:`1px solid ${h.getThemeColor("border")}`,backgroundColor:h.getThemeColor("background"),color:h.getThemeColor("primary")},children:[C&&Z("div",{className:"w-64 border-r flex flex-col",style:{borderColor:h.getThemeColor("border")},children:[j("div",{className:"p-4 border-b",style:{borderColor:h.getThemeColor("border")},children:Z("div",{className:"flex items-center justify-between mb-3",children:[j("h3",{className:"font-semibold",children:"聊天历史"}),j("button",{onClick:G,className:"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:"新建对话",children:"➕"})]})}),j("div",{className:"flex-1 overflow-y-auto",children:E.map(v=>j("div",{className:`p-3 border-b cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${w?.id===v.id?"bg-blue-50 dark:bg-blue-900/20":""}`,style:{borderColor:h.getThemeColor("border")},onClick:()=>ce(v),children:Z("div",{className:"flex items-start justify-between",children:[Z("div",{className:"flex-1 min-w-0",children:[j("p",{className:"text-sm font-medium truncate",children:v.title}),j("p",{className:"text-xs text-gray-500 mt-1",children:z(v.updatedAt)})]}),j("button",{onClick:b=>{b.stopPropagation(),ue(v.id)},className:"ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors",title:"删除对话",children:"🗑️"})]})},v.id))})]}),Z("div",{className:"flex-1 flex flex-col",children:[Z("div",{className:"p-4 border-b flex items-center justify-between",style:{borderColor:h.getThemeColor("border")},children:[Z("div",{className:"flex items-center space-x-2",children:[j("button",{onClick:()=>T(!C),className:"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:C?"隐藏侧边栏":"显示侧边栏",children:C?"◀":"▶"}),j("h2",{className:"font-semibold",children:w?.title||"新对话"})]}),Z("div",{className:"flex items-center space-x-2",children:[t&&j("button",{onClick:t,className:"p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:"设置",children:"⚙️"}),j("button",{onClick:e,className:"p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:"关闭",children:"✕"})]})]}),Z("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[w?.messages.map(v=>j("div",{className:`flex ${v.role==="user"?"justify-end":"justify-start"}`,children:Z("div",{className:`max-w-[80%] p-3 rounded-lg ${v.role==="user"?"bg-blue-500 text-white":"bg-gray-100 dark:bg-gray-700"}`,children:[j("div",{className:"whitespace-pre-wrap break-words",children:v.content}),j("div",{className:`text-xs mt-1 opacity-70 ${v.role==="user"?"text-blue-100":"text-gray-500"}`,children:z(v.timestamp)})]})},v.id)),p&&O&&j("div",{className:"flex justify-start",children:Z("div",{className:"max-w-[80%] p-3 rounded-lg bg-gray-100 dark:bg-gray-700",children:[j("div",{className:"whitespace-pre-wrap break-words",children:O}),j("div",{className:"text-xs mt-1 opacity-70 text-gray-500",children:"正在输入..."})]})}),o&&!O&&j("div",{className:"flex justify-start",children:j("div",{className:"max-w-[80%] p-3 rounded-lg bg-gray-100 dark:bg-gray-700",children:Z("div",{className:"flex items-center space-x-2",children:[j("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"}),j("span",{children:"AI正在思考..."})]})})}),j("div",{ref:N})]}),a&&j("div",{className:"px-4 py-2 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800",children:j("p",{className:"text-red-600 dark:text-red-400 text-sm",children:a})}),j("div",{className:"p-4 border-t",style:{borderColor:h.getThemeColor("border")},children:Z("div",{className:"flex space-x-2",children:[j("textarea",{ref:L,value:d,onChange:v=>m(v.target.value),onKeyDown:we,placeholder:"输入消息... (Enter发送，Shift+Enter换行)",className:"flex-1 p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600",style:{minHeight:"44px",maxHeight:"120px",backgroundColor:h.getThemeColor("surface"),borderColor:h.getThemeColor("border"),color:h.getThemeColor("primary")},rows:1,disabled:o}),j("button",{onClick:pe,disabled:!d.trim()||o,className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:o?"发送中...":"发送"})]})})]})]}):j("div",{className:`apple-modal rounded-xl shadow-lg w-full h-96 flex items-center justify-center ${h.getThemeClasses()}`,children:Z("div",{className:"text-center",children:[j("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"}),j("p",{children:"加载中..."})]})})};function Oy({chatContext:e,openChatWindow:t}={}){const n=ne.useRef(null),r=wy(),[o,i]=ne.useState(t?"chat_window":"settings"),[a,u]=ne.useState(e),[d,m]=ne.useState(null),[w,_]=ne.useState(!1),E=Nr();ne.useEffect(()=>{e&&(u(e),i("chat"),setTimeout(S,50))},[e]),ne.useEffect(()=>{_(E.isDarkMode());const N=L=>{_(L.isDark)};return E.addListener(N),()=>{E.removeListener(N)}},[E]);const S=async()=>{if(!(!a||!a.blockUUIDs.length))try{const N=a.blockUUIDs[0],L=document.querySelector(`[blockid="${N}"]`);if(L){const h=L.getBoundingClientRect();m({top:h.bottom,left:h.left,width:h.width})}}catch(N){console.error("获取块位置失败:",N)}},C=async N=>{if(!(!a||!a.blockUUIDs.length))try{const L=Kl(N);for(const R of a.blockUUIDs)await logseq.Editor.removeBlock(R);const h=a.blockUUIDs[0],k=await logseq.Editor.getBlock(h);if(!k){console.error("无法获取块信息");return}if(k.parent){const R=k.parent.id||k.parent.uuid;for(const M of L)await logseq.Editor.insertBlock(R,M,{sibling:!0,before:!1})}else if(k.page){const R=k.page.originalName||k.page.name;for(const M of L)await logseq.Editor.insertBlock(R,M)}logseq.hideMainUI()}catch(L){console.error("替换内容失败:",L)}},T=async N=>{if(!(!a||!a.blockUUIDs.length))try{const L=Kl(N),h=a.blockUUIDs[0],k=O(L);await g(h,k),logseq.hideMainUI()}catch(L){console.error("插入内容失败:",L)}},O=N=>{if(!N.length)return[];const L=[],h=[];for(const k of N){const R=k.match(/^(\s+)/),M=R?Math.floor(R[1].length/2):0,ue={content:k.trimStart().replace(/^(\d+\.|-|\*|\+)\s+/,"")};if(h.length===0)L.push(ue),h.push({node:ue,indent:M});else{for(;h.length>0&&h[h.length-1].indent>=M;)h.pop();if(h.length===0)L.push(ue);else{const pe=h[h.length-1].node;pe.children||(pe.children=[]),pe.children.push(ue)}h.push({node:ue,indent:M})}}return L},g=async(N,L)=>{for(const h of L){const k=await logseq.Editor.insertBlock(N,h.content,{sibling:!1});h.children&&h.children.length>0&&k&&await g(k.uuid,h.children)}},p=()=>{logseq.hideMainUI()},f=()=>{i("settings")},I=()=>{a?i("chat"):logseq.hideMainUI()};return r?Z("main",{className:"fixed inset-0 z-50 flex items-center justify-center",style:{backdropFilter:"none",WebkitBackdropFilter:"none",backgroundColor:"transparent"},onClick:N=>{n.current?.contains(N.target)||window.logseq.hideMainUI()},children:[o==="settings"&&j("div",{ref:n,className:`apple-modal rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto pointer-events-auto mx-auto ${E.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",backgroundColor:E.getThemeColor("background"),color:E.getThemeColor("primary")},onClick:N=>N.stopPropagation(),children:j(Ty,{onBackToChat:a?I:void 0})}),o==="chat"&&a&&j("div",{ref:n,className:"pointer-events-auto",style:d?{position:"absolute",top:`${d.top+10}px`,left:`${d.left}px`,zIndex:9999,maxWidth:"700px",width:`${Math.max(580,Math.min(d.width*1.5,700))}px`}:{maxWidth:"700px",width:"100%"},onClick:N=>N.stopPropagation(),children:j(by,{context:a,onClose:p,onReplace:C,onInsert:T,onOpenSettings:f})}),o==="chat_window"&&j("div",{ref:n,className:"pointer-events-auto",style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:9999,width:"90vw",maxWidth:"1000px",minWidth:"600px"},onClick:N=>N.stopPropagation(),children:j(Iy,{onClose:p,onOpenSettings:f})})]}):null}async function Ly(){try{const e=await logseq.Editor.getEditingCursorPosition();if(e&&e.pos){const t=await logseq.Editor.getCurrentBlock();if(t){const n=t.content,r=e.pos;if(r.start!==r.end)return n.substring(r.start,r.end)}}return null}catch(e){return console.error("获取选中文本失败:",e),null}}async function My(){try{return await logseq.Editor.getSelectedBlocks()||[]}catch(e){return console.error("获取选中块失败:",e),[]}}async function Od(){try{return await logseq.Editor.getCurrentBlock()}catch(e){return console.error("获取当前块失败:",e),null}}async function eu(){const e=await Ly();if(e){const r=await Od();return{type:"selection",content:e,blockUUIDs:r?[r.uuid]:[]}}const t=await My();if(t&&t.length>0)return{type:"blocks",content:t.map(o=>o.content).join(`

`),blockUUIDs:t.map(o=>o.uuid)};const n=await Od();return n?{type:"block",content:n.content,blockUUIDs:[n.uuid]}:{type:"none",content:"",blockUUIDs:[]}}const Ay={id:"logseq-plugin-ai-chat",icon:"./logo.svg",title:"AI 聊天助手",description:"在Logseq中使用自定义API进行AI聊天，支持智能上下文感知和完整暗色模式"},jy=(e,...t)=>String.raw(e,...t),Gp=Ay.id,Ry=qp(document.getElementById("app"));let wr,io=!1,zo=[];async function Yp(){try{const e=await ki();for(const o of zo)try{console.log(`尝试清除命令: ${o}`)}catch(i){console.warn(`清除命令失败: ${o}`,i)}zo=[];const t="open-ai-chat";logseq.App.registerCommandPalette({key:t,label:e.keybindings.openChat.description,keybinding:{binding:e.keybindings.openChat.binding,mac:e.keybindings.openChat.mac}},async()=>{const o=await eu();console.log("获取到的上下文:",o),wr=o,gr(),logseq.showMainUI()}),zo.push(t);const n="quick-ai-reply";logseq.App.registerCommandPalette({key:n,label:e.keybindings.quickReply.description,keybinding:{binding:e.keybindings.quickReply.binding,mac:e.keybindings.quickReply.mac}},async()=>{const o=await eu();console.log("快速回复上下文:",o),wr=o,gr(),logseq.showMainUI()}),zo.push(n);const r="open-chat-window";logseq.App.registerCommandPalette({key:r,label:e.keybindings.openChatWindow.description,keybinding:{binding:e.keybindings.openChatWindow.binding,mac:e.keybindings.openChatWindow.mac}},async()=>{console.log("打开聊天窗口"),wr=void 0,io=!0,gr(),logseq.showMainUI()}),zo.push(r),console.log("快捷键注册完成:",e.keybindings)}catch(e){console.error("注册快捷键失败:",e)}}async function Dy(){console.log("重新加载快捷键..."),await Yp()}async function zy(){console.info(`#${Gp}: MAIN`),await ky(),await Fy(),logseq.useSettingsSchema([{key:"apiUrl",type:"string",default:"https://api.openai.com/v1/chat/completions",title:"API URL",description:"AI服务的API地址"},{key:"apiKey",type:"string",default:"",title:"API Key",description:"访问AI服务所需的API密钥"},{key:"modelName",type:"string",default:"gpt-3.5-turbo",title:"模型名称",description:"使用的AI模型名称"},{key:"enableHistory",type:"boolean",default:!1,title:"启用历史记录",description:"是否保存聊天历史记录"}]),gr();function e(){return{show(){wr=void 0,io=!1,gr(),logseq.showMainUI()},async openAIChat(){const r=await eu();console.log("获取到的上下文:",r),wr=r,io=!1,gr(),logseq.showMainUI()},openChatWindow(){console.log("打开聊天窗口"),wr=void 0,io=!0,gr(),logseq.showMainUI()}}}const t=e();logseq.provideModel(t),logseq.setMainUIInlineStyle({zIndex:9}),logseq.Editor.registerSlashCommand("AI聊天",async()=>t.openAIChat()),logseq.Editor.registerSlashCommand("聊天窗口",async()=>t.openChatWindow()),await Yp(),window.reloadKeybindings=Dy;const n="ai-chat-plugin-open";logseq.provideStyle(jy`
    .${n} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${n}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `),logseq.App.registerUIItem("toolbar",{key:n,template:`
    <a data-on-click="show">
        <div class="${n}">🤖</div>
    </a>    
`})}function gr(){Ry.render(j(Bd.StrictMode,{children:j(Oy,{chatContext:wr,openChatWindow:io})})),io=!1}async function Fy(){try{const e=Nr();console.info(`#${Gp}: 主题管理器已初始化`)}catch(e){console.error("主题初始化失败:",e)}}logseq.ready(zy).catch(console.error);
