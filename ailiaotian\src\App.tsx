import React, { useRef, useState, useEffect } from "react";
import { useAppVisible } from "./utils";
import { SettingsUI } from "./components/SettingsUI";
import { ChatModal } from "./components/ChatModal";
import { ChatWindow } from "./components/ChatWindow";
import { ContextData, ContextSourceType } from "./contextManager";
import { formatResponseToBlocks, prepareBlocksForInsertion, BlockEntity } from "./formatter";
import { getThemeManager } from "./themeManager";

// 应用页面类型
enum AppPageType {
  SETTINGS = 'settings',
  CHAT = 'chat',
  CHAT_WINDOW = 'chat_window'
}

// 应用程序属性
interface AppProps {
  chatContext?: ContextData;
  openChatWindow?: boolean; // 新增：是否直接打开聊天窗口
}

function App({ chatContext, openChatWindow }: AppProps = {}) {
  const innerRef = useRef<HTMLDivElement>(null);
  const visible = useAppVisible();
  const [currentPage, setCurrentPage] = useState<AppPageType>(
    openChatWindow ? AppPageType.CHAT_WINDOW : AppPageType.SETTINGS
  );
  const [context, setContext] = useState<ContextData | undefined>(chatContext);
  const [blockPosition, setBlockPosition] = useState<{top: number, left: number, width: number} | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // 主题管理
  const themeManager = getThemeManager();
  
  // 当chatContext变化时更新state
  useEffect(() => {
    if (chatContext) {
      setContext(chatContext);
      setCurrentPage(AppPageType.CHAT);
      // 获取块的位置
      setTimeout(getBlockPosition, 50);
    }
  }, [chatContext]);

  // 主题监听
  useEffect(() => {
    // 初始化主题状态
    setIsDarkMode(themeManager.isDarkMode());

    // 监听主题变化
    const handleThemeChange = (theme: any) => {
      setIsDarkMode(theme.isDark);
    };

    themeManager.addListener(handleThemeChange);

    // 清理监听器
    return () => {
      themeManager.removeListener(handleThemeChange);
    };
  }, [themeManager]);
  
  // 获取块的位置
  const getBlockPosition = async () => {
    if (!context || !context.blockUUIDs.length) return;
    
    try {
      const blockUUID = context.blockUUIDs[0];
      // 使用DOM方法获取块的位置
      const blockElement = document.querySelector(`[blockid="${blockUUID}"]`);
      if (blockElement) {
        const rect = blockElement.getBoundingClientRect();
        setBlockPosition({
          top: rect.bottom,
          left: rect.left,
          width: rect.width
        });
      }
    } catch (error) {
      console.error('获取块位置失败:', error);
    }
  };
  
  // 处理替换原文
  const handleReplace = async (content: string) => {
    if (!context || !context.blockUUIDs.length) return;
    
    try {
      // 格式化 AI 响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(content);
      
      // 删除原始块
      for (const uuid of context.blockUUIDs) {
        await logseq.Editor.removeBlock(uuid);
      }
      
      // 获取第一个块的父块信息
      const firstUUID = context.blockUUIDs[0];
      const firstBlock = await logseq.Editor.getBlock(firstUUID);
      
      if (!firstBlock) {
        console.error('无法获取块信息');
        return;
      }
      
      // 确定插入位置
      if (firstBlock.parent) {
        // 如果有父块，在父块下插入
        const parentUUID = firstBlock.parent.id || firstBlock.parent.uuid;
        
        // 插入块
        for (const blockContent of formattedBlocks) {
          await logseq.Editor.insertBlock(parentUUID, blockContent, {
            sibling: true,
            before: false
          });
        }
      } else if (firstBlock.page) {
        // 如果是顶级块，在页面中插入
        const pageName = firstBlock.page.originalName || firstBlock.page.name;
        
        // 插入块
        for (const blockContent of formattedBlocks) {
          await logseq.Editor.insertBlock(pageName, blockContent);
        }
      }
      
      // 关闭UI
      logseq.hideMainUI();
    } catch (error) {
      console.error('替换内容失败:', error);
    }
  };
  
  // 处理插入子块
  const handleInsert = async (content: string) => {
    if (!context || !context.blockUUIDs.length) return;
    
    try {
      // 格式化 AI 响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(content);
      
      // 获取第一个块的UUID
      const parentUUID = context.blockUUIDs[0];
      
      // 构建块结构树，处理缩进关系
      const blockTree = buildBlockTree(formattedBlocks);
      
      // 递归插入块树
      await insertBlockTree(parentUUID, blockTree);
      
      // 关闭UI
      logseq.hideMainUI();
    } catch (error) {
      console.error('插入内容失败:', error);
    }
  };
  
  // 根据缩进构建块结构树
  const buildBlockTree = (blocks: string[]): BlockEntity[] => {
    if (!blocks.length) return [];
    
    const result: BlockEntity[] = [];
    const stack: {node: BlockEntity, indent: number}[] = [];
    
    for (const block of blocks) {
      // 获取缩进级别
      const indentMatch = block.match(/^(\s+)/);
      const indent = indentMatch ? Math.floor(indentMatch[1].length / 2) : 0;
      
      // 创建新块对象（去除前导空格）
      const content = block.trimStart();
      // 如果内容以 "-"、"*"、"+" 或数字+点开始，移除这个标记
      const cleanContent = content.replace(/^(\d+\.|-|\*|\+)\s+/, '');
      
      const newNode: BlockEntity = {
        content: cleanContent,
      };
      
      if (stack.length === 0) {
        // 根级别块
        result.push(newNode);
        stack.push({ node: newNode, indent });
      } else {
        // 找到合适的父节点
        while (stack.length > 0 && stack[stack.length - 1].indent >= indent) {
          stack.pop();
        }
        
        if (stack.length === 0) {
          // 如果没有找到合适的父节点，添加到根级别
          result.push(newNode);
        } else {
          // 将当前块添加为上一级的子块
          const parent = stack[stack.length - 1].node;
          if (!parent.children) {
            parent.children = [];
          }
          parent.children.push(newNode);
        }
        
        stack.push({ node: newNode, indent });
      }
    }
    
    return result;
  };
  
  // 递归插入块树
  const insertBlockTree = async (parentUUID: string, blocks: BlockEntity[]) => {
    for (const block of blocks) {
      // 插入当前块
      const insertedBlock = await logseq.Editor.insertBlock(
        parentUUID,
        block.content,
        { sibling: false }
      );
      
      // 如果有子块且插入成功了，递归插入子块
      if (block.children && block.children.length > 0 && insertedBlock) {
        await insertBlockTree(insertedBlock.uuid, block.children);
      }
    }
  };
  
  // 处理模态框关闭
  const handleClose = () => {
    logseq.hideMainUI();
  };

  // 切换到设置页面
  const handleOpenSettings = () => {
    setCurrentPage(AppPageType.SETTINGS);
  };

  // 返回聊天页面
  const handleBackToChat = () => {
    if (context) {
      setCurrentPage(AppPageType.CHAT);
    } else {
      // 如果没有上下文，关闭UI
      logseq.hideMainUI();
    }
  };

  // 打开聊天窗口
  const handleOpenChatWindow = () => {
    setCurrentPage(AppPageType.CHAT_WINDOW);
  };
  
  if (!visible) {
    return null;
  }
  
  return (
    <main
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{
        backdropFilter: 'none',
        WebkitBackdropFilter: 'none',
        backgroundColor: 'transparent'
      }}
      onClick={(e) => {
        if (!innerRef.current?.contains(e.target as any)) {
          window.logseq.hideMainUI();
        }
      }}
    >
      {currentPage === AppPageType.SETTINGS && (
        <div
          ref={innerRef}
          className={`apple-modal rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto pointer-events-auto mx-auto ${themeManager.getThemeClasses()}`}
          style={{
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            backgroundColor: themeManager.getThemeColor('background'),
            color: themeManager.getThemeColor('primary'),
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <SettingsUI onBackToChat={context ? handleBackToChat : undefined} />
        </div>
      )}
      
      {currentPage === AppPageType.CHAT && context && (
        <div
          ref={innerRef}
          className="pointer-events-auto"
          style={blockPosition ? {
            position: 'absolute',
            top: `${blockPosition.top + 10}px`,
            left: `${blockPosition.left}px`,
            zIndex: 9999,
            maxWidth: '700px',
            width: `${Math.max(580, Math.min(blockPosition.width * 1.5, 700))}px`
          } : {
            maxWidth: '700px',
            width: '100%'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <ChatModal
            context={context}
            onClose={handleClose}
            onReplace={handleReplace}
            onInsert={handleInsert}
            onOpenSettings={handleOpenSettings}
          />
        </div>
      )}

      {currentPage === AppPageType.CHAT_WINDOW && (
        <div
          ref={innerRef}
          className="pointer-events-auto"
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9999,
            width: '90vw',
            maxWidth: '1000px',
            minWidth: '600px'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <ChatWindow
            onClose={handleClose}
            onOpenSettings={handleOpenSettings}
          />
        </div>
      )}
    </main>
  );
}

export default App;
