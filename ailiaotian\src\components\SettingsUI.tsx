import React, { useEffect, useState } from "react";
import {
  PluginSettings,
  DEFAULT_SETTINGS,
  updateSettings,
  getSettings,
  KeybindingConfig,
  validateKeybinding,
  formatKeybindingDisplay,
  checkKeybindingConflict
} from "../settings";
import { getThemeManager } from "../themeManager";
import { ThemeToggle, ThemeIndicator } from "./ThemeToggle";

interface CustomPrompt {
  name: string;
  prompt: string;
}

interface SettingsUIProps {
  onBackToChat?: () => void;
}

/**
 * 设置页面组件
 */
export const SettingsUI: React.FC<SettingsUIProps> = ({ onBackToChat }) => {
  // 设置状态
  const [settings, setSettings] = useState<PluginSettings>(DEFAULT_SETTINGS);
  // 加载状态
  const [isLoading, setIsLoading] = useState(true);
  // 保存状态
  const [isSaving, setIsSaving] = useState(false);
  // 保存成功消息
  const [saveMessage, setSaveMessage] = useState("");
  // 新提示状态
  const [newPrompt, setNewPrompt] = useState<CustomPrompt>({ name: "", prompt: "" });
  // 当前编辑的提示索引
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  // 快捷键验证错误
  const [keybindingErrors, setKeybindingErrors] = useState<{[key: string]: string}>({});
  // 快捷键警告
  const [keybindingWarnings, setKeybindingWarnings] = useState<{[key: string]: string}>({});
  // 是否为Mac系统
  const [isMac, setIsMac] = useState(false);

  // 主题管理
  const themeManager = getThemeManager();
  const [isDarkMode, setIsDarkMode] = useState(themeManager.isDarkMode());

  // 主题监听
  useEffect(() => {
    const handleThemeChange = (theme: any) => {
      setIsDarkMode(theme.isDark);
    };

    themeManager.addListener(handleThemeChange);

    return () => {
      themeManager.removeListener(handleThemeChange);
    };
  }, [themeManager]);

  // 检测操作系统
  useEffect(() => {
    setIsMac(navigator.platform.toUpperCase().indexOf('MAC') >= 0);
  }, []);

  // 组件挂载时加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const currentSettings = await getSettings();
        setSettings(currentSettings);
      } catch (error) {
        console.error("加载设置出错:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // 监听ESC键关闭窗口
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);

    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // 处理文本输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings((prev) => ({ ...prev, [name]: value }));
  };

  // 处理切换按钮变化
  const handleToggleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSettings((prev) => ({ ...prev, [name]: checked }));
  };

  // 处理快捷键输入变化
  const handleKeybindingChange = (
    keybindingType: 'openChat' | 'quickReply' | 'openChatWindow',
    platform: 'binding' | 'mac',
    value: string
  ) => {
    const newKeybinding = {
      ...settings.keybindings[keybindingType],
      [platform]: value
    };

    // 验证快捷键格式
    const validation = validateKeybinding(value);
    const errorKey = `${keybindingType}.${platform}`;

    if (!validation.isValid && value.trim() !== '') {
      setKeybindingErrors(prev => ({
        ...prev,
        [errorKey]: validation.error || '快捷键格式无效'
      }));
    } else {
      setKeybindingErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    }

    // 检查冲突
    const conflictCheck = checkKeybindingConflict(value, settings.keybindings);
    if (conflictCheck.hasConflict) {
      setKeybindingWarnings(prev => ({
        ...prev,
        [errorKey]: conflictCheck.message || '存在快捷键冲突'
      }));
    } else {
      setKeybindingWarnings(prev => {
        const newWarnings = { ...prev };
        delete newWarnings[errorKey];
        return newWarnings;
      });
    }

    // 更新设置
    setSettings(prev => ({
      ...prev,
      keybindings: {
        ...prev.keybindings,
        [keybindingType]: newKeybinding
      }
    }));
  };

  // 处理新提示输入变化
  const handleNewPromptChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewPrompt((prev) => ({ ...prev, [name]: value }));
  };

  // 处理编辑提示输入变化
  const handleEditPromptChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {
    const { name, value } = e.target;
    setSettings((prev) => {
      const updatedPrompts = [...prev.customPrompts];
      updatedPrompts[index] = { ...updatedPrompts[index], [name]: value };
      return { ...prev, customPrompts: updatedPrompts };
    });
  };

  // 添加新提示
  const handleAddPrompt = () => {
    if (!newPrompt.name || !newPrompt.prompt) {
      return;
    }

    setSettings((prev) => ({
      ...prev,
      customPrompts: [...prev.customPrompts, { ...newPrompt }],
    }));

    setNewPrompt({ name: "", prompt: "" });
  };

  // 删除提示
  const handleDeletePrompt = (index: number) => {
    setSettings((prev) => ({
      ...prev,
      customPrompts: prev.customPrompts.filter((_, i) => i !== index),
    }));
    
    // 如果正在编辑的提示被删除，重置编辑状态
    if (editingIndex === index) {
      setEditingIndex(null);
    }
  };

  // 开始编辑提示
  const handleStartEditing = (index: number) => {
    setEditingIndex(index);
  };

  // 取消编辑提示
  const handleCancelEditing = () => {
    setEditingIndex(null);
  };

  // 保存编辑后的提示
  const handleSaveEditing = () => {
    setEditingIndex(null);
  };

  // 保存设置
  const handleSaveSettings = async () => {
    setIsSaving(true);
    setSaveMessage("");

    try {
      await updateSettings(settings);

      // 重新加载快捷键
      if ((window as any).reloadKeybindings) {
        await (window as any).reloadKeybindings();
      }

      setSaveMessage("设置已保存！快捷键将在下次使用时生效。");
      setTimeout(() => setSaveMessage(""), 5000);
    } catch (error) {
      console.error("保存设置出错:", error);
      setSaveMessage("保存设置失败，请重试。");
    } finally {
      setIsSaving(false);
    }
  };

  // 关闭设置窗口
  const handleClose = () => {
    if (window.logseq) {
      window.logseq.hideMainUI();
    }
  };

  if (isLoading) {
    return (
      <div
        className="p-4 text-center"
        style={{ color: themeManager.getThemeColor('primary') }}
      >
        加载中...
      </div>
    );
  }

  return (
    <div
      className="p-4 max-w-2xl mx-auto theme-transition"
      style={{
        backgroundColor: themeManager.getThemeColor('background'),
        color: themeManager.getThemeColor('primary')
      }}
    >
      <div className="flex justify-between items-center mb-6">
        <h1
          className="text-2xl font-bold"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          AI 聊天设置
        </h1>
        <div className="flex items-center space-x-2">
          {/* 返回聊天按钮 */}
          {onBackToChat && (
            <button
              onClick={onBackToChat}
              className="p-2 rounded-full transition-colors"
              style={{
                color: themeManager.getThemeColor('secondary'),
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = themeManager.getThemeColor('surface');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
              title="返回聊天"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </button>
          )}

          <button
            onClick={handleClose}
            className="p-2 rounded-full transition-colors"
            style={{
              color: themeManager.getThemeColor('secondary'),
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = themeManager.getThemeColor('surface');
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            title="关闭设置"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* 主题设置 */}
      <div className="mb-6">
        <h2
          className="text-xl font-semibold mb-3"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          界面设置
        </h2>

        <div className="mb-4">
          <ThemeToggle />
        </div>

        <div className="mb-4">
          <ThemeIndicator />
        </div>
      </div>

      {/* 快捷键设置 */}
      <div className="mb-6">
        <h2
          className="text-xl font-semibold mb-3"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          快捷键设置
        </h2>

        {/* 打开AI聊天快捷键 */}
        <div className="mb-4">
          <label
            className="block mb-2 font-medium"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            打开AI聊天
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Windows/Linux 快捷键 */}
            <div>
              <label
                className="block mb-1 text-sm"
                style={{ color: themeManager.getThemeColor('secondary') }}
              >
                Windows/Linux
              </label>
              <input
                type="text"
                value={settings.keybindings.openChat.binding}
                onChange={(e) => handleKeybindingChange('openChat', 'binding', e.target.value)}
                className="apple-input w-full p-2 rounded text-sm"
                style={{
                  backgroundColor: themeManager.getThemeColor('surface'),
                  color: themeManager.getThemeColor('primary'),
                  borderColor: keybindingErrors['openChat.binding']
                    ? '#ef4444'
                    : themeManager.getThemeColor('border')
                }}
                placeholder="ctrl+g"
              />
              {keybindingErrors['openChat.binding'] && (
                <p className="text-red-500 text-xs mt-1">
                  {keybindingErrors['openChat.binding']}
                </p>
              )}
              {keybindingWarnings['openChat.binding'] && (
                <p className="text-yellow-500 text-xs mt-1">
                  {keybindingWarnings['openChat.binding']}
                </p>
              )}
            </div>

            {/* Mac 快捷键 */}
            <div>
              <label
                className="block mb-1 text-sm"
                style={{ color: themeManager.getThemeColor('secondary') }}
              >
                Mac
              </label>
              <input
                type="text"
                value={settings.keybindings.openChat.mac}
                onChange={(e) => handleKeybindingChange('openChat', 'mac', e.target.value)}
                className="apple-input w-full p-2 rounded text-sm"
                style={{
                  backgroundColor: themeManager.getThemeColor('surface'),
                  color: themeManager.getThemeColor('primary'),
                  borderColor: keybindingErrors['openChat.mac']
                    ? '#ef4444'
                    : themeManager.getThemeColor('border')
                }}
                placeholder="cmd+g"
              />
              {keybindingErrors['openChat.mac'] && (
                <p className="text-red-500 text-xs mt-1">
                  {keybindingErrors['openChat.mac']}
                </p>
              )}
              {keybindingWarnings['openChat.mac'] && (
                <p className="text-yellow-500 text-xs mt-1">
                  {keybindingWarnings['openChat.mac']}
                </p>
              )}
            </div>
          </div>

          {/* 当前系统快捷键预览 */}
          <div className="mt-2">
            <span
              className="text-sm"
              style={{ color: themeManager.getThemeColor('secondary') }}
            >
              当前快捷键:
            </span>
            <span
              className="text-sm font-mono ml-1 px-2 py-1 rounded"
              style={{
                backgroundColor: themeManager.getThemeColor('surface'),
                color: themeManager.getThemeColor('accent')
              }}
            >
              {formatKeybindingDisplay(settings.keybindings.openChat, isMac)}
            </span>
          </div>
        </div>

        {/* 快速AI回复快捷键 */}
        <div className="mb-4">
          <label
            className="block mb-2 font-medium"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            快速AI回复
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Windows/Linux 快捷键 */}
            <div>
              <label
                className="block mb-1 text-sm"
                style={{ color: themeManager.getThemeColor('secondary') }}
              >
                Windows/Linux
              </label>
              <input
                type="text"
                value={settings.keybindings.quickReply.binding}
                onChange={(e) => handleKeybindingChange('quickReply', 'binding', e.target.value)}
                className="apple-input w-full p-2 rounded text-sm"
                style={{
                  backgroundColor: themeManager.getThemeColor('surface'),
                  color: themeManager.getThemeColor('primary'),
                  borderColor: keybindingErrors['quickReply.binding']
                    ? '#ef4444'
                    : themeManager.getThemeColor('border')
                }}
                placeholder="ctrl+shift+g"
              />
              {keybindingErrors['quickReply.binding'] && (
                <p className="text-red-500 text-xs mt-1">
                  {keybindingErrors['quickReply.binding']}
                </p>
              )}
              {keybindingWarnings['quickReply.binding'] && (
                <p className="text-yellow-500 text-xs mt-1">
                  {keybindingWarnings['quickReply.binding']}
                </p>
              )}
            </div>

            {/* Mac 快捷键 */}
            <div>
              <label
                className="block mb-1 text-sm"
                style={{ color: themeManager.getThemeColor('secondary') }}
              >
                Mac
              </label>
              <input
                type="text"
                value={settings.keybindings.quickReply.mac}
                onChange={(e) => handleKeybindingChange('quickReply', 'mac', e.target.value)}
                className="apple-input w-full p-2 rounded text-sm"
                style={{
                  backgroundColor: themeManager.getThemeColor('surface'),
                  color: themeManager.getThemeColor('primary'),
                  borderColor: keybindingErrors['quickReply.mac']
                    ? '#ef4444'
                    : themeManager.getThemeColor('border')
                }}
                placeholder="cmd+shift+g"
              />
              {keybindingErrors['quickReply.mac'] && (
                <p className="text-red-500 text-xs mt-1">
                  {keybindingErrors['quickReply.mac']}
                </p>
              )}
              {keybindingWarnings['quickReply.mac'] && (
                <p className="text-yellow-500 text-xs mt-1">
                  {keybindingWarnings['quickReply.mac']}
                </p>
              )}
            </div>
          </div>

          {/* 当前系统快捷键预览 */}
          <div className="mt-2">
            <span
              className="text-sm"
              style={{ color: themeManager.getThemeColor('secondary') }}
            >
              当前快捷键:
            </span>
            <span
              className="text-sm font-mono ml-1 px-2 py-1 rounded"
              style={{
                backgroundColor: themeManager.getThemeColor('surface'),
                color: themeManager.getThemeColor('accent')
              }}
            >
              {formatKeybindingDisplay(settings.keybindings.quickReply, isMac)}
            </span>
          </div>
        </div>

        {/* 打开聊天窗口快捷键 */}
        <div className="mb-4">
          <label
            className="block mb-2 font-medium"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            打开聊天窗口
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Windows/Linux 快捷键 */}
            <div>
              <label
                className="block mb-1 text-sm"
                style={{ color: themeManager.getThemeColor('secondary') }}
              >
                Windows/Linux
              </label>
              <input
                type="text"
                value={settings.keybindings.openChatWindow.binding}
                onChange={(e) => handleKeybindingChange('openChatWindow', 'binding', e.target.value)}
                className="apple-input w-full p-2 rounded text-sm"
                style={{
                  backgroundColor: themeManager.getThemeColor('surface'),
                  color: themeManager.getThemeColor('primary'),
                  borderColor: keybindingErrors['openChatWindow.binding']
                    ? '#ef4444'
                    : themeManager.getThemeColor('border')
                }}
                placeholder="ctrl+alt+g"
              />
              {keybindingErrors['openChatWindow.binding'] && (
                <p className="text-red-500 text-xs mt-1">
                  {keybindingErrors['openChatWindow.binding']}
                </p>
              )}
              {keybindingWarnings['openChatWindow.binding'] && (
                <p className="text-yellow-500 text-xs mt-1">
                  {keybindingWarnings['openChatWindow.binding']}
                </p>
              )}
            </div>

            {/* Mac 快捷键 */}
            <div>
              <label
                className="block mb-1 text-sm"
                style={{ color: themeManager.getThemeColor('secondary') }}
              >
                Mac
              </label>
              <input
                type="text"
                value={settings.keybindings.openChatWindow.mac}
                onChange={(e) => handleKeybindingChange('openChatWindow', 'mac', e.target.value)}
                className="apple-input w-full p-2 rounded text-sm"
                style={{
                  backgroundColor: themeManager.getThemeColor('surface'),
                  color: themeManager.getThemeColor('primary'),
                  borderColor: keybindingErrors['openChatWindow.mac']
                    ? '#ef4444'
                    : themeManager.getThemeColor('border')
                }}
                placeholder="cmd+alt+g"
              />
              {keybindingErrors['openChatWindow.mac'] && (
                <p className="text-red-500 text-xs mt-1">
                  {keybindingErrors['openChatWindow.mac']}
                </p>
              )}
              {keybindingWarnings['openChatWindow.mac'] && (
                <p className="text-yellow-500 text-xs mt-1">
                  {keybindingWarnings['openChatWindow.mac']}
                </p>
              )}
            </div>
          </div>

          {/* 当前系统快捷键预览 */}
          <div className="mt-2">
            <span
              className="text-sm"
              style={{ color: themeManager.getThemeColor('secondary') }}
            >
              当前快捷键:
            </span>
            <span
              className="text-sm font-mono ml-1 px-2 py-1 rounded"
              style={{
                backgroundColor: themeManager.getThemeColor('surface'),
                color: themeManager.getThemeColor('accent')
              }}
            >
              {formatKeybindingDisplay(settings.keybindings.openChatWindow, isMac)}
            </span>
          </div>

          {/* 功能说明 */}
          <div className="mt-2">
            <span
              className="text-xs"
              style={{ color: themeManager.getThemeColor('muted') }}
            >
              独立聊天窗口，不依赖选中内容，支持多轮对话和历史记录
            </span>
          </div>
        </div>

        {/* 快捷键使用说明 */}
        <div
          className="p-3 rounded-lg text-sm"
          style={{
            backgroundColor: themeManager.getThemeColor('surface'),
            color: themeManager.getThemeColor('secondary')
          }}
        >
          <p className="mb-2">
            <strong>快捷键格式说明：</strong>
          </p>
          <ul className="list-disc list-inside space-y-1">
            <li>使用 + 号连接修饰键和按键，如：ctrl+g</li>
            <li>支持的修饰键：ctrl, cmd, alt, shift, meta</li>
            <li>按键可以是字母、数字或功能键</li>
            <li>修改后需要重新加载插件才能生效</li>
          </ul>
        </div>
      </div>

      {/* API配置 */}
      <div className="mb-6">
        <h2
          className="text-xl font-semibold mb-3"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          API 配置
        </h2>
        
        <div className="mb-4">
          <label
            className="block mb-2 font-medium"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            API URL
          </label>
          <input
            type="text"
            name="apiUrl"
            value={settings.apiUrl}
            onChange={handleInputChange}
            className="apple-input w-full p-2 rounded"
            style={{
              backgroundColor: themeManager.getThemeColor('surface'),
              color: themeManager.getThemeColor('primary'),
              borderColor: themeManager.getThemeColor('border')
            }}
            placeholder="https://api.openai.com/v1/chat/completions"
          />
        </div>

        <div className="mb-4">
          <label
            className="block mb-2 font-medium"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            API Key
          </label>
          <input
            type="password"
            name="apiKey"
            value={settings.apiKey}
            onChange={handleInputChange}
            className="apple-input w-full p-2 rounded"
            style={{
              backgroundColor: themeManager.getThemeColor('surface'),
              color: themeManager.getThemeColor('primary'),
              borderColor: themeManager.getThemeColor('border')
            }}
            placeholder="sk-..."
          />
        </div>

        <div className="mb-4">
          <label
            className="block mb-2 font-medium"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            模型名称
          </label>
          <input
            type="text"
            name="modelName"
            value={settings.modelName}
            onChange={handleInputChange}
            className="apple-input w-full p-2 rounded"
            style={{
              backgroundColor: themeManager.getThemeColor('surface'),
              color: themeManager.getThemeColor('primary'),
              borderColor: themeManager.getThemeColor('border')
            }}
            placeholder="gpt-3.5-turbo"
          />
        </div>
      </div>

      {/* 历史记录设置 */}
      <div className="mb-6">
        <h2
          className="text-xl font-semibold mb-3"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          历史记录设置
        </h2>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableHistory"
            name="enableHistory"
            checked={settings.enableHistory}
            onChange={handleToggleChange}
            className="mr-2"
            style={{
              accentColor: themeManager.getThemeColor('accent')
            }}
          />
          <label
            htmlFor="enableHistory"
            className="font-medium"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            启用会话历史记录
          </label>
        </div>
        <p
          className="text-sm mt-1"
          style={{ color: themeManager.getThemeColor('secondary') }}
        >
          启用后，将在同一聊天会话中保存历史对话记录。
        </p>
      </div>

      {/* 自定义提示 */}
      <div className="mb-6">
        <h2
          className="text-xl font-semibold mb-3"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          自定义提示词
        </h2>

        {/* 添加新提示 */}
        <div
          className="p-4 rounded mb-4"
          style={{ backgroundColor: themeManager.getThemeColor('surface') }}
        >
          <h3
            className="font-medium mb-2"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            添加新提示词
          </h3>
          <div className="mb-3">
            <label
              className="block mb-1 text-sm"
              style={{ color: themeManager.getThemeColor('primary') }}
            >
              提示词名称
            </label>
            <input
              type="text"
              name="name"
              value={newPrompt.name}
              onChange={handleNewPromptChange}
              className="apple-input w-full p-2 rounded"
              style={{
                backgroundColor: themeManager.getThemeColor('background'),
                color: themeManager.getThemeColor('primary'),
                borderColor: themeManager.getThemeColor('border')
              }}
              placeholder="提示名称，如：总结、翻译等"
            />
          </div>
          <div className="mb-3">
            <label
              className="block mb-1 text-sm"
              style={{ color: themeManager.getThemeColor('primary') }}
            >
              提示词内容
            </label>
            <textarea
              name="prompt"
              value={newPrompt.prompt}
              onChange={handleNewPromptChange}
              className="apple-input w-full p-2 rounded"
              style={{
                backgroundColor: themeManager.getThemeColor('background'),
                color: themeManager.getThemeColor('primary'),
                borderColor: themeManager.getThemeColor('border')
              }}
              rows={3}
              placeholder="提示词内容，如：请总结以下内容的要点："
            />
          </div>
          <button
            onClick={handleAddPrompt}
            disabled={!newPrompt.name || !newPrompt.prompt}
            className="apple-button px-3 py-1 rounded"
            style={{
              backgroundColor: (!newPrompt.name || !newPrompt.prompt)
                ? themeManager.getThemeColor('muted')
                : themeManager.getThemeColor('accent'),
              color: '#ffffff',
              opacity: (!newPrompt.name || !newPrompt.prompt) ? 0.5 : 1
            }}
          >
            添加提示词
          </button>
        </div>

        {/* 现有提示词列表 */}
        <div>
          <h3
            className="font-medium mb-2"
            style={{ color: themeManager.getThemeColor('primary') }}
          >
            现有提示词
          </h3>
          {settings.customPrompts.length === 0 ? (
            <p style={{ color: themeManager.getThemeColor('secondary') }}>
              暂无自定义提示词
            </p>
          ) : (
            <div className="space-y-3">
              {settings.customPrompts.map((prompt, index) => (
                <div
                  key={index}
                  className="rounded-lg overflow-hidden"
                  style={{
                    border: `1px solid ${themeManager.getThemeColor('border')}`,
                    backgroundColor: themeManager.getThemeColor('surface')
                  }}
                >
                  {editingIndex === index ? (
                    // 编辑模式
                    <div className="p-3">
                      <div className="mb-2">
                        <label className="block mb-1 text-sm">提示词名称</label>
                        <input
                          type="text"
                          name="name"
                          value={prompt.name}
                          onChange={(e) => handleEditPromptChange(e, index)}
                          className="w-full p-2 border border-gray-300 rounded"
                        />
                      </div>
                      <div className="mb-2">
                        <label className="block mb-1 text-sm">提示词内容</label>
                        <textarea
                          name="prompt"
                          value={prompt.prompt}
                          onChange={(e) => handleEditPromptChange(e, index)}
                          className="w-full p-2 border border-gray-300 rounded"
                          rows={3}
                        />
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={handleSaveEditing}
                          className="px-2 py-1 bg-green-500 text-white text-sm rounded"
                        >
                          完成
                        </button>
                        <button
                          onClick={handleCancelEditing}
                          className="px-2 py-1 bg-gray-300 text-gray-700 text-sm rounded"
                        >
                          取消
                        </button>
                      </div>
                    </div>
                  ) : (
                    // 查看模式
                    <div className="flex justify-between p-3">
                      <div>
                        <div
                          className="font-medium"
                          style={{ color: themeManager.getThemeColor('primary') }}
                        >
                          {prompt.name}
                        </div>
                        <div
                          className="text-sm mt-1 line-clamp-2"
                          style={{ color: themeManager.getThemeColor('secondary') }}
                        >
                          {prompt.prompt}
                        </div>
                      </div>
                      <div className="flex items-start space-x-2">
                        <button
                          onClick={() => handleStartEditing(index)}
                          className="transition-colors"
                          style={{ color: themeManager.getThemeColor('accent') }}
                          title="编辑提示词"
                          onMouseEnter={(e) => {
                            e.currentTarget.style.opacity = '0.7';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.opacity = '1';
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDeletePrompt(index)}
                          className="transition-colors"
                          style={{ color: '#EF4444' }}
                          title="删除提示词"
                          onMouseEnter={(e) => {
                            e.currentTarget.style.opacity = '0.7';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.opacity = '1';
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 保存按钮 */}
      <div className="mt-6 flex items-center">
        <button
          onClick={handleSaveSettings}
          disabled={isSaving}
          className="apple-button px-4 py-2 rounded font-medium"
          style={{
            backgroundColor: isSaving
              ? themeManager.getThemeColor('muted')
              : '#10B981',
            color: '#ffffff',
            opacity: isSaving ? 0.5 : 1,
            cursor: isSaving ? 'not-allowed' : 'pointer'
          }}
        >
          {isSaving ? "保存中..." : "保存设置"}
        </button>

        {saveMessage && (
          <span
            className="ml-3"
            style={{ color: '#10B981' }}
          >
            {saveMessage}
          </span>
        )}
      </div>
    </div>
  );
}; 