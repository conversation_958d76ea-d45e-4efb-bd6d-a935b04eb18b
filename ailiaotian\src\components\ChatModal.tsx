import React, { useState, useEffect, useRef } from 'react';
import { getSettings, PluginSettings } from '../settings';
import { ContextData } from '../contextManager';
import { sendChatRequest, ApiResponseHandlers } from '../api';
import { formatResponseToBlocks, prepareBlocksForInsertion, BlockEntity } from '../formatter';
import { getThemeManager } from '../themeManager';

interface ChatModalProps {
  context: ContextData;
  onClose: () => void;
  onReplace: (content: string) => void;
  onInsert: (content: string) => void;
  onOpenSettings?: () => void;
}

/**
 * 聊天模态框组件
 */
export const ChatModal: React.FC<ChatModalProps> = ({ context, onClose, onReplace, onInsert, onOpenSettings }) => {
  // AI响应内容
  const [aiResponse, setAiResponse] = useState('');
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 错误信息
  const [error, setError] = useState('');
  // 插件设置
  const [settings, setSettings] = useState<PluginSettings | null>(null);
  // 选中的提示
  const [selectedPrompt, setSelectedPrompt] = useState('custom'); // 默认选择自定义提示词
  // 自定义提示词
  const [customPrompt, setCustomPrompt] = useState('');
  // 是否显示自定义输入
  const [showCustomInput, setShowCustomInput] = useState(true); // 默认显示自定义输入
  // 流式输出的当前位置
  const [streamPosition, setStreamPosition] = useState(0);
  // 匹配的提示词列表
  const [matchedPrompts, setMatchedPrompts] = useState<Array<{name: string, prompt: string}>>([]);
  // 当前选中的提示词建议索引
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  // 是否显示提示词建议
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  // 模态框引用
  const modalRef = useRef<HTMLDivElement>(null);
  // 自定义输入引用
  const promptInputRef = useRef<HTMLInputElement>(null);
  // 响应容器引用
  const responseRef = useRef<HTMLDivElement>(null);
  // 提示词建议容器引用
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // 主题管理
  const themeManager = getThemeManager();
  const [isDarkMode, setIsDarkMode] = useState(themeManager.isDarkMode());
  
  // 主题监听
  useEffect(() => {
    const handleThemeChange = (theme: any) => {
      setIsDarkMode(theme.isDark);
    };

    themeManager.addListener(handleThemeChange);

    return () => {
      themeManager.removeListener(handleThemeChange);
    };
  }, [themeManager]);

  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const loadedSettings = await getSettings();
        setSettings(loadedSettings);
      } catch (error) {
        console.error('加载设置出错:', error);
        setError('无法加载插件设置，请检查配置');
      }
    };

    loadSettings();
  }, []);
  
  // 当选择提示变化时自动发送请求
  useEffect(() => {
    if (selectedPrompt !== 'default' && selectedPrompt !== 'custom' && context?.content) {
      handleSendMessage();
    }
    
    // 如果选择了自定义提示，聚焦到输入框
    if (selectedPrompt === 'custom') {
      setShowCustomInput(true);
      setTimeout(() => {
        promptInputRef.current?.focus();
      }, 50);
    } else {
      setShowCustomInput(false);
    }
  }, [selectedPrompt]);
  
  // 初始化后聚焦到自定义输入框
  useEffect(() => {
    if (showCustomInput) {
      setTimeout(() => {
        promptInputRef.current?.focus();
      }, 50);
    }
  }, []);

  // 点击页面其他区域时隐藏提示词建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 处理提示选择变化
  const handlePromptChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedPrompt(e.target.value);
  };
  
  // 处理自定义提示变化
  const handleCustomPromptChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setCustomPrompt(inputValue);
    
    // 匹配提示词
    if (settings && inputValue.trim() !== '') {
      const matchedItems = settings.customPrompts.filter(
        prompt => prompt.name.toLowerCase().includes(inputValue.toLowerCase())
      );
      setMatchedPrompts(matchedItems);
      setShowSuggestions(matchedItems.length > 0);
      setSelectedSuggestionIndex(-1);
    } else {
      setMatchedPrompts([]);
      setShowSuggestions(false);
    }
  };

  // 处理提示词建议选择
  const handleSuggestionSelect = (promptName: string, promptContent: string) => {
    // 设置视觉反馈，显示正在使用的提示词名称
    const promptDisplay = `使用提示词: ${promptName}`;
    setCustomPrompt(promptDisplay);
    setSelectedPrompt('custom');
    setShowSuggestions(false);
    
    // 自动发送消息，不需要用户手动点击发送
    setTimeout(() => {
      handleSendMessage(promptContent);
    }, 100);
  };
  
  // 处理键盘导航提示词建议
  const handlePromptKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 处理ESC键 - 隐藏提示词建议
    if (e.key === 'Escape') {
      setShowSuggestions(false);
      return;
    }
    
    // 如果没有显示提示词建议，则处理回车键发送消息
    if (!showSuggestions) {
      if (e.key === 'Enter' && customPrompt.trim() && !isLoading) {
        e.preventDefault();
        handleSendMessage();
      }
      return;
    }
    
    // 处理上下键导航提示词建议
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => 
        prev < matchedPrompts.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => (prev > 0 ? prev - 1 : 0));
    } 
    // 处理回车键选择提示词建议
    else if (e.key === 'Enter' && selectedSuggestionIndex >= 0) {
      e.preventDefault();
      const selectedPrompt = matchedPrompts[selectedSuggestionIndex];
      handleSuggestionSelect(selectedPrompt.name, selectedPrompt.prompt);
    }
  };
  
  // 处理自定义提示发送
  const handleCustomPromptSend = () => {
    if (customPrompt.trim() && !isLoading) {
      handleSendMessage();
    }
  };
  
  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && customPrompt.trim() && !isLoading && !showSuggestions) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  // 处理发送消息
  const handleSendMessage = async (promptOverride?: string) => {
    if (!context?.content || isLoading) {
      return;
    }
    
    if (!settings || !settings.apiKey) {
      setError('请先在设置中配置API密钥');
      return;
    }
    
    setIsLoading(true);
    setError('');
    setAiResponse('');
    setStreamPosition(0);
    setShowSuggestions(false);
    
    try {
      // 获取系统提示（如果选择了自定义提示）
      let systemPrompt: string | undefined;
      
      if (promptOverride) {
        // 使用传入的提示词覆盖
        systemPrompt = promptOverride;
      } else if (selectedPrompt === 'custom') {
        // 使用用户输入的自定义提示
        if (!customPrompt.trim()) {
          setError('请输入自定义提示词');
          setIsLoading(false);
          return;
        }
        systemPrompt = customPrompt;
      } else if (selectedPrompt !== 'default' && settings) {
        // 使用预设提示
        const selectedPromptObj = settings.customPrompts.find(p => p.name === selectedPrompt);
        if (selectedPromptObj) {
          systemPrompt = selectedPromptObj.prompt;
        }
      }
      
      // 定义响应处理器
      const responseHandlers: ApiResponseHandlers = {
        onChunk: (chunk: string) => {
          setAiResponse(prev => {
            const newResponse = prev + chunk;
            // 更新当前位置以实现打字机效果
            setTimeout(() => {
              setStreamPosition(newResponse.length);
            }, 10);
            return newResponse;
          });
        },
        onComplete: (fullResponse: string) => {
          setIsLoading(false);
          // 完成后显示全部内容
          setStreamPosition(fullResponse.length);
        },
        onError: (errorMsg: string) => {
          setError(`API 请求失败: ${errorMsg}`);
          setIsLoading(false);
        }
      };
      
      // 发送请求到 API
      await sendChatRequest(
        context.content,
        context,
        responseHandlers,
        systemPrompt
      );
    } catch (error) {
      console.error('发送消息出错:', error);
      setError('发送消息失败，请重试');
      setIsLoading(false);
    }
  };
  
  // 渲染打字机效果的响应文本
  const renderStreamingResponse = () => {
    if (!aiResponse) return null;
    
    return (
      <div className="typewriter-text">
        {aiResponse.slice(0, streamPosition)}
        {isLoading && streamPosition === aiResponse.length && (
          <span className="blinking-cursor">|</span>
        )}
      </div>
    );
  };
  
  // 渲染提示选项
  const renderPromptOptions = () => {
    if (!settings) return null;
    
    return (
      <>
        <option value="custom">自定义提示词</option>
        {settings.customPrompts.map((prompt, index) => (
          <option key={index} value={prompt.name}>
            {prompt.name}
          </option>
        ))}
      </>
    );
  };

  // 渲染提示词建议
  const renderPromptSuggestions = () => {
    if (!showSuggestions || matchedPrompts.length === 0) {
      return null;
    }

    return (
      <div 
        ref={suggestionsRef}
        className="absolute z-10 left-4 right-16 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto"
      >
        {matchedPrompts.map((prompt, index) => (
          <div
            key={index}
            className={`px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${
              index === selectedSuggestionIndex ? 'bg-gray-100 dark:bg-gray-700' : ''
            }`}
            onClick={() => handleSuggestionSelect(prompt.name, prompt.prompt)}
          >
            <div className="font-medium text-gray-900 dark:text-white">{prompt.name}</div>
          </div>
        ))}
      </div>
    );
  };
  
  // 处理替换原文
  const handleReplace = async (responseText: string) => {
    try {
      // 格式化响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(responseText);
      
      if (formattedBlocks.length === 0 || context.blockUUIDs.length === 0) {
        setError('无法替换原文：无有效块数据或无原始块 UUID');
        return;
      }
      
      // 执行替换操作
      onReplace(responseText);
    } catch (error) {
      console.error('替换原文出错:', error);
      setError('替换原文失败，请重试');
    }
  };
  
  // 处理插入子块
  const handleInsert = async (responseText: string) => {
    try {
      // 格式化响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(responseText);
      
      if (formattedBlocks.length === 0 || context.blockUUIDs.length === 0) {
        setError('无法插入子块：无有效块数据或无原始块 UUID');
        return;
      }
      
      // 执行插入操作
      onInsert(responseText);
    } catch (error) {
      console.error('插入子块出错:', error);
      setError('插入子块失败，请重试');
    }
  };
  
  return (
    <div
      ref={modalRef}
      className={`apple-modal rounded-xl shadow-lg w-full flex flex-col ${themeManager.getThemeClasses()}`}
      style={{
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        maxHeight: '60vh',
        border: `1px solid ${themeManager.getThemeColor('border')}`,
        backgroundColor: themeManager.getThemeColor('background'),
        color: themeManager.getThemeColor('primary')
      }}
    >
      {/* 模态框标题栏 */}
      <div
        className="p-4 border-b flex justify-between items-center"
        style={{ borderColor: themeManager.getThemeColor('border') }}
      >
        <h2
          className="text-lg font-medium"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          AI 聊天
        </h2>
        <div className="flex items-center">
          <select
            value={selectedPrompt}
            onChange={handlePromptChange}
            className="apple-select mr-3 text-sm rounded-md border-none h-8 px-3"
            style={{
              backgroundColor: themeManager.getThemeColor('surface'),
              color: themeManager.getThemeColor('primary')
            }}
          >
            {renderPromptOptions()}
          </select>

          {/* 设置按钮 */}
          {onOpenSettings && (
            <button
              onClick={onOpenSettings}
              className="h-8 w-8 flex items-center justify-center rounded-full transition-colors mr-2"
              style={{
                color: themeManager.getThemeColor('secondary'),
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = themeManager.getThemeColor('surface');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
              title="打开设置"
            >
              ⚙️
            </button>
          )}

          <button
            onClick={onClose}
            className="h-8 w-8 flex items-center justify-center rounded-full transition-colors"
            style={{
              color: themeManager.getThemeColor('secondary'),
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = themeManager.getThemeColor('surface');
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            title="关闭"
          >
            ✕
          </button>
        </div>
      </div>
      
      {/* 自定义提示输入 */}
      {showCustomInput && (
        <div
          className="p-4 border-b relative"
          style={{ borderColor: themeManager.getThemeColor('border') }}
        >
          <div className="flex items-center">
            <input
              ref={promptInputRef}
              type="text"
              value={customPrompt}
              onChange={handleCustomPromptChange}
              onKeyPress={handleKeyPress}
              onKeyDown={handlePromptKeyDown}
              placeholder="输入提示词，支持自动补全"
              className="apple-input flex-grow text-sm rounded-lg px-4 py-2"
              style={{
                backgroundColor: themeManager.getThemeColor('surface'),
                color: themeManager.getThemeColor('primary'),
                borderColor: themeManager.getThemeColor('border')
              }}
            />
            <button
              onClick={handleCustomPromptSend}
              disabled={!customPrompt.trim() || isLoading}
              className="apple-button ml-2 px-4 py-2 rounded-lg text-sm font-medium"
              style={{
                backgroundColor: (!customPrompt.trim() || isLoading)
                  ? themeManager.getThemeColor('muted')
                  : themeManager.getThemeColor('accent'),
                color: '#ffffff',
                opacity: (!customPrompt.trim() || isLoading) ? 0.5 : 1,
                cursor: (!customPrompt.trim() || isLoading) ? 'not-allowed' : 'pointer'
              }}
            >
              发送
            </button>
          </div>

          {/* 提示词建议下拉框 */}
          {renderPromptSuggestions()}
        </div>
      )}
      
      {/* 内容区域 */}
      <div 
        ref={responseRef}
        className="p-4 flex-grow overflow-y-auto max-h-[40vh] relative"
      >
        {/* 错误消息 */}
        {error && (
          <div
            className="mb-4 p-3 rounded-lg text-sm"
            style={{
              backgroundColor: isDarkMode ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.1)',
              borderColor: isDarkMode ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.3)',
              color: isDarkMode ? '#FCA5A5' : '#DC2626',
              border: '1px solid'
            }}
          >
            {error}
          </div>
        )}

        {/* 加载指示器 */}
        {isLoading && !aiResponse && (
          <div className="flex justify-center items-center py-8">
            <div
              className="apple-spinner w-6 h-6 border-2 rounded-full animate-spin"
              style={{
                borderColor: themeManager.getThemeColor('border'),
                borderTopColor: themeManager.getThemeColor('accent')
              }}
            ></div>
          </div>
        )}

        {/* AI 响应 */}
        {aiResponse && (
          <div
            className="ai-response rounded-lg p-4 text-sm whitespace-pre-wrap relative"
            style={{
              backgroundColor: themeManager.getThemeColor('surface'),
              color: themeManager.getThemeColor('primary')
            }}
          >
            {renderStreamingResponse()}
          </div>
        )}
      </div>
      
      {/* 操作按钮 */}
      {aiResponse && !isLoading && (
        <div
          className="p-3 border-t flex justify-end space-x-2"
          style={{ borderColor: themeManager.getThemeColor('border') }}
        >
          <button
            onClick={() => handleReplace(aiResponse)}
            className="apple-button-secondary px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            style={{
              backgroundColor: themeManager.getThemeColor('surface'),
              color: themeManager.getThemeColor('primary'),
              border: `1px solid ${themeManager.getThemeColor('border')}`
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = themeManager.getThemeColor('border');
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = themeManager.getThemeColor('surface');
            }}
          >
            替换原文
          </button>
          <button
            onClick={() => handleInsert(aiResponse)}
            className="apple-button-primary px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            style={{
              backgroundColor: themeManager.getThemeColor('accent'),
              color: '#ffffff'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.9';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
            }}
          >
            插入子块
          </button>
        </div>
      )}
    </div>
  );
}; 