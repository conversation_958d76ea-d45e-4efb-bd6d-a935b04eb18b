(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const l of o)if(l.type==="childList")for(const a of l.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const l={};return o.integrity&&(l.integrity=o.integrity),o.referrerPolicy&&(l.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?l.credentials="include":o.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(o){if(o.ep)return;o.ep=!0;const l=n(o);fetch(o.href,l)}})();var Hl=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function $h(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Id={exports:{}},qi={},Ld={exports:{}},pe={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hl=Symbol.for("react.element"),Bh=Symbol.for("react.portal"),Hh=Symbol.for("react.fragment"),Wh=Symbol.for("react.strict_mode"),Vh=Symbol.for("react.profiler"),qh=Symbol.for("react.provider"),Qh=Symbol.for("react.context"),Kh=Symbol.for("react.forward_ref"),Gh=Symbol.for("react.suspense"),Yh=Symbol.for("react.memo"),Xh=Symbol.for("react.lazy"),Cc=Symbol.iterator;function Zh(e){return e===null||typeof e!="object"?null:(e=Cc&&e[Cc]||e["@@iterator"],typeof e=="function"?e:null)}var jd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Md=Object.assign,Ad={};function po(e,t,n){this.props=e,this.context=t,this.refs=Ad,this.updater=n||jd}po.prototype.isReactComponent={};po.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};po.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Rd(){}Rd.prototype=po.prototype;function tu(e,t,n){this.props=e,this.context=t,this.refs=Ad,this.updater=n||jd}var nu=tu.prototype=new Rd;nu.constructor=tu;Md(nu,po.prototype);nu.isPureReactComponent=!0;var xc=Array.isArray,zd=Object.prototype.hasOwnProperty,ru={current:null},Dd={key:!0,ref:!0,__self:!0,__source:!0};function Fd(e,t,n){var r,o={},l=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(l=""+t.key),t)zd.call(t,r)&&!Dd.hasOwnProperty(r)&&(o[r]=t[r]);var c=arguments.length-2;if(c===1)o.children=n;else if(1<c){for(var d=Array(c),h=0;h<c;h++)d[h]=arguments[h+2];o.children=d}if(e&&e.defaultProps)for(r in c=e.defaultProps,c)o[r]===void 0&&(o[r]=c[r]);return{$$typeof:hl,type:e,key:l,ref:a,props:o,_owner:ru.current}}function Jh(e,t){return{$$typeof:hl,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ou(e){return typeof e=="object"&&e!==null&&e.$$typeof===hl}function em(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Sc=/\/+/g;function Ms(e,t){return typeof e=="object"&&e!==null&&e.key!=null?em(""+e.key):t.toString(36)}function ai(e,t,n,r,o){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(l){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case hl:case Bh:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+Ms(a,0):r,xc(o)?(n="",e!=null&&(n=e.replace(Sc,"$&/")+"/"),ai(o,t,n,"",function(h){return h})):o!=null&&(ou(o)&&(o=Jh(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(Sc,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",xc(e))for(var c=0;c<e.length;c++){l=e[c];var d=r+Ms(l,c);a+=ai(l,t,n,d,o)}else if(d=Zh(e),typeof d=="function")for(e=d.call(e),c=0;!(l=e.next()).done;)l=l.value,d=r+Ms(l,c++),a+=ai(l,t,n,d,o);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Wl(e,t,n){if(e==null)return e;var r=[],o=0;return ai(e,r,"","",function(l){return t.call(n,l,o++)}),r}function tm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ut={current:null},ui={transition:null},nm={ReactCurrentDispatcher:ut,ReactCurrentBatchConfig:ui,ReactCurrentOwner:ru};function Ud(){throw Error("act(...) is not supported in production builds of React.")}pe.Children={map:Wl,forEach:function(e,t,n){Wl(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Wl(e,function(){t++}),t},toArray:function(e){return Wl(e,function(t){return t})||[]},only:function(e){if(!ou(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};pe.Component=po;pe.Fragment=Hh;pe.Profiler=Vh;pe.PureComponent=tu;pe.StrictMode=Wh;pe.Suspense=Gh;pe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=nm;pe.act=Ud;pe.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Md({},e.props),o=e.key,l=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,a=ru.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(d in t)zd.call(t,d)&&!Dd.hasOwnProperty(d)&&(r[d]=t[d]===void 0&&c!==void 0?c[d]:t[d])}var d=arguments.length-2;if(d===1)r.children=n;else if(1<d){c=Array(d);for(var h=0;h<d;h++)c[h]=arguments[h+2];r.children=c}return{$$typeof:hl,type:e.type,key:o,ref:l,props:r,_owner:a}};pe.createContext=function(e){return e={$$typeof:Qh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:qh,_context:e},e.Consumer=e};pe.createElement=Fd;pe.createFactory=function(e){var t=Fd.bind(null,e);return t.type=e,t};pe.createRef=function(){return{current:null}};pe.forwardRef=function(e){return{$$typeof:Kh,render:e}};pe.isValidElement=ou;pe.lazy=function(e){return{$$typeof:Xh,_payload:{_status:-1,_result:e},_init:tm}};pe.memo=function(e,t){return{$$typeof:Yh,type:e,compare:t===void 0?null:t}};pe.startTransition=function(e){var t=ui.transition;ui.transition={};try{e()}finally{ui.transition=t}};pe.unstable_act=Ud;pe.useCallback=function(e,t){return ut.current.useCallback(e,t)};pe.useContext=function(e){return ut.current.useContext(e)};pe.useDebugValue=function(){};pe.useDeferredValue=function(e){return ut.current.useDeferredValue(e)};pe.useEffect=function(e,t){return ut.current.useEffect(e,t)};pe.useId=function(){return ut.current.useId()};pe.useImperativeHandle=function(e,t,n){return ut.current.useImperativeHandle(e,t,n)};pe.useInsertionEffect=function(e,t){return ut.current.useInsertionEffect(e,t)};pe.useLayoutEffect=function(e,t){return ut.current.useLayoutEffect(e,t)};pe.useMemo=function(e,t){return ut.current.useMemo(e,t)};pe.useReducer=function(e,t,n){return ut.current.useReducer(e,t,n)};pe.useRef=function(e){return ut.current.useRef(e)};pe.useState=function(e){return ut.current.useState(e)};pe.useSyncExternalStore=function(e,t,n){return ut.current.useSyncExternalStore(e,t,n)};pe.useTransition=function(){return ut.current.useTransition()};pe.version="18.3.1";Ld.exports=pe;var se=Ld.exports;const $d=$h(se);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rm=se,om=Symbol.for("react.element"),lm=Symbol.for("react.fragment"),im=Object.prototype.hasOwnProperty,sm=rm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,am={key:!0,ref:!0,__self:!0,__source:!0};function Bd(e,t,n){var r,o={},l=null,a=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)im.call(t,r)&&!am.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:om,type:e,key:l,ref:a,props:o,_owner:sm.current}}qi.Fragment=lm;qi.jsx=Bd;qi.jsxs=Bd;Id.exports=qi;var lu=Id.exports;const um=lu.Fragment,A=lu.jsx,ee=lu.jsxs;var ki={exports:{}};/*! For license information please see lsplugin.user.js.LICENSE.txt */ki.exports;(function(e,t){(function(n,r){e.exports=r()})(self,()=>(()=>{var n={227:(a,c,d)=>{var h=d(155);c.formatArgs=function(S){if(S[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+S[0]+(this.useColors?"%c ":" ")+"+"+a.exports.humanize(this.diff),!this.useColors)return;const T="color: "+this.color;S.splice(1,0,T,"color: inherit");let k=0,x=0;S[0].replace(/%[a-zA-Z%]/g,_=>{_!=="%%"&&(k++,_==="%c"&&(x=k))}),S.splice(x,0,T)},c.save=function(S){try{S?c.storage.setItem("debug",S):c.storage.removeItem("debug")}catch{}},c.load=function(){let S;try{S=c.storage.getItem("debug")}catch{}return!S&&h!==void 0&&"env"in h&&(S=h.env.DEBUG),S},c.useColors=function(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},c.storage=function(){try{return localStorage}catch{}}(),c.destroy=(()=>{let S=!1;return()=>{S||(S=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),c.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],c.log=console.debug||console.log||(()=>{}),a.exports=d(447)(c);const{formatters:v}=a.exports;v.j=function(S){try{return JSON.stringify(S)}catch(T){return"[UnexpectedJSONParseError]: "+T.message}}},447:(a,c,d)=>{a.exports=function(h){function v(k){let x,_,I,g=null;function p(...f){if(!p.enabled)return;const P=p,N=Number(new Date),O=N-(x||N);P.diff=O,P.prev=x,P.curr=N,x=N,f[0]=v.coerce(f[0]),typeof f[0]!="string"&&f.unshift("%O");let m=0;f[0]=f[0].replace(/%([a-zA-Z%])/g,(w,R)=>{if(w==="%%")return"%";m++;const j=v.formatters[R];if(typeof j=="function"){const J=f[m];w=j.call(P,J),f.splice(m,1),m--}return w}),v.formatArgs.call(P,f),(P.log||v.log).apply(P,f)}return p.namespace=k,p.useColors=v.useColors(),p.color=v.selectColor(k),p.extend=S,p.destroy=v.destroy,Object.defineProperty(p,"enabled",{enumerable:!0,configurable:!1,get:()=>g!==null?g:(_!==v.namespaces&&(_=v.namespaces,I=v.enabled(k)),I),set:f=>{g=f}}),typeof v.init=="function"&&v.init(p),p}function S(k,x){const _=v(this.namespace+(x===void 0?":":x)+k);return _.log=this.log,_}function T(k){return k.toString().substring(2,k.toString().length-2).replace(/\.\*\?$/,"*")}return v.debug=v,v.default=v,v.coerce=function(k){return k instanceof Error?k.stack||k.message:k},v.disable=function(){const k=[...v.names.map(T),...v.skips.map(T).map(x=>"-"+x)].join(",");return v.enable(""),k},v.enable=function(k){let x;v.save(k),v.namespaces=k,v.names=[],v.skips=[];const _=(typeof k=="string"?k:"").split(/[\s,]+/),I=_.length;for(x=0;x<I;x++)_[x]&&((k=_[x].replace(/\*/g,".*?"))[0]==="-"?v.skips.push(new RegExp("^"+k.slice(1)+"$")):v.names.push(new RegExp("^"+k+"$")))},v.enabled=function(k){if(k[k.length-1]==="*")return!0;let x,_;for(x=0,_=v.skips.length;x<_;x++)if(v.skips[x].test(k))return!1;for(x=0,_=v.names.length;x<_;x++)if(v.names[x].test(k))return!0;return!1},v.humanize=d(824),v.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(h).forEach(k=>{v[k]=h[k]}),v.names=[],v.skips=[],v.formatters={},v.selectColor=function(k){let x=0;for(let _=0;_<k.length;_++)x=(x<<5)-x+k.charCodeAt(_),x|=0;return v.colors[Math.abs(x)%v.colors.length]},v.enable(v.load()),v}},996:a=>{var c=function(I){return function(g){return!!g&&typeof g=="object"}(I)&&!function(g){var p=Object.prototype.toString.call(g);return p==="[object RegExp]"||p==="[object Date]"||function(f){return f.$$typeof===d}(g)}(I)},d=typeof Symbol=="function"&&Symbol.for?Symbol.for("react.element"):60103;function h(I,g){return g.clone!==!1&&g.isMergeableObject(I)?x((p=I,Array.isArray(p)?[]:{}),I,g):I;var p}function v(I,g,p){return I.concat(g).map(function(f){return h(f,p)})}function S(I){return Object.keys(I).concat(function(g){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(g).filter(function(p){return Object.propertyIsEnumerable.call(g,p)}):[]}(I))}function T(I,g){try{return g in I}catch{return!1}}function k(I,g,p){var f={};return p.isMergeableObject(I)&&S(I).forEach(function(P){f[P]=h(I[P],p)}),S(g).forEach(function(P){(function(N,O){return T(N,O)&&!(Object.hasOwnProperty.call(N,O)&&Object.propertyIsEnumerable.call(N,O))})(I,P)||(T(I,P)&&p.isMergeableObject(g[P])?f[P]=function(N,O){if(!O.customMerge)return x;var m=O.customMerge(N);return typeof m=="function"?m:x}(P,p)(I[P],g[P],p):f[P]=h(g[P],p))}),f}function x(I,g,p){(p=p||{}).arrayMerge=p.arrayMerge||v,p.isMergeableObject=p.isMergeableObject||c,p.cloneUnlessOtherwiseSpecified=h;var f=Array.isArray(g);return f===Array.isArray(I)?f?p.arrayMerge(I,g,p):k(I,g,p):h(g,p)}x.all=function(I,g){if(!Array.isArray(I))throw new Error("first argument should be an array");return I.reduce(function(p,f){return x(p,f,g)},{})};var _=x;a.exports=_},856:function(a){a.exports=function(){function c(W){return c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(Q){return typeof Q}:function(Q){return Q&&typeof Symbol=="function"&&Q.constructor===Symbol&&Q!==Symbol.prototype?"symbol":typeof Q},c(W)}function d(W,Q){return d=Object.setPrototypeOf||function(le,fe){return le.__proto__=fe,le},d(W,Q)}function h(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function v(W,Q,le){return v=h()?Reflect.construct:function(fe,ze,At){var Yt=[null];Yt.push.apply(Yt,ze);var _n=new(Function.bind.apply(fe,Yt));return At&&d(_n,At.prototype),_n},v.apply(null,arguments)}function S(W){return T(W)||k(W)||x(W)||I()}function T(W){if(Array.isArray(W))return _(W)}function k(W){if(typeof Symbol<"u"&&W[Symbol.iterator]!=null||W["@@iterator"]!=null)return Array.from(W)}function x(W,Q){if(W){if(typeof W=="string")return _(W,Q);var le=Object.prototype.toString.call(W).slice(8,-1);return le==="Object"&&W.constructor&&(le=W.constructor.name),le==="Map"||le==="Set"?Array.from(W):le==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(le)?_(W,Q):void 0}}function _(W,Q){(Q==null||Q>W.length)&&(Q=W.length);for(var le=0,fe=new Array(Q);le<Q;le++)fe[le]=W[le];return fe}function I(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var g=Object.hasOwnProperty,p=Object.setPrototypeOf,f=Object.isFrozen,P=Object.getPrototypeOf,N=Object.getOwnPropertyDescriptor,O=Object.freeze,m=Object.seal,w=Object.create,R=typeof Reflect<"u"&&Reflect,j=R.apply,J=R.construct;j||(j=function(W,Q,le){return W.apply(Q,le)}),O||(O=function(W){return W}),m||(m=function(W){return W}),J||(J=function(W,Q){return v(W,S(Q))});var ce=te(Array.prototype.forEach),ue=te(Array.prototype.pop),he=te(Array.prototype.push),oe=te(String.prototype.toLowerCase),Ee=te(String.prototype.match),Se=te(String.prototype.replace),z=te(String.prototype.indexOf),E=te(String.prototype.trim),L=te(RegExp.prototype.test),$=ye(TypeError);function te(W){return function(Q){for(var le=arguments.length,fe=new Array(le>1?le-1:0),ze=1;ze<le;ze++)fe[ze-1]=arguments[ze];return j(W,Q,fe)}}function ye(W){return function(){for(var Q=arguments.length,le=new Array(Q),fe=0;fe<Q;fe++)le[fe]=arguments[fe];return J(W,le)}}function B(W,Q){p&&p(W,null);for(var le=Q.length;le--;){var fe=Q[le];if(typeof fe=="string"){var ze=oe(fe);ze!==fe&&(f(Q)||(Q[le]=ze),fe=ze)}W[fe]=!0}return W}function ne(W){var Q,le=w(null);for(Q in W)j(g,W,[Q])&&(le[Q]=W[Q]);return le}function ae(W,Q){for(;W!==null;){var le=N(W,Q);if(le){if(le.get)return te(le.get);if(typeof le.value=="function")return te(le.value)}W=P(W)}function fe(ze){return console.warn("fallback value for",ze),null}return fe}var Re=O(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ve=O(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),it=O(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),K=O(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Y=O(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),ve=O(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),_e=O(["#text"]),qe=O(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),dt=O(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),an=O(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),wt=O(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ft=m(/\{\{[\w\W]*|[\w\W]*\}\}/gm),tr=m(/<%[\w\W]*|[\w\W]*%>/gm),us=m(/^data-[\-\w.\u00B7-\uFFFF]/),Mt=m(/^aria-[\-\w]+$/),Gt=m(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),wl=m(/^(?:\w+script|data):/i),cs=m(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ds=m(/^html$/i),nr=function(){return typeof window>"u"?null:window},fs=function(W,Q){if(c(W)!=="object"||typeof W.createPolicy!="function")return null;var le=null,fe="data-tt-policy-suffix";Q.currentScript&&Q.currentScript.hasAttribute(fe)&&(le=Q.currentScript.getAttribute(fe));var ze="dompurify"+(le?"#"+le:"");try{return W.createPolicy(ze,{createHTML:function(At){return At}})}catch{return console.warn("TrustedTypes policy "+ze+" could not be created."),null}};function kl(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:nr(),Q=function(C){return kl(C)};if(Q.version="2.3.8",Q.removed=[],!W||!W.document||W.document.nodeType!==9)return Q.isSupported=!1,Q;var le=W.document,fe=W.document,ze=W.DocumentFragment,At=W.HTMLTemplateElement,Yt=W.Node,_n=W.Element,go=W.NodeFilter,Cl=W.NamedNodeMap,un=Cl===void 0?W.NamedNodeMap||W.MozNamedAttrMap:Cl,ps=W.HTMLFormElement,hs=W.DOMParser,ms=W.trustedTypes,Pr=_n.prototype,gs=ae(Pr,"cloneNode"),ys=ae(Pr,"nextSibling"),vs=ae(Pr,"childNodes"),yo=ae(Pr,"parentNode");if(typeof At=="function"){var Rt=fe.createElement("template");Rt.content&&Rt.content.ownerDocument&&(fe=Rt.content.ownerDocument)}var zt=fs(ms,le),xl=zt?zt.createHTML(""):"",Or=fe,vo=Or.implementation,En=Or.createNodeIterator,Sl=Or.createDocumentFragment,_l=Or.getElementsByTagName,ws=le.importNode,El={};try{El=ne(fe).documentMode?fe.documentMode:{}}catch{}var kt={};Q.isSupported=typeof yo=="function"&&vo&&vo.createHTMLDocument!==void 0&&El!==9;var rr,Xt,Ir=ft,Lr=tr,wo=us,ks=Mt,Tl=wl,jr=cs,we=Gt,Ue=null,Nl=B({},[].concat(S(Re),S(Ve),S(it),S(Y),S(_e))),$e=null,Tn=B({},[].concat(S(qe),S(dt),S(an),S(wt))),Oe=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Nn=null,Mr=null,ko=!0,Co=!0,bl=!1,bn=!1,cn=!1,xo=!1,So=!1,Pn=!1,Ar=!1,On=!1,Pl=!0,_o=!0,In=!1,Dt={},Ln=null,Ol=B({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Il=null,Ll=B({},["audio","video","img","source","image","track"]),Eo=null,dn=B({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),jn="http://www.w3.org/1998/Math/MathML",To="http://www.w3.org/2000/svg",Zt="http://www.w3.org/1999/xhtml",Rr=Zt,jl=!1,or=["application/xhtml+xml","text/html"],lr="text/html",Mn=null,Cs=fe.createElement("form"),Ml=function(C){return C instanceof RegExp||C instanceof Function},No=function(C){Mn&&Mn===C||(C&&c(C)==="object"||(C={}),C=ne(C),Ue="ALLOWED_TAGS"in C?B({},C.ALLOWED_TAGS):Nl,$e="ALLOWED_ATTR"in C?B({},C.ALLOWED_ATTR):Tn,Eo="ADD_URI_SAFE_ATTR"in C?B(ne(dn),C.ADD_URI_SAFE_ATTR):dn,Il="ADD_DATA_URI_TAGS"in C?B(ne(Ll),C.ADD_DATA_URI_TAGS):Ll,Ln="FORBID_CONTENTS"in C?B({},C.FORBID_CONTENTS):Ol,Nn="FORBID_TAGS"in C?B({},C.FORBID_TAGS):{},Mr="FORBID_ATTR"in C?B({},C.FORBID_ATTR):{},Dt="USE_PROFILES"in C&&C.USE_PROFILES,ko=C.ALLOW_ARIA_ATTR!==!1,Co=C.ALLOW_DATA_ATTR!==!1,bl=C.ALLOW_UNKNOWN_PROTOCOLS||!1,bn=C.SAFE_FOR_TEMPLATES||!1,cn=C.WHOLE_DOCUMENT||!1,Pn=C.RETURN_DOM||!1,Ar=C.RETURN_DOM_FRAGMENT||!1,On=C.RETURN_TRUSTED_TYPE||!1,So=C.FORCE_BODY||!1,Pl=C.SANITIZE_DOM!==!1,_o=C.KEEP_CONTENT!==!1,In=C.IN_PLACE||!1,we=C.ALLOWED_URI_REGEXP||we,Rr=C.NAMESPACE||Zt,C.CUSTOM_ELEMENT_HANDLING&&Ml(C.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Oe.tagNameCheck=C.CUSTOM_ELEMENT_HANDLING.tagNameCheck),C.CUSTOM_ELEMENT_HANDLING&&Ml(C.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Oe.attributeNameCheck=C.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),C.CUSTOM_ELEMENT_HANDLING&&typeof C.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(Oe.allowCustomizedBuiltInElements=C.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),rr=rr=or.indexOf(C.PARSER_MEDIA_TYPE)===-1?lr:C.PARSER_MEDIA_TYPE,Xt=rr==="application/xhtml+xml"?function(q){return q}:oe,bn&&(Co=!1),Ar&&(Pn=!0),Dt&&(Ue=B({},S(_e)),$e=[],Dt.html===!0&&(B(Ue,Re),B($e,qe)),Dt.svg===!0&&(B(Ue,Ve),B($e,dt),B($e,wt)),Dt.svgFilters===!0&&(B(Ue,it),B($e,dt),B($e,wt)),Dt.mathMl===!0&&(B(Ue,Y),B($e,an),B($e,wt))),C.ADD_TAGS&&(Ue===Nl&&(Ue=ne(Ue)),B(Ue,C.ADD_TAGS)),C.ADD_ATTR&&($e===Tn&&($e=ne($e)),B($e,C.ADD_ATTR)),C.ADD_URI_SAFE_ATTR&&B(Eo,C.ADD_URI_SAFE_ATTR),C.FORBID_CONTENTS&&(Ln===Ol&&(Ln=ne(Ln)),B(Ln,C.FORBID_CONTENTS)),_o&&(Ue["#text"]=!0),cn&&B(Ue,["html","head","body"]),Ue.table&&(B(Ue,["tbody"]),delete Nn.tbody),O&&O(C),Mn=C)},Al=B({},["mi","mo","mn","ms","mtext"]),Rl=B({},["foreignobject","desc","title","annotation-xml"]),xs=B({},["title","style","font","a","script"]),Ft=B({},Ve);B(Ft,it),B(Ft,K);var zr=B({},Y);B(zr,ve);var Ss=function(C){var q=yo(C);q&&q.tagName||(q={namespaceURI:Zt,tagName:"template"});var H=oe(C.tagName),ke=oe(q.tagName);return C.namespaceURI===To?q.namespaceURI===Zt?H==="svg":q.namespaceURI===jn?H==="svg"&&(ke==="annotation-xml"||Al[ke]):!!Ft[H]:C.namespaceURI===jn?q.namespaceURI===Zt?H==="math":q.namespaceURI===To?H==="math"&&Rl[ke]:!!zr[H]:C.namespaceURI===Zt&&!(q.namespaceURI===To&&!Rl[ke])&&!(q.namespaceURI===jn&&!Al[ke])&&!zr[H]&&(xs[H]||!Ft[H])},Ut=function(C){he(Q.removed,{element:C});try{C.parentNode.removeChild(C)}catch{try{C.outerHTML=xl}catch{C.remove()}}},ir=function(C,q){try{he(Q.removed,{attribute:q.getAttributeNode(C),from:q})}catch{he(Q.removed,{attribute:null,from:q})}if(q.removeAttribute(C),C==="is"&&!$e[C])if(Pn||Ar)try{Ut(q)}catch{}else try{q.setAttribute(C,"")}catch{}},zl=function(C){var q,H;if(So)C="<remove></remove>"+C;else{var ke=Ee(C,/^[\r\n\t ]+/);H=ke&&ke[0]}rr==="application/xhtml+xml"&&(C='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+C+"</body></html>");var Le=zt?zt.createHTML(C):C;if(Rr===Zt)try{q=new hs().parseFromString(Le,rr)}catch{}if(!q||!q.documentElement){q=vo.createDocument(Rr,"template",null);try{q.documentElement.innerHTML=jl?"":Le}catch{}}var Ge=q.body||q.documentElement;return C&&H&&Ge.insertBefore(fe.createTextNode(H),Ge.childNodes[0]||null),Rr===Zt?_l.call(q,cn?"html":"body")[0]:cn?q.documentElement:Ge},Dl=function(C){return En.call(C.ownerDocument||C,C,go.SHOW_ELEMENT|go.SHOW_COMMENT|go.SHOW_TEXT,null,!1)},_s=function(C){return C instanceof ps&&(typeof C.nodeName!="string"||typeof C.textContent!="string"||typeof C.removeChild!="function"||!(C.attributes instanceof un)||typeof C.removeAttribute!="function"||typeof C.setAttribute!="function"||typeof C.namespaceURI!="string"||typeof C.insertBefore!="function")},sr=function(C){return c(Yt)==="object"?C instanceof Yt:C&&c(C)==="object"&&typeof C.nodeType=="number"&&typeof C.nodeName=="string"},tt=function(C,q,H){kt[C]&&ce(kt[C],function(ke){ke.call(Q,q,H,Mn)})},ar=function(C){var q;if(tt("beforeSanitizeElements",C,null),_s(C)||L(/[\u0080-\uFFFF]/,C.nodeName))return Ut(C),!0;var H=Xt(C.nodeName);if(tt("uponSanitizeElement",C,{tagName:H,allowedTags:Ue}),C.hasChildNodes()&&!sr(C.firstElementChild)&&(!sr(C.content)||!sr(C.content.firstElementChild))&&L(/<[/\w]/g,C.innerHTML)&&L(/<[/\w]/g,C.textContent)||H==="select"&&L(/<template/i,C.innerHTML))return Ut(C),!0;if(!Ue[H]||Nn[H]){if(!Nn[H]&&fn(H)&&(Oe.tagNameCheck instanceof RegExp&&L(Oe.tagNameCheck,H)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(H)))return!1;if(_o&&!Ln[H]){var ke=yo(C)||C.parentNode,Le=vs(C)||C.childNodes;if(Le&&ke)for(var Ge=Le.length-1;Ge>=0;--Ge)ke.insertBefore(gs(Le[Ge],!0),ys(C))}return Ut(C),!0}return C instanceof _n&&!Ss(C)?(Ut(C),!0):H!=="noscript"&&H!=="noembed"||!L(/<\/no(script|embed)/i,C.innerHTML)?(bn&&C.nodeType===3&&(q=C.textContent,q=Se(q,Ir," "),q=Se(q,Lr," "),C.textContent!==q&&(he(Q.removed,{element:C.cloneNode()}),C.textContent=q)),tt("afterSanitizeElements",C,null),!1):(Ut(C),!0)},Fl=function(C,q,H){if(Pl&&(q==="id"||q==="name")&&(H in fe||H in Cs))return!1;if(!(Co&&!Mr[q]&&L(wo,q))){if(!(ko&&L(ks,q))){if(!$e[q]||Mr[q]){if(!(fn(C)&&(Oe.tagNameCheck instanceof RegExp&&L(Oe.tagNameCheck,C)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(C))&&(Oe.attributeNameCheck instanceof RegExp&&L(Oe.attributeNameCheck,q)||Oe.attributeNameCheck instanceof Function&&Oe.attributeNameCheck(q))||q==="is"&&Oe.allowCustomizedBuiltInElements&&(Oe.tagNameCheck instanceof RegExp&&L(Oe.tagNameCheck,H)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(H))))return!1}else if(!Eo[q]){if(!L(we,Se(H,jr,""))){if((q!=="src"&&q!=="xlink:href"&&q!=="href"||C==="script"||z(H,"data:")!==0||!Il[C])&&!(bl&&!L(Tl,Se(H,jr,"")))){if(H)return!1}}}}}return!0},fn=function(C){return C.indexOf("-")>0},ur=function(C){var q,H,ke,Le;tt("beforeSanitizeAttributes",C,null);var Ge=C.attributes;if(Ge){var Be={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:$e};for(Le=Ge.length;Le--;){var cr=q=Ge[Le],Jt=cr.name,pt=cr.namespaceURI;if(H=Jt==="value"?q.value:E(q.value),ke=Xt(Jt),Be.attrName=ke,Be.attrValue=H,Be.keepAttr=!0,Be.forceKeepAttr=void 0,tt("uponSanitizeAttribute",C,Be),H=Be.attrValue,!Be.forceKeepAttr&&(ir(Jt,C),Be.keepAttr))if(L(/\/>/i,H))ir(Jt,C);else{bn&&(H=Se(H,Ir," "),H=Se(H,Lr," "));var bo=Xt(C.nodeName);if(Fl(bo,ke,H))try{pt?C.setAttributeNS(pt,Jt,H):C.setAttribute(Jt,H),ue(Q.removed)}catch{}}}tt("afterSanitizeAttributes",C,null)}},Es=function C(q){var H,ke=Dl(q);for(tt("beforeSanitizeShadowDOM",q,null);H=ke.nextNode();)tt("uponSanitizeShadowNode",H,null),ar(H)||(H.content instanceof ze&&C(H.content),ur(H));tt("afterSanitizeShadowDOM",q,null)};return Q.sanitize=function(C,q){var H,ke,Le,Ge,Be;if((jl=!C)&&(C="<!-->"),typeof C!="string"&&!sr(C)){if(typeof C.toString!="function")throw $("toString is not a function");if(typeof(C=C.toString())!="string")throw $("dirty is not a string, aborting")}if(!Q.isSupported){if(c(W.toStaticHTML)==="object"||typeof W.toStaticHTML=="function"){if(typeof C=="string")return W.toStaticHTML(C);if(sr(C))return W.toStaticHTML(C.outerHTML)}return C}if(xo||No(q),Q.removed=[],typeof C=="string"&&(In=!1),In){if(C.nodeName){var cr=Xt(C.nodeName);if(!Ue[cr]||Nn[cr])throw $("root node is forbidden and cannot be sanitized in-place")}}else if(C instanceof Yt)(ke=(H=zl("<!---->")).ownerDocument.importNode(C,!0)).nodeType===1&&ke.nodeName==="BODY"||ke.nodeName==="HTML"?H=ke:H.appendChild(ke);else{if(!Pn&&!bn&&!cn&&C.indexOf("<")===-1)return zt&&On?zt.createHTML(C):C;if(!(H=zl(C)))return Pn?null:On?xl:""}H&&So&&Ut(H.firstChild);for(var Jt=Dl(In?C:H);Le=Jt.nextNode();)Le.nodeType===3&&Le===Ge||ar(Le)||(Le.content instanceof ze&&Es(Le.content),ur(Le),Ge=Le);if(Ge=null,In)return C;if(Pn){if(Ar)for(Be=Sl.call(H.ownerDocument);H.firstChild;)Be.appendChild(H.firstChild);else Be=H;return $e.shadowroot&&(Be=ws.call(le,Be,!0)),Be}var pt=cn?H.outerHTML:H.innerHTML;return cn&&Ue["!doctype"]&&H.ownerDocument&&H.ownerDocument.doctype&&H.ownerDocument.doctype.name&&L(ds,H.ownerDocument.doctype.name)&&(pt="<!DOCTYPE "+H.ownerDocument.doctype.name+`>
`+pt),bn&&(pt=Se(pt,Ir," "),pt=Se(pt,Lr," ")),zt&&On?zt.createHTML(pt):pt},Q.setConfig=function(C){No(C),xo=!0},Q.clearConfig=function(){Mn=null,xo=!1},Q.isValidAttribute=function(C,q,H){Mn||No({});var ke=Xt(C),Le=Xt(q);return Fl(ke,Le,H)},Q.addHook=function(C,q){typeof q=="function"&&(kt[C]=kt[C]||[],he(kt[C],q))},Q.removeHook=function(C){if(kt[C])return ue(kt[C])},Q.removeHooks=function(C){kt[C]&&(kt[C]=[])},Q.removeAllHooks=function(){kt={}},Q}return kl()}()},729:a=>{var c=Object.prototype.hasOwnProperty,d="~";function h(){}function v(x,_,I){this.fn=x,this.context=_,this.once=I||!1}function S(x,_,I,g,p){if(typeof I!="function")throw new TypeError("The listener must be a function");var f=new v(I,g||x,p),P=d?d+_:_;return x._events[P]?x._events[P].fn?x._events[P]=[x._events[P],f]:x._events[P].push(f):(x._events[P]=f,x._eventsCount++),x}function T(x,_){--x._eventsCount==0?x._events=new h:delete x._events[_]}function k(){this._events=new h,this._eventsCount=0}Object.create&&(h.prototype=Object.create(null),new h().__proto__||(d=!1)),k.prototype.eventNames=function(){var x,_,I=[];if(this._eventsCount===0)return I;for(_ in x=this._events)c.call(x,_)&&I.push(d?_.slice(1):_);return Object.getOwnPropertySymbols?I.concat(Object.getOwnPropertySymbols(x)):I},k.prototype.listeners=function(x){var _=d?d+x:x,I=this._events[_];if(!I)return[];if(I.fn)return[I.fn];for(var g=0,p=I.length,f=new Array(p);g<p;g++)f[g]=I[g].fn;return f},k.prototype.listenerCount=function(x){var _=d?d+x:x,I=this._events[_];return I?I.fn?1:I.length:0},k.prototype.emit=function(x,_,I,g,p,f){var P=d?d+x:x;if(!this._events[P])return!1;var N,O,m=this._events[P],w=arguments.length;if(m.fn){switch(m.once&&this.removeListener(x,m.fn,void 0,!0),w){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,_),!0;case 3:return m.fn.call(m.context,_,I),!0;case 4:return m.fn.call(m.context,_,I,g),!0;case 5:return m.fn.call(m.context,_,I,g,p),!0;case 6:return m.fn.call(m.context,_,I,g,p,f),!0}for(O=1,N=new Array(w-1);O<w;O++)N[O-1]=arguments[O];m.fn.apply(m.context,N)}else{var R,j=m.length;for(O=0;O<j;O++)switch(m[O].once&&this.removeListener(x,m[O].fn,void 0,!0),w){case 1:m[O].fn.call(m[O].context);break;case 2:m[O].fn.call(m[O].context,_);break;case 3:m[O].fn.call(m[O].context,_,I);break;case 4:m[O].fn.call(m[O].context,_,I,g);break;default:if(!N)for(R=1,N=new Array(w-1);R<w;R++)N[R-1]=arguments[R];m[O].fn.apply(m[O].context,N)}}return!0},k.prototype.on=function(x,_,I){return S(this,x,_,I,!1)},k.prototype.once=function(x,_,I){return S(this,x,_,I,!0)},k.prototype.removeListener=function(x,_,I,g){var p=d?d+x:x;if(!this._events[p])return this;if(!_)return T(this,p),this;var f=this._events[p];if(f.fn)f.fn!==_||g&&!f.once||I&&f.context!==I||T(this,p);else{for(var P=0,N=[],O=f.length;P<O;P++)(f[P].fn!==_||g&&!f[P].once||I&&f[P].context!==I)&&N.push(f[P]);N.length?this._events[p]=N.length===1?N[0]:N:T(this,p)}return this},k.prototype.removeAllListeners=function(x){var _;return x?(_=d?d+x:x,this._events[_]&&T(this,_)):(this._events=new h,this._eventsCount=0),this},k.prototype.off=k.prototype.removeListener,k.prototype.addListener=k.prototype.on,k.prefixed=d,k.EventEmitter=k,a.exports=k},717:a=>{typeof Object.create=="function"?a.exports=function(c,d){c.super_=d,c.prototype=Object.create(d.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}})}:a.exports=function(c,d){c.super_=d;var h=function(){};h.prototype=d.prototype,c.prototype=new h,c.prototype.constructor=c}},824:a=>{var c=1e3,d=60*c,h=60*d,v=24*h,S=7*v,T=365.25*v;function k(x,_,I,g){var p=_>=1.5*I;return Math.round(x/I)+" "+g+(p?"s":"")}a.exports=function(x,_){_=_||{};var I=typeof x;if(I==="string"&&x.length>0)return function(g){if(!((g=String(g)).length>100)){var p=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(g);if(p){var f=parseFloat(p[1]);switch((p[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return f*T;case"weeks":case"week":case"w":return f*S;case"days":case"day":case"d":return f*v;case"hours":case"hour":case"hrs":case"hr":case"h":return f*h;case"minutes":case"minute":case"mins":case"min":case"m":return f*d;case"seconds":case"second":case"secs":case"sec":case"s":return f*c;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return f;default:return}}}}(x);if(I==="number"&&isFinite(x))return _.long?function(g){var p=Math.abs(g);return p>=v?k(g,p,v,"day"):p>=h?k(g,p,h,"hour"):p>=d?k(g,p,d,"minute"):p>=c?k(g,p,c,"second"):g+" ms"}(x):function(g){var p=Math.abs(g);return p>=v?Math.round(g/v)+"d":p>=h?Math.round(g/h)+"h":p>=d?Math.round(g/d)+"m":p>=c?Math.round(g/c)+"s":g+"ms"}(x);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(x))}},520:(a,c,d)=>{var h=d(155),v=h.platform==="win32",S=d(539);function T(m,w){for(var R=[],j=0;j<m.length;j++){var J=m[j];J&&J!=="."&&(J===".."?R.length&&R[R.length-1]!==".."?R.pop():w&&R.push(".."):R.push(J))}return R}function k(m){for(var w=m.length-1,R=0;R<=w&&!m[R];R++);for(var j=w;j>=0&&!m[j];j--);return R===0&&j===w?m:R>j?[]:m.slice(R,j+1)}var x=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,_=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,I={};function g(m){var w=x.exec(m),R=(w[1]||"")+(w[2]||""),j=w[3]||"",J=_.exec(j);return[R,J[1],J[2],J[3]]}function p(m){var w=x.exec(m),R=w[1]||"",j=!!R&&R[1]!==":";return{device:R,isUnc:j,isAbsolute:j||!!w[2],tail:w[3]}}function f(m){return"\\\\"+m.replace(/^[\\\/]+/,"").replace(/[\\\/]+/g,"\\")}I.resolve=function(){for(var m="",w="",R=!1,j=arguments.length-1;j>=-1;j--){var J;if(j>=0?J=arguments[j]:m?(J=h.env["="+m])&&J.substr(0,3).toLowerCase()===m.toLowerCase()+"\\"||(J=m+"\\"):J=h.cwd(),!S.isString(J))throw new TypeError("Arguments to path.resolve must be strings");if(J){var ce=p(J),ue=ce.device,he=ce.isUnc,oe=ce.isAbsolute,Ee=ce.tail;if((!ue||!m||ue.toLowerCase()===m.toLowerCase())&&(m||(m=ue),R||(w=Ee+"\\"+w,R=oe),m&&R))break}}return he&&(m=f(m)),m+(R?"\\":"")+(w=T(w.split(/[\\\/]+/),!R).join("\\"))||"."},I.normalize=function(m){var w=p(m),R=w.device,j=w.isUnc,J=w.isAbsolute,ce=w.tail,ue=/[\\\/]$/.test(ce);return(ce=T(ce.split(/[\\\/]+/),!J).join("\\"))||J||(ce="."),ce&&ue&&(ce+="\\"),j&&(R=f(R)),R+(J?"\\":"")+ce},I.isAbsolute=function(m){return p(m).isAbsolute},I.join=function(){for(var m=[],w=0;w<arguments.length;w++){var R=arguments[w];if(!S.isString(R))throw new TypeError("Arguments to path.join must be strings");R&&m.push(R)}var j=m.join("\\");return/^[\\\/]{2}[^\\\/]/.test(m[0])||(j=j.replace(/^[\\\/]{2,}/,"\\")),I.normalize(j)},I.relative=function(m,w){m=I.resolve(m),w=I.resolve(w);for(var R=m.toLowerCase(),j=w.toLowerCase(),J=k(w.split("\\")),ce=k(R.split("\\")),ue=k(j.split("\\")),he=Math.min(ce.length,ue.length),oe=he,Ee=0;Ee<he;Ee++)if(ce[Ee]!==ue[Ee]){oe=Ee;break}if(oe==0)return w;var Se=[];for(Ee=oe;Ee<ce.length;Ee++)Se.push("..");return(Se=Se.concat(J.slice(oe))).join("\\")},I._makeLong=function(m){if(!S.isString(m))return m;if(!m)return"";var w=I.resolve(m);return/^[a-zA-Z]\:\\/.test(w)?"\\\\?\\"+w:/^\\\\[^?.]/.test(w)?"\\\\?\\UNC\\"+w.substring(2):m},I.dirname=function(m){var w=g(m),R=w[0],j=w[1];return R||j?(j&&(j=j.substr(0,j.length-1)),R+j):"."},I.basename=function(m,w){var R=g(m)[2];return w&&R.substr(-1*w.length)===w&&(R=R.substr(0,R.length-w.length)),R},I.extname=function(m){return g(m)[3]},I.format=function(m){if(!S.isObject(m))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof m);var w=m.root||"";if(!S.isString(w))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof m.root);var R=m.dir,j=m.base||"";return R?R[R.length-1]===I.sep?R+j:R+I.sep+j:j},I.parse=function(m){if(!S.isString(m))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof m);var w=g(m);if(!w||w.length!==4)throw new TypeError("Invalid path '"+m+"'");return{root:w[0],dir:w[0]+w[1].slice(0,-1),base:w[2],ext:w[3],name:w[2].slice(0,w[2].length-w[3].length)}},I.sep="\\",I.delimiter=";";var P=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,N={};function O(m){return P.exec(m).slice(1)}N.resolve=function(){for(var m="",w=!1,R=arguments.length-1;R>=-1&&!w;R--){var j=R>=0?arguments[R]:h.cwd();if(!S.isString(j))throw new TypeError("Arguments to path.resolve must be strings");j&&(m=j+"/"+m,w=j[0]==="/")}return(w?"/":"")+(m=T(m.split("/"),!w).join("/"))||"."},N.normalize=function(m){var w=N.isAbsolute(m),R=m&&m[m.length-1]==="/";return(m=T(m.split("/"),!w).join("/"))||w||(m="."),m&&R&&(m+="/"),(w?"/":"")+m},N.isAbsolute=function(m){return m.charAt(0)==="/"},N.join=function(){for(var m="",w=0;w<arguments.length;w++){var R=arguments[w];if(!S.isString(R))throw new TypeError("Arguments to path.join must be strings");R&&(m+=m?"/"+R:R)}return N.normalize(m)},N.relative=function(m,w){m=N.resolve(m).substr(1),w=N.resolve(w).substr(1);for(var R=k(m.split("/")),j=k(w.split("/")),J=Math.min(R.length,j.length),ce=J,ue=0;ue<J;ue++)if(R[ue]!==j[ue]){ce=ue;break}var he=[];for(ue=ce;ue<R.length;ue++)he.push("..");return(he=he.concat(j.slice(ce))).join("/")},N._makeLong=function(m){return m},N.dirname=function(m){var w=O(m),R=w[0],j=w[1];return R||j?(j&&(j=j.substr(0,j.length-1)),R+j):"."},N.basename=function(m,w){var R=O(m)[2];return w&&R.substr(-1*w.length)===w&&(R=R.substr(0,R.length-w.length)),R},N.extname=function(m){return O(m)[3]},N.format=function(m){if(!S.isObject(m))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof m);var w=m.root||"";if(!S.isString(w))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof m.root);return(m.dir?m.dir+N.sep:"")+(m.base||"")},N.parse=function(m){if(!S.isString(m))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof m);var w=O(m);if(!w||w.length!==4)throw new TypeError("Invalid path '"+m+"'");return w[1]=w[1]||"",w[2]=w[2]||"",w[3]=w[3]||"",{root:w[0],dir:w[0]+w[1].slice(0,-1),base:w[2],ext:w[3],name:w[2].slice(0,w[2].length-w[3].length)}},N.sep="/",N.delimiter=":",a.exports=v?I:N,a.exports.posix=N,a.exports.win32=I},155:a=>{var c,d,h=a.exports={};function v(){throw new Error("setTimeout has not been defined")}function S(){throw new Error("clearTimeout has not been defined")}function T(N){if(c===setTimeout)return setTimeout(N,0);if((c===v||!c)&&setTimeout)return c=setTimeout,setTimeout(N,0);try{return c(N,0)}catch{try{return c.call(null,N,0)}catch{return c.call(this,N,0)}}}(function(){try{c=typeof setTimeout=="function"?setTimeout:v}catch{c=v}try{d=typeof clearTimeout=="function"?clearTimeout:S}catch{d=S}})();var k,x=[],_=!1,I=-1;function g(){_&&k&&(_=!1,k.length?x=k.concat(x):I=-1,x.length&&p())}function p(){if(!_){var N=T(g);_=!0;for(var O=x.length;O;){for(k=x,x=[];++I<O;)k&&k[I].run();I=-1,O=x.length}k=null,_=!1,function(m){if(d===clearTimeout)return clearTimeout(m);if((d===S||!d)&&clearTimeout)return d=clearTimeout,clearTimeout(m);try{d(m)}catch{try{return d.call(null,m)}catch{return d.call(this,m)}}}(N)}}function f(N,O){this.fun=N,this.array=O}function P(){}h.nextTick=function(N){var O=new Array(arguments.length-1);if(arguments.length>1)for(var m=1;m<arguments.length;m++)O[m-1]=arguments[m];x.push(new f(N,O)),x.length!==1||_||T(p)},f.prototype.run=function(){this.fun.apply(null,this.array)},h.title="browser",h.browser=!0,h.env={},h.argv=[],h.version="",h.versions={},h.on=P,h.addListener=P,h.once=P,h.off=P,h.removeListener=P,h.removeAllListeners=P,h.emit=P,h.prependListener=P,h.prependOnceListener=P,h.listeners=function(N){return[]},h.binding=function(N){throw new Error("process.binding is not supported")},h.cwd=function(){return"/"},h.chdir=function(N){throw new Error("process.chdir is not supported")},h.umask=function(){return 0}},384:a=>{a.exports=function(c){return c&&typeof c=="object"&&typeof c.copy=="function"&&typeof c.fill=="function"&&typeof c.readUInt8=="function"}},539:(a,c,d)=>{var h=d(155),v=/%[sdj%]/g;c.format=function(E){if(!m(E)){for(var L=[],$=0;$<arguments.length;$++)L.push(k(arguments[$]));return L.join(" ")}$=1;for(var te=arguments,ye=te.length,B=String(E).replace(v,function(ae){if(ae==="%%")return"%";if($>=ye)return ae;switch(ae){case"%s":return String(te[$++]);case"%d":return Number(te[$++]);case"%j":try{return JSON.stringify(te[$++])}catch{return"[Circular]"}default:return ae}}),ne=te[$];$<ye;ne=te[++$])N(ne)||!j(ne)?B+=" "+ne:B+=" "+k(ne);return B},c.deprecate=function(E,L){if(w(d.g.process))return function(){return c.deprecate(E,L).apply(this,arguments)};if(h.noDeprecation===!0)return E;var $=!1;return function(){if(!$){if(h.throwDeprecation)throw new Error(L);h.traceDeprecation?console.trace(L):console.error(L),$=!0}return E.apply(this,arguments)}};var S,T={};function k(E,L){var $={seen:[],stylize:_};return arguments.length>=3&&($.depth=arguments[2]),arguments.length>=4&&($.colors=arguments[3]),P(L)?$.showHidden=L:L&&c._extend($,L),w($.showHidden)&&($.showHidden=!1),w($.depth)&&($.depth=2),w($.colors)&&($.colors=!1),w($.customInspect)&&($.customInspect=!0),$.colors&&($.stylize=x),I($,E,$.depth)}function x(E,L){var $=k.styles[L];return $?"\x1B["+k.colors[$][0]+"m"+E+"\x1B["+k.colors[$][1]+"m":E}function _(E,L){return E}function I(E,L,$){if(E.customInspect&&L&&ue(L.inspect)&&L.inspect!==c.inspect&&(!L.constructor||L.constructor.prototype!==L)){var te=L.inspect($,E);return m(te)||(te=I(E,te,$)),te}var ye=function(Y,ve){if(w(ve))return Y.stylize("undefined","undefined");if(m(ve)){var _e="'"+JSON.stringify(ve).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return Y.stylize(_e,"string")}if(O(ve))return Y.stylize(""+ve,"number");if(P(ve))return Y.stylize(""+ve,"boolean");if(N(ve))return Y.stylize("null","null")}(E,L);if(ye)return ye;var B=Object.keys(L),ne=function(Y){var ve={};return Y.forEach(function(_e,qe){ve[_e]=!0}),ve}(B);if(E.showHidden&&(B=Object.getOwnPropertyNames(L)),ce(L)&&(B.indexOf("message")>=0||B.indexOf("description")>=0))return g(L);if(B.length===0){if(ue(L)){var ae=L.name?": "+L.name:"";return E.stylize("[Function"+ae+"]","special")}if(R(L))return E.stylize(RegExp.prototype.toString.call(L),"regexp");if(J(L))return E.stylize(Date.prototype.toString.call(L),"date");if(ce(L))return g(L)}var Re,Ve="",it=!1,K=["{","}"];return f(L)&&(it=!0,K=["[","]"]),ue(L)&&(Ve=" [Function"+(L.name?": "+L.name:"")+"]"),R(L)&&(Ve=" "+RegExp.prototype.toString.call(L)),J(L)&&(Ve=" "+Date.prototype.toUTCString.call(L)),ce(L)&&(Ve=" "+g(L)),B.length!==0||it&&L.length!=0?$<0?R(L)?E.stylize(RegExp.prototype.toString.call(L),"regexp"):E.stylize("[Object]","special"):(E.seen.push(L),Re=it?function(Y,ve,_e,qe,dt){for(var an=[],wt=0,ft=ve.length;wt<ft;++wt)z(ve,String(wt))?an.push(p(Y,ve,_e,qe,String(wt),!0)):an.push("");return dt.forEach(function(tr){tr.match(/^\d+$/)||an.push(p(Y,ve,_e,qe,tr,!0))}),an}(E,L,$,ne,B):B.map(function(Y){return p(E,L,$,ne,Y,it)}),E.seen.pop(),function(Y,ve,_e){return Y.reduce(function(qe,dt){return dt.indexOf(`
`)>=0,qe+dt.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?_e[0]+(ve===""?"":ve+`
 `)+" "+Y.join(`,
  `)+" "+_e[1]:_e[0]+ve+" "+Y.join(", ")+" "+_e[1]}(Re,Ve,K)):K[0]+Ve+K[1]}function g(E){return"["+Error.prototype.toString.call(E)+"]"}function p(E,L,$,te,ye,B){var ne,ae,Re;if((Re=Object.getOwnPropertyDescriptor(L,ye)||{value:L[ye]}).get?ae=Re.set?E.stylize("[Getter/Setter]","special"):E.stylize("[Getter]","special"):Re.set&&(ae=E.stylize("[Setter]","special")),z(te,ye)||(ne="["+ye+"]"),ae||(E.seen.indexOf(Re.value)<0?(ae=N($)?I(E,Re.value,null):I(E,Re.value,$-1)).indexOf(`
`)>-1&&(ae=B?ae.split(`
`).map(function(Ve){return"  "+Ve}).join(`
`).substr(2):`
`+ae.split(`
`).map(function(Ve){return"   "+Ve}).join(`
`)):ae=E.stylize("[Circular]","special")),w(ne)){if(B&&ye.match(/^\d+$/))return ae;(ne=JSON.stringify(""+ye)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(ne=ne.substr(1,ne.length-2),ne=E.stylize(ne,"name")):(ne=ne.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),ne=E.stylize(ne,"string"))}return ne+": "+ae}function f(E){return Array.isArray(E)}function P(E){return typeof E=="boolean"}function N(E){return E===null}function O(E){return typeof E=="number"}function m(E){return typeof E=="string"}function w(E){return E===void 0}function R(E){return j(E)&&he(E)==="[object RegExp]"}function j(E){return typeof E=="object"&&E!==null}function J(E){return j(E)&&he(E)==="[object Date]"}function ce(E){return j(E)&&(he(E)==="[object Error]"||E instanceof Error)}function ue(E){return typeof E=="function"}function he(E){return Object.prototype.toString.call(E)}function oe(E){return E<10?"0"+E.toString(10):E.toString(10)}c.debuglog=function(E){if(w(S)&&(S=h.env.NODE_DEBUG||""),E=E.toUpperCase(),!T[E])if(new RegExp("\\b"+E+"\\b","i").test(S)){var L=h.pid;T[E]=function(){var $=c.format.apply(c,arguments);console.error("%s %d: %s",E,L,$)}}else T[E]=function(){};return T[E]},c.inspect=k,k.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},k.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},c.isArray=f,c.isBoolean=P,c.isNull=N,c.isNullOrUndefined=function(E){return E==null},c.isNumber=O,c.isString=m,c.isSymbol=function(E){return typeof E=="symbol"},c.isUndefined=w,c.isRegExp=R,c.isObject=j,c.isDate=J,c.isError=ce,c.isFunction=ue,c.isPrimitive=function(E){return E===null||typeof E=="boolean"||typeof E=="number"||typeof E=="string"||typeof E=="symbol"||E===void 0},c.isBuffer=d(384);var Ee=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function Se(){var E=new Date,L=[oe(E.getHours()),oe(E.getMinutes()),oe(E.getSeconds())].join(":");return[E.getDate(),Ee[E.getMonth()],L].join(" ")}function z(E,L){return Object.prototype.hasOwnProperty.call(E,L)}c.log=function(){console.log("%s - %s",Se(),c.format.apply(c,arguments))},c.inherits=d(717),c._extend=function(E,L){if(!L||!j(L))return E;for(var $=Object.keys(L),te=$.length;te--;)E[$[te]]=L[$[te]];return E}}},r={};function o(a){var c=r[a];if(c!==void 0)return c.exports;var d=r[a]={exports:{}};return n[a].call(d.exports,d,d.exports,o),d.exports}o.n=a=>{var c=a&&a.__esModule?()=>a.default:()=>a;return o.d(c,{a:c}),c},o.d=(a,c)=>{for(var d in c)o.o(c,d)&&!o.o(a,d)&&Object.defineProperty(a,d,{enumerable:!0,get:c[d]})},o.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}(),o.o=(a,c)=>Object.prototype.hasOwnProperty.call(a,c),o.r=a=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})};var l={};return(()=>{o.r(l),o.d(l,{LSPluginUser:()=>js,setupPluginUserInstance:()=>kc});var a=o(520),c=(o(856),o(996)),d=o.n(c),h=function(){return h=Object.assign||function(s){for(var i,u=1,y=arguments.length;u<y;u++)for(var b in i=arguments[u])Object.prototype.hasOwnProperty.call(i,b)&&(s[b]=i[b]);return s},h.apply(this,arguments)};function v(s){return s.toLowerCase()}var S=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],T=/[^A-Z0-9]+/gi;function k(s,i,u){return i instanceof RegExp?s.replace(i,u):i.reduce(function(y,b){return y.replace(b,u)},s)}function x(s,i){return i===void 0&&(i={}),function(u,y){y===void 0&&(y={});for(var b=y.splitRegexp,M=b===void 0?S:b,D=y.stripRegexp,F=D===void 0?T:D,V=y.transform,Z=V===void 0?v:V,re=y.delimiter,X=re===void 0?" ":re,ie=k(k(u,M,"$1\0$2"),F,"\0"),me=0,de=ie.length;ie.charAt(me)==="\0";)me++;for(;ie.charAt(de-1)==="\0";)de--;return ie.slice(me,de).split("\0").map(Z).join(X)}(s,h({delimiter:"."},i))}var _=o(729),I=o.n(_);function g(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}const p=navigator.platform.toLowerCase()==="win32"?a.win32:a.posix,f=function(s,i){return i===void 0&&(i={}),x(s,h({delimiter:"_"},i))};class P extends I(){constructor(i,u){super(),g(this,"_tag",void 0),g(this,"_opts",void 0),g(this,"_logs",[]),this._tag=i,this._opts=u}write(i,u,y){var b;u!=null&&u.length&&u[u.length-1]===!0&&(y=!0,u.pop());const M=u.reduce((F,V)=>(V&&V instanceof Error?F+=`${V.message} ${V.stack}`:F+=V.toString(),F),`[${this._tag}][${new Date().toLocaleTimeString()}] `);var D;this._logs.push([i,M]),(y||(b=this._opts)!==null&&b!==void 0&&b.console)&&((D=console)===null||D===void 0||D[i==="ERROR"?"error":"debug"](`${i}: ${M}`)),this.emit("change")}clear(){this._logs=[],this.emit("change")}info(...i){this.write("INFO",i)}error(...i){this.write("ERROR",i)}warn(...i){this.write("WARN",i)}setTag(i){this._tag=i}toJSON(){return this._logs}}function N(s,...i){try{const u=new URL(s);if(!u.origin)throw new Error(null);const y=p.join(s.substr(u.origin.length),...i);return u.origin+y}catch{return p.join(s,...i)}}function O(s,i){let u,y,b=!1;const M=F=>V=>{s&&clearTimeout(s),F(V),b=!0},D=new Promise((F,V)=>{u=M(F),y=M(V),s&&(s=setTimeout(()=>y(new Error(`[deferred timeout] ${i}`)),s))});return{created:Date.now(),setTag:F=>i=F,resolve:u,reject:y,promise:D,get settled(){return b}}}const m=new Map;window.__injectedUIEffects=m;var w=o(227),R=o.n(w);function j(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}const J="application/x-postmate-v1+json";let ce=0;const ue={handshake:1,"handshake-reply":1,call:1,emit:1,reply:1,request:1},he=(s,i)=>(typeof i!="string"||s.origin===i)&&!!s.data&&(typeof s.data!="object"||"postmate"in s.data)&&s.data.type===J&&!!ue[s.data.postmate];class oe{constructor(i){j(this,"parent",void 0),j(this,"frame",void 0),j(this,"child",void 0),j(this,"events",{}),j(this,"childOrigin",void 0),j(this,"listener",void 0),this.parent=i.parent,this.frame=i.frame,this.child=i.child,this.childOrigin=i.childOrigin,this.listener=u=>{if(!he(u,this.childOrigin))return!1;const{data:y,name:b}=((u||{}).data||{}).value||{};u.data.postmate==="emit"&&b in this.events&&this.events[b].forEach(M=>{M.call(this,y)})},this.parent.addEventListener("message",this.listener,!1)}get(i,...u){return new Promise((y,b)=>{const M=++ce,D=F=>{F.data.uid===M&&F.data.postmate==="reply"&&(this.parent.removeEventListener("message",D,!1),F.data.error?b(F.data.error):y(F.data.value))};this.parent.addEventListener("message",D,!1),this.child.postMessage({postmate:"request",type:J,property:i,args:u,uid:M},this.childOrigin)})}call(i,u){this.child.postMessage({postmate:"call",type:J,property:i,data:u},this.childOrigin)}on(i,u){this.events[i]||(this.events[i]=[]),this.events[i].push(u)}destroy(){window.removeEventListener("message",this.listener,!1),this.frame.parentNode.removeChild(this.frame)}}class Ee{constructor(i){j(this,"model",void 0),j(this,"parent",void 0),j(this,"parentOrigin",void 0),j(this,"child",void 0),this.model=i.model,this.parent=i.parent,this.parentOrigin=i.parentOrigin,this.child=i.child,this.child.addEventListener("message",u=>{if(!he(u,this.parentOrigin))return;const{property:y,uid:b,data:M,args:D}=u.data;u.data.postmate!=="call"?((F,V,Z)=>{const re=typeof F[V]=="function"?F[V].apply(null,Z):F[V];return Promise.resolve(re)})(this.model,y,D).then(F=>{u.source.postMessage({property:y,postmate:"reply",type:J,uid:b,value:F},u.origin)}).catch(F=>{u.source.postMessage({property:y,postmate:"reply",type:J,uid:b,error:F},u.origin)}):y in this.model&&typeof this.model[y]=="function"&&this.model[y](M)})}emit(i,u){this.parent.postMessage({postmate:"emit",type:J,value:{name:i,data:u}},this.parentOrigin)}}class Se{constructor(i){j(this,"container",void 0),j(this,"parent",void 0),j(this,"frame",void 0),j(this,"child",void 0),j(this,"childOrigin",void 0),j(this,"url",void 0),j(this,"model",void 0),this.container=i.container,this.url=i.url,this.parent=window,this.frame=document.createElement("iframe"),i.id&&(this.frame.id=i.id),i.name&&(this.frame.name=i.name),this.frame.classList.add.apply(this.frame.classList,i.classListArray||[]),this.container.appendChild(this.frame),this.child=this.frame.contentWindow,this.model=i.model||{}}sendHandshake(i){const u=(M=>{const D=document.createElement("a");D.href=M;const F=D.protocol.length>4?D.protocol:window.location.protocol,V=D.host.length?D.port==="80"||D.port==="443"?D.hostname:D.host:window.location.host;return D.origin||`${F}//${V}`})(i=i||this.url);let y,b=0;return new Promise((M,D)=>{const F=Z=>!!he(Z,u)&&(Z.data.postmate==="handshake-reply"?(clearInterval(y),this.parent.removeEventListener("message",F,!1),this.childOrigin=Z.origin,M(new oe(this))):D("Failed handshake"));this.parent.addEventListener("message",F,!1);const V=()=>{b++,this.child.postMessage({postmate:"handshake",type:J,model:this.model},u),b===5&&clearInterval(y)};this.frame.addEventListener("load",()=>{V(),y=setInterval(V,500)}),this.frame.src=i})}destroy(){this.frame.parentNode.removeChild(this.frame)}}j(Se,"debug",!1),j(Se,"Model",void 0);class z{constructor(i){j(this,"child",void 0),j(this,"model",void 0),j(this,"parent",void 0),j(this,"parentOrigin",void 0),this.child=window,this.model=i,this.parent=this.child.parent}sendHandshakeReply(){return new Promise((i,u)=>{const y=b=>{if(b.data.postmate){if(b.data.postmate==="handshake"){this.child.removeEventListener("message",y,!1),b.source.postMessage({postmate:"handshake-reply",type:J},b.origin),this.parentOrigin=b.origin;const M=b.data.model;return M&&Object.keys(M).forEach(D=>{this.model[D]=M[D]}),i(new Ee(this))}return u("Handshake Reply Failed")}};this.child.addEventListener("message",y,!1)})}}function E(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}const{importHTML:L,createSandboxContainer:$}=window.QSandbox||{};function te(s,i){return s.startsWith("http")?fetch(s,i):(s=s.replace("file://",""),new Promise(async(u,y)=>{try{const b=await window.apis.doAction(["readFile",s]);u({text:()=>b})}catch(b){console.error(b),y(b)}}))}class ye extends I(){constructor(i){super(),E(this,"_pluginLocal",void 0),E(this,"_frame",void 0),E(this,"_root",void 0),E(this,"_loaded",!1),E(this,"_unmountFns",[]),this._pluginLocal=i,i._dispose(()=>{this._unmount()})}async load(){const{name:i,entry:u}=this._pluginLocal.options;if(this.loaded||!u)return;const{template:y,execScripts:b}=await L(u,{fetch:te});this._mount(y,document.body);const M=$(i,{elementGetter:()=>{var F;return(F=this._root)===null||F===void 0?void 0:F.firstChild}}).instance.proxy;M.__shadow_mode__=!0,M.LSPluginLocal=this._pluginLocal,M.LSPluginShadow=this,M.LSPluginUser=M.logseq=new js(this._pluginLocal.toJSON(),this._pluginLocal.caller);const D=await b(M,!0);this._unmountFns.push(D.unmount),this._loaded=!0}_mount(i,u){const y=this._frame=document.createElement("div");y.classList.add("lsp-shadow-sandbox"),y.id=this._pluginLocal.id,this._root=y.attachShadow({mode:"open"}),this._root.innerHTML=`<div>${i}</div>`,u.appendChild(y),this.emit("mounted")}_unmount(){for(const i of this._unmountFns)i&&i.call(null)}destroy(){var i,u;(i=this.frame)===null||i===void 0||(u=i.parentNode)===null||u===void 0||u.removeChild(this.frame)}get loaded(){return this._loaded}get document(){var i;return(i=this._root)===null||i===void 0?void 0:i.firstChild}get frame(){return this._frame}}function B(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}const ne=R()("LSPlugin:caller"),ae="#await#response#",Re="#lspmsg#",Ve="#lspmsg#error#",it=s=>`#lspmsg#${s}`;class K extends I(){constructor(i){super(),B(this,"_pluginLocal",void 0),B(this,"_connected",!1),B(this,"_parent",void 0),B(this,"_child",void 0),B(this,"_shadow",void 0),B(this,"_status",void 0),B(this,"_userModel",{}),B(this,"_call",void 0),B(this,"_callUserModel",void 0),B(this,"_debugTag",""),this._pluginLocal=i,i&&(this._debugTag=i.debugTag)}async connectToChild(){if(this._connected)return;const{shadow:i}=this._pluginLocal;i?await this._setupShadowSandbox():await this._setupIframeSandbox()}async connectToParent(i={}){if(this._connected)return;const u=this,y=this._pluginLocal!=null;let b=0;const M=new Map,D=O(6e4),F=this._extendUserModel({"#lspmsg#ready#":async re=>{F[it(re?.pid)]=({type:X,payload:ie})=>{ne(`[host (_call) -> *user] ${this._debugTag}`,X,ie),u.emit(X,ie)},await D.resolve()},"#lspmsg#beforeunload#":async re=>{const X=O(1e4);u.emit("beforeunload",Object.assign({actor:X},re)),await X.promise},"#lspmsg#settings#":async({type:re,payload:X})=>{u.emit("settings:changed",X)},[Re]:async({ns:re,type:X,payload:ie})=>{ne(`[host (async) -> *user] ${this._debugTag} ns=${re} type=${X}`,ie),re&&re.startsWith("hook")?u.emit(`${re}:${X}`,ie):u.emit(X,ie)},"#lspmsg#reply#":({_sync:re,result:X})=>{if(ne(`[sync host -> *user] #${re}`,X),M.has(re)){const ie=M.get(re);ie&&(X!=null&&X.hasOwnProperty(Ve)?ie.reject(X[Ve]):ie.resolve(X),M.delete(re))}},...i});var V;if(y)return await D.promise,JSON.parse(JSON.stringify((V=this._pluginLocal)===null||V===void 0?void 0:V.toJSON()));const Z=new z(F).sendHandshakeReply();return this._status="pending",await Z.then(re=>{this._child=re,this._connected=!0,this._call=async(X,ie={},me)=>{if(me){const de=++b;M.set(de,me),ie._sync=de,me.setTag(`async call #${de}`),ne(`async call #${de}`)}return re.emit(it(F.baseInfo.id),{type:X,payload:ie}),me?.promise},this._callUserModel=async(X,ie)=>{try{F[X](ie)}catch{ne(`[model method] #${X} not existed`)}},setInterval(()=>{if(M.size>100)for(const[X,ie]of M)ie.settled&&M.delete(X)},18e5)}).finally(()=>{this._status=void 0}),await D.promise,F.baseInfo}async call(i,u={}){var y;return(y=this._call)===null||y===void 0?void 0:y.call(this,i,u)}async callAsync(i,u={}){var y;const b=O(1e4);return(y=this._call)===null||y===void 0?void 0:y.call(this,i,u,b)}async callUserModel(i,...u){var y;return(y=this._callUserModel)===null||y===void 0?void 0:y.apply(this,[i,...u])}async callUserModelAsync(i,...u){var y;return i=`${ae}${i}`,(y=this._callUserModel)===null||y===void 0?void 0:y.apply(this,[i,...u])}async _setupIframeSandbox(){const i=this._pluginLocal,u=i.id,y=`${u}_lsp_main`,b=new URL(i.options.entry);b.searchParams.set("__v__",i.options.version);const M=document.querySelector(`#${y}`);M&&M.parentElement.removeChild(M);const D=document.createElement("div");D.classList.add("lsp-iframe-sandbox-container"),D.id=y,D.dataset.pid=u;try{var F;const X=(F=await this._pluginLocal._loadLayoutsData())===null||F===void 0?void 0:F.$$0;if(X){D.dataset.inited_layout="true";let{width:ie,height:me,left:de,top:Ce,vw:Te,vh:He}=X;de=Math.max(de,0),de=typeof Te=="number"?`${Math.min(100*de/Te,99)}%`:`${de}px`,Ce=Math.max(Ce,45),Ce=typeof He=="number"?`${Math.min(100*Ce/He,99)}%`:`${Ce}px`,Object.assign(D.style,{width:ie+"px",height:me+"px",left:de,top:Ce})}}catch(X){console.error("[Restore Layout Error]",X)}document.body.appendChild(D);const V=new Se({id:u+"_iframe",container:D,url:b.href,classListArray:["lsp-iframe-sandbox"],model:{baseInfo:JSON.parse(JSON.stringify(i.toJSON()))}});let Z,re=V.sendHandshake();return this._status="pending",new Promise((X,ie)=>{Z=setTimeout(()=>{ie(new Error("handshake Timeout")),V.destroy()},4e3),re.then(me=>{this._parent=me,this._connected=!0,this.emit("connected"),me.on(it(i.id),({type:de,payload:Ce})=>{var Te,He;ne("[user -> *host] ",de,Ce),(Te=this._pluginLocal)===null||Te===void 0||Te.emit(de,Ce||{}),(He=this._pluginLocal)===null||He===void 0||He.caller.emit(de,Ce||{})}),this._call=async(...de)=>{await me.call(it(i.id),{type:de[0],payload:Object.assign(de[1]||{},{$$pid:i.id})})},this._callUserModel=async(de,...Ce)=>{if(de.startsWith(ae))return await me.get(de.replace(ae,""),...Ce);me.call(de,Ce?.[0])},X(null)}).catch(me=>{ie(me)}).finally(()=>{clearTimeout(Z)})}).catch(X=>{throw ne("[iframe sandbox] error",X),X}).finally(()=>{this._status=void 0})}async _setupShadowSandbox(){const i=this._pluginLocal,u=this._shadow=new ye(i);try{this._status="pending",await u.load(),this._connected=!0,this.emit("connected"),this._call=async(y,b={},M)=>{var D;return M&&(b.actor=M),(D=this._pluginLocal)===null||D===void 0||D.emit(y,Object.assign(b,{$$pid:i.id})),M?.promise},this._callUserModel=async(...y)=>{var b;let M=y[0];(b=M)!==null&&b!==void 0&&b.startsWith(ae)&&(M=M.replace(ae,""));const D=y[1]||{},F=this._userModel[M];typeof F=="function"&&await F.call(null,D)}}catch(y){throw ne("[shadow sandbox] error",y),y}finally{this._status=void 0}}_extendUserModel(i){return Object.assign(this._userModel,i)}_getSandboxIframeContainer(){var i;return(i=this._parent)===null||i===void 0?void 0:i.frame.parentNode}_getSandboxShadowContainer(){var i;return(i=this._shadow)===null||i===void 0?void 0:i.frame.parentNode}_getSandboxIframeRoot(){var i;return(i=this._parent)===null||i===void 0?void 0:i.frame}_getSandboxShadowRoot(){var i;return(i=this._shadow)===null||i===void 0?void 0:i.frame}set debugTag(i){this._debugTag=i}async destroy(){var i;let u=null;this._parent&&(u=this._getSandboxIframeContainer(),await this._parent.destroy()),this._shadow&&(u=this._getSandboxShadowContainer(),this._shadow.destroy()),(i=u)===null||i===void 0||i.parentNode.removeChild(u)}}function Y(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}class ve{constructor(i,u){Y(this,"ctx",void 0),Y(this,"opts",void 0),this.ctx=i,this.opts=u}get ctxId(){return this.ctx.baseInfo.id}setItem(i,u){var y;return this.ctx.caller.callAsync("api:call",{method:"write-plugin-storage-file",args:[this.ctxId,i,u,(y=this.opts)===null||y===void 0?void 0:y.assets]})}getItem(i){var u;return this.ctx.caller.callAsync("api:call",{method:"read-plugin-storage-file",args:[this.ctxId,i,(u=this.opts)===null||u===void 0?void 0:u.assets]})}removeItem(i){var u;return this.ctx.caller.call("api:call",{method:"unlink-plugin-storage-file",args:[this.ctxId,i,(u=this.opts)===null||u===void 0?void 0:u.assets]})}allKeys(){var i;return this.ctx.caller.callAsync("api:call",{method:"list-plugin-storage-files",args:[this.ctxId,(i=this.opts)===null||i===void 0?void 0:i.assets]})}clear(){var i;return this.ctx.caller.call("api:call",{method:"clear-plugin-storage-files",args:[this.ctxId,(i=this.opts)===null||i===void 0?void 0:i.assets]})}hasItem(i){var u;return this.ctx.caller.callAsync("api:call",{method:"exist-plugin-storage-file",args:[this.ctxId,i,(u=this.opts)===null||u===void 0?void 0:u.assets]})}}class _e{constructor(i){var u,y,b;b=void 0,(y="ctx")in(u=this)?Object.defineProperty(u,y,{value:b,enumerable:!0,configurable:!0,writable:!0}):u[y]=b,this.ctx=i}get React(){return this.ensureHostScope().React}get ReactDOM(){return this.ensureHostScope().ReactDOM}get pluginLocal(){return this.ensureHostScope().LSPluginCore.ensurePlugin(this.ctx.baseInfo.id)}invokeExperMethod(i,...u){var y,b;const M=this.ensureHostScope();return i=(y=f(i))===null||y===void 0?void 0:y.toLowerCase(),(b=M.logseq.api["exper_"+i])===null||b===void 0?void 0:b.apply(M,u)}async loadScripts(...i){(i=i.map(u=>u!=null&&u.startsWith("http")?u:this.ctx.resolveResourceFullUrl(u))).unshift(this.ctx.baseInfo.id),await this.invokeExperMethod("loadScripts",...i)}registerFencedCodeRenderer(i,u){return this.ensureHostScope().logseq.api.exper_register_fenced_code_renderer(this.ctx.baseInfo.id,i,u)}registerExtensionsEnhancer(i,u){const y=this.ensureHostScope();return i==="katex"&&y.katex&&u(y.katex).catch(console.error),y.logseq.api.exper_register_extensions_enhancer(this.ctx.baseInfo.id,i,u)}ensureHostScope(){if(window===top)throw new Error("Can not access host scope!");return top}}function qe(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}const dt=s=>`task_callback_${s}`;class an{constructor(i,u,y={}){qe(this,"_client",void 0),qe(this,"_requestId",void 0),qe(this,"_requestOptions",void 0),qe(this,"_promise",void 0),qe(this,"_aborted",!1),this._client=i,this._requestId=u,this._requestOptions=y,this._promise=new Promise((F,V)=>{if(!this._requestId)return V(null);this._client.once(dt(this._requestId),Z=>{Z&&Z instanceof Error?V(Z):F(Z)})});const{success:b,fail:M,final:D}=this._requestOptions;this._promise.then(F=>{b?.(F)}).catch(F=>{M?.(F)}).finally(()=>{D?.()})}abort(){this._requestOptions.abortable&&!this._aborted&&(this._client.ctx._execCallableAPI("http_request_abort",this._requestId),this._aborted=!0)}get promise(){return this._promise}get client(){return this._client}get requestId(){return this._requestId}}class wt extends _.EventEmitter{constructor(i){super(),qe(this,"_ctx",void 0),this._ctx=i,this.ctx.caller.on("#lsp#request#callback",u=>{const y=u?.requestId;y&&this.emit(dt(y),u?.payload)})}static createRequestTask(i,u,y){return new an(i,u,y)}async _request(i){const u=this.ctx.baseInfo.id,{success:y,fail:b,final:M,...D}=i,F=this.ctx.Experiments.invokeExperMethod("request",u,D),V=wt.createRequestTask(this.ctx.Request,F,i);return D.abortable?V:V.promise}get ctx(){return this._ctx}}const ft=Array.isArray,tr=typeof Hl=="object"&&Hl&&Hl.Object===Object&&Hl;var us=typeof self=="object"&&self&&self.Object===Object&&self;const Mt=tr||us||Function("return this")(),Gt=Mt.Symbol;var wl=Object.prototype,cs=wl.hasOwnProperty,ds=wl.toString,nr=Gt?Gt.toStringTag:void 0;const fs=function(s){var i=cs.call(s,nr),u=s[nr];try{s[nr]=void 0;var y=!0}catch{}var b=ds.call(s);return y&&(i?s[nr]=u:delete s[nr]),b};var kl=Object.prototype.toString;const W=function(s){return kl.call(s)};var Q=Gt?Gt.toStringTag:void 0;const le=function(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":Q&&Q in Object(s)?fs(s):W(s)},fe=function(s){var i=typeof s;return s!=null&&(i=="object"||i=="function")},ze=function(s){if(!fe(s))return!1;var i=le(s);return i=="[object Function]"||i=="[object GeneratorFunction]"||i=="[object AsyncFunction]"||i=="[object Proxy]"},At=Mt["__core-js_shared__"];var Yt,_n=(Yt=/[^.]+$/.exec(At&&At.keys&&At.keys.IE_PROTO||""))?"Symbol(src)_1."+Yt:"";const go=function(s){return!!_n&&_n in s};var Cl=Function.prototype.toString;const un=function(s){if(s!=null){try{return Cl.call(s)}catch{}try{return s+""}catch{}}return""};var ps=/^\[object .+?Constructor\]$/,hs=Function.prototype,ms=Object.prototype,Pr=hs.toString,gs=ms.hasOwnProperty,ys=RegExp("^"+Pr.call(gs).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const vs=function(s){return!(!fe(s)||go(s))&&(ze(s)?ys:ps).test(un(s))},yo=function(s,i){return s?.[i]},Rt=function(s,i){var u=yo(s,i);return vs(u)?u:void 0},zt=function(){try{var s=Rt(Object,"defineProperty");return s({},"",{}),s}catch{}}(),xl=function(s,i,u){i=="__proto__"&&zt?zt(s,i,{configurable:!0,enumerable:!0,value:u,writable:!0}):s[i]=u},Or=function(s){return function(i,u,y){for(var b=-1,M=Object(i),D=y(i),F=D.length;F--;){var V=D[s?F:++b];if(u(M[V],V,M)===!1)break}return i}}(),vo=function(s,i){for(var u=-1,y=Array(s);++u<s;)y[u]=i(u);return y},En=function(s){return s!=null&&typeof s=="object"},Sl=function(s){return En(s)&&le(s)=="[object Arguments]"};var _l=Object.prototype,ws=_l.hasOwnProperty,El=_l.propertyIsEnumerable;const kt=Sl(function(){return arguments}())?Sl:function(s){return En(s)&&ws.call(s,"callee")&&!El.call(s,"callee")},rr=function(){return!1};var Xt=t&&!t.nodeType&&t,Ir=Xt&&!0&&e&&!e.nodeType&&e,Lr=Ir&&Ir.exports===Xt?Mt.Buffer:void 0;const wo=(Lr?Lr.isBuffer:void 0)||rr;var ks=/^(?:0|[1-9]\d*)$/;const Tl=function(s,i){var u=typeof s;return!!(i=i??9007199254740991)&&(u=="number"||u!="symbol"&&ks.test(s))&&s>-1&&s%1==0&&s<i},jr=function(s){return typeof s=="number"&&s>-1&&s%1==0&&s<=9007199254740991};var we={};we["[object Float32Array]"]=we["[object Float64Array]"]=we["[object Int8Array]"]=we["[object Int16Array]"]=we["[object Int32Array]"]=we["[object Uint8Array]"]=we["[object Uint8ClampedArray]"]=we["[object Uint16Array]"]=we["[object Uint32Array]"]=!0,we["[object Arguments]"]=we["[object Array]"]=we["[object ArrayBuffer]"]=we["[object Boolean]"]=we["[object DataView]"]=we["[object Date]"]=we["[object Error]"]=we["[object Function]"]=we["[object Map]"]=we["[object Number]"]=we["[object Object]"]=we["[object RegExp]"]=we["[object Set]"]=we["[object String]"]=we["[object WeakMap]"]=!1;const Ue=function(s){return En(s)&&jr(s.length)&&!!we[le(s)]},Nl=function(s){return function(i){return s(i)}};var $e=t&&!t.nodeType&&t,Tn=$e&&!0&&e&&!e.nodeType&&e,Oe=Tn&&Tn.exports===$e&&tr.process,Nn=function(){try{var s=Tn&&Tn.require&&Tn.require("util").types;return s||Oe&&Oe.binding&&Oe.binding("util")}catch{}}(),Mr=Nn&&Nn.isTypedArray;const ko=Mr?Nl(Mr):Ue;var Co=Object.prototype.hasOwnProperty;const bl=function(s,i){var u=ft(s),y=!u&&kt(s),b=!u&&!y&&wo(s),M=!u&&!y&&!b&&ko(s),D=u||y||b||M,F=D?vo(s.length,String):[],V=F.length;for(var Z in s)!i&&!Co.call(s,Z)||D&&(Z=="length"||b&&(Z=="offset"||Z=="parent")||M&&(Z=="buffer"||Z=="byteLength"||Z=="byteOffset")||Tl(Z,V))||F.push(Z);return F};var bn=Object.prototype;const cn=function(s){var i=s&&s.constructor;return s===(typeof i=="function"&&i.prototype||bn)},xo=function(s,i){return function(u){return s(i(u))}}(Object.keys,Object);var So=Object.prototype.hasOwnProperty;const Pn=function(s){if(!cn(s))return xo(s);var i=[];for(var u in Object(s))So.call(s,u)&&u!="constructor"&&i.push(u);return i},Ar=function(s){return s!=null&&jr(s.length)&&!ze(s)},On=function(s){return Ar(s)?bl(s):Pn(s)},Pl=function(s,i){return s&&Or(s,i,On)},_o=function(){this.__data__=[],this.size=0},In=function(s,i){return s===i||s!=s&&i!=i},Dt=function(s,i){for(var u=s.length;u--;)if(In(s[u][0],i))return u;return-1};var Ln=Array.prototype.splice;const Ol=function(s){var i=this.__data__,u=Dt(i,s);return!(u<0)&&(u==i.length-1?i.pop():Ln.call(i,u,1),--this.size,!0)},Il=function(s){var i=this.__data__,u=Dt(i,s);return u<0?void 0:i[u][1]},Ll=function(s){return Dt(this.__data__,s)>-1},Eo=function(s,i){var u=this.__data__,y=Dt(u,s);return y<0?(++this.size,u.push([s,i])):u[y][1]=i,this};function dn(s){var i=-1,u=s==null?0:s.length;for(this.clear();++i<u;){var y=s[i];this.set(y[0],y[1])}}dn.prototype.clear=_o,dn.prototype.delete=Ol,dn.prototype.get=Il,dn.prototype.has=Ll,dn.prototype.set=Eo;const jn=dn,To=function(){this.__data__=new jn,this.size=0},Zt=function(s){var i=this.__data__,u=i.delete(s);return this.size=i.size,u},Rr=function(s){return this.__data__.get(s)},jl=function(s){return this.__data__.has(s)},or=Rt(Mt,"Map"),lr=Rt(Object,"create"),Mn=function(){this.__data__=lr?lr(null):{},this.size=0},Cs=function(s){var i=this.has(s)&&delete this.__data__[s];return this.size-=i?1:0,i};var Ml=Object.prototype.hasOwnProperty;const No=function(s){var i=this.__data__;if(lr){var u=i[s];return u==="__lodash_hash_undefined__"?void 0:u}return Ml.call(i,s)?i[s]:void 0};var Al=Object.prototype.hasOwnProperty;const Rl=function(s){var i=this.__data__;return lr?i[s]!==void 0:Al.call(i,s)},xs=function(s,i){var u=this.__data__;return this.size+=this.has(s)?0:1,u[s]=lr&&i===void 0?"__lodash_hash_undefined__":i,this};function Ft(s){var i=-1,u=s==null?0:s.length;for(this.clear();++i<u;){var y=s[i];this.set(y[0],y[1])}}Ft.prototype.clear=Mn,Ft.prototype.delete=Cs,Ft.prototype.get=No,Ft.prototype.has=Rl,Ft.prototype.set=xs;const zr=Ft,Ss=function(){this.size=0,this.__data__={hash:new zr,map:new(or||jn),string:new zr}},Ut=function(s){var i=typeof s;return i=="string"||i=="number"||i=="symbol"||i=="boolean"?s!=="__proto__":s===null},ir=function(s,i){var u=s.__data__;return Ut(i)?u[typeof i=="string"?"string":"hash"]:u.map},zl=function(s){var i=ir(this,s).delete(s);return this.size-=i?1:0,i},Dl=function(s){return ir(this,s).get(s)},_s=function(s){return ir(this,s).has(s)},sr=function(s,i){var u=ir(this,s),y=u.size;return u.set(s,i),this.size+=u.size==y?0:1,this};function tt(s){var i=-1,u=s==null?0:s.length;for(this.clear();++i<u;){var y=s[i];this.set(y[0],y[1])}}tt.prototype.clear=Ss,tt.prototype.delete=zl,tt.prototype.get=Dl,tt.prototype.has=_s,tt.prototype.set=sr;const ar=tt,Fl=function(s,i){var u=this.__data__;if(u instanceof jn){var y=u.__data__;if(!or||y.length<199)return y.push([s,i]),this.size=++u.size,this;u=this.__data__=new ar(y)}return u.set(s,i),this.size=u.size,this};function fn(s){var i=this.__data__=new jn(s);this.size=i.size}fn.prototype.clear=To,fn.prototype.delete=Zt,fn.prototype.get=Rr,fn.prototype.has=jl,fn.prototype.set=Fl;const ur=fn,Es=function(s){return this.__data__.set(s,"__lodash_hash_undefined__"),this},C=function(s){return this.__data__.has(s)};function q(s){var i=-1,u=s==null?0:s.length;for(this.__data__=new ar;++i<u;)this.add(s[i])}q.prototype.add=q.prototype.push=Es,q.prototype.has=C;const H=q,ke=function(s,i){for(var u=-1,y=s==null?0:s.length;++u<y;)if(i(s[u],u,s))return!0;return!1},Le=function(s,i){return s.has(i)},Ge=function(s,i,u,y,b,M){var D=1&u,F=s.length,V=i.length;if(F!=V&&!(D&&V>F))return!1;var Z=M.get(s),re=M.get(i);if(Z&&re)return Z==i&&re==s;var X=-1,ie=!0,me=2&u?new H:void 0;for(M.set(s,i),M.set(i,s);++X<F;){var de=s[X],Ce=i[X];if(y)var Te=D?y(Ce,de,X,i,s,M):y(de,Ce,X,s,i,M);if(Te!==void 0){if(Te)continue;ie=!1;break}if(me){if(!ke(i,function(He,Nt){if(!Le(me,Nt)&&(de===He||b(de,He,u,y,M)))return me.push(Nt)})){ie=!1;break}}else if(de!==Ce&&!b(de,Ce,u,y,M)){ie=!1;break}}return M.delete(s),M.delete(i),ie},Be=Mt.Uint8Array,cr=function(s){var i=-1,u=Array(s.size);return s.forEach(function(y,b){u[++i]=[b,y]}),u},Jt=function(s){var i=-1,u=Array(s.size);return s.forEach(function(y){u[++i]=y}),u};var pt=Gt?Gt.prototype:void 0,bo=pt?pt.valueOf:void 0;const Yu=function(s,i,u,y,b,M,D){switch(u){case"[object DataView]":if(s.byteLength!=i.byteLength||s.byteOffset!=i.byteOffset)return!1;s=s.buffer,i=i.buffer;case"[object ArrayBuffer]":return!(s.byteLength!=i.byteLength||!M(new Be(s),new Be(i)));case"[object Boolean]":case"[object Date]":case"[object Number]":return In(+s,+i);case"[object Error]":return s.name==i.name&&s.message==i.message;case"[object RegExp]":case"[object String]":return s==i+"";case"[object Map]":var F=cr;case"[object Set]":var V=1&y;if(F||(F=Jt),s.size!=i.size&&!V)return!1;var Z=D.get(s);if(Z)return Z==i;y|=2,D.set(s,i);var re=Ge(F(s),F(i),y,b,M,D);return D.delete(s),re;case"[object Symbol]":if(bo)return bo.call(s)==bo.call(i)}return!1},Gp=function(s,i){for(var u=-1,y=i.length,b=s.length;++u<y;)s[b+u]=i[u];return s},Yp=function(s,i,u){var y=i(s);return ft(s)?y:Gp(y,u(s))},Xp=function(s,i){for(var u=-1,y=s==null?0:s.length,b=0,M=[];++u<y;){var D=s[u];i(D,u,s)&&(M[b++]=D)}return M},Zp=function(){return[]};var Jp=Object.prototype.propertyIsEnumerable,Xu=Object.getOwnPropertySymbols;const eh=Xu?function(s){return s==null?[]:(s=Object(s),Xp(Xu(s),function(i){return Jp.call(s,i)}))}:Zp,Zu=function(s){return Yp(s,On,eh)};var th=Object.prototype.hasOwnProperty;const nh=function(s,i,u,y,b,M){var D=1&u,F=Zu(s),V=F.length;if(V!=Zu(i).length&&!D)return!1;for(var Z=V;Z--;){var re=F[Z];if(!(D?re in i:th.call(i,re)))return!1}var X=M.get(s),ie=M.get(i);if(X&&ie)return X==i&&ie==s;var me=!0;M.set(s,i),M.set(i,s);for(var de=D;++Z<V;){var Ce=s[re=F[Z]],Te=i[re];if(y)var He=D?y(Te,Ce,re,i,s,M):y(Ce,Te,re,s,i,M);if(!(He===void 0?Ce===Te||b(Ce,Te,u,y,M):He)){me=!1;break}de||(de=re=="constructor")}if(me&&!de){var Nt=s.constructor,fr=i.constructor;Nt==fr||!("constructor"in s)||!("constructor"in i)||typeof Nt=="function"&&Nt instanceof Nt&&typeof fr=="function"&&fr instanceof fr||(me=!1)}return M.delete(s),M.delete(i),me},Ts=Rt(Mt,"DataView"),Ns=Rt(Mt,"Promise"),bs=Rt(Mt,"Set"),Ps=Rt(Mt,"WeakMap");var Ju="[object Map]",ec="[object Promise]",tc="[object Set]",nc="[object WeakMap]",rc="[object DataView]",rh=un(Ts),oh=un(or),lh=un(Ns),ih=un(bs),sh=un(Ps),dr=le;(Ts&&dr(new Ts(new ArrayBuffer(1)))!=rc||or&&dr(new or)!=Ju||Ns&&dr(Ns.resolve())!=ec||bs&&dr(new bs)!=tc||Ps&&dr(new Ps)!=nc)&&(dr=function(s){var i=le(s),u=i=="[object Object]"?s.constructor:void 0,y=u?un(u):"";if(y)switch(y){case rh:return rc;case oh:return Ju;case lh:return ec;case ih:return tc;case sh:return nc}return i});const oc=dr;var lc="[object Arguments]",ic="[object Array]",Ul="[object Object]",sc=Object.prototype.hasOwnProperty;const ah=function(s,i,u,y,b,M){var D=ft(s),F=ft(i),V=D?ic:oc(s),Z=F?ic:oc(i),re=(V=V==lc?Ul:V)==Ul,X=(Z=Z==lc?Ul:Z)==Ul,ie=V==Z;if(ie&&wo(s)){if(!wo(i))return!1;D=!0,re=!1}if(ie&&!re)return M||(M=new ur),D||ko(s)?Ge(s,i,u,y,b,M):Yu(s,i,V,u,y,b,M);if(!(1&u)){var me=re&&sc.call(s,"__wrapped__"),de=X&&sc.call(i,"__wrapped__");if(me||de){var Ce=me?s.value():s,Te=de?i.value():i;return M||(M=new ur),b(Ce,Te,u,y,M)}}return!!ie&&(M||(M=new ur),nh(s,i,u,y,b,M))},ac=function s(i,u,y,b,M){return i===u||(i==null||u==null||!En(i)&&!En(u)?i!=i&&u!=u:ah(i,u,y,b,s,M))},uh=function(s,i,u,y){var b=u.length,M=b,D=!y;if(s==null)return!M;for(s=Object(s);b--;){var F=u[b];if(D&&F[2]?F[1]!==s[F[0]]:!(F[0]in s))return!1}for(;++b<M;){var V=(F=u[b])[0],Z=s[V],re=F[1];if(D&&F[2]){if(Z===void 0&&!(V in s))return!1}else{var X=new ur;if(y)var ie=y(Z,re,V,s,i,X);if(!(ie===void 0?ac(re,Z,3,y,X):ie))return!1}}return!0},uc=function(s){return s==s&&!fe(s)},ch=function(s){for(var i=On(s),u=i.length;u--;){var y=i[u],b=s[y];i[u]=[y,b,uc(b)]}return i},cc=function(s,i){return function(u){return u!=null&&u[s]===i&&(i!==void 0||s in Object(u))}},dh=function(s){var i=ch(s);return i.length==1&&i[0][2]?cc(i[0][0],i[0][1]):function(u){return u===s||uh(u,s,i)}},Os=function(s){return typeof s=="symbol"||En(s)&&le(s)=="[object Symbol]"};var fh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ph=/^\w*$/;const Is=function(s,i){if(ft(s))return!1;var u=typeof s;return!(u!="number"&&u!="symbol"&&u!="boolean"&&s!=null&&!Os(s))||ph.test(s)||!fh.test(s)||i!=null&&s in Object(i)};function Ls(s,i){if(typeof s!="function"||i!=null&&typeof i!="function")throw new TypeError("Expected a function");var u=function(){var y=arguments,b=i?i.apply(this,y):y[0],M=u.cache;if(M.has(b))return M.get(b);var D=s.apply(this,y);return u.cache=M.set(b,D)||M,D};return u.cache=new(Ls.Cache||ar),u}Ls.Cache=ar;const hh=Ls;var mh=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,gh=/\\(\\)?/g;const yh=function(s){var i=hh(s,function(y){return u.size===500&&u.clear(),y}),u=i.cache;return i}(function(s){var i=[];return s.charCodeAt(0)===46&&i.push(""),s.replace(mh,function(u,y,b,M){i.push(b?M.replace(gh,"$1"):y||u)}),i}),vh=function(s,i){for(var u=-1,y=s==null?0:s.length,b=Array(y);++u<y;)b[u]=i(s[u],u,s);return b};var dc=Gt?Gt.prototype:void 0,fc=dc?dc.toString:void 0;const wh=function s(i){if(typeof i=="string")return i;if(ft(i))return vh(i,s)+"";if(Os(i))return fc?fc.call(i):"";var u=i+"";return u=="0"&&1/i==-1/0?"-0":u},kh=function(s){return s==null?"":wh(s)},pc=function(s,i){return ft(s)?s:Is(s,i)?[s]:yh(kh(s))},$l=function(s){if(typeof s=="string"||Os(s))return s;var i=s+"";return i=="0"&&1/s==-1/0?"-0":i},hc=function(s,i){for(var u=0,y=(i=pc(i,s)).length;s!=null&&u<y;)s=s[$l(i[u++])];return u&&u==y?s:void 0},Ch=function(s,i,u){var y=s==null?void 0:hc(s,i);return y===void 0?u:y},xh=function(s,i){return s!=null&&i in Object(s)},Sh=function(s,i,u){for(var y=-1,b=(i=pc(i,s)).length,M=!1;++y<b;){var D=$l(i[y]);if(!(M=s!=null&&u(s,D)))break;s=s[D]}return M||++y!=b?M:!!(b=s==null?0:s.length)&&jr(b)&&Tl(D,b)&&(ft(s)||kt(s))},_h=function(s,i){return s!=null&&Sh(s,i,xh)},Eh=function(s,i){return Is(s)&&uc(i)?cc($l(s),i):function(u){var y=Ch(u,s);return y===void 0&&y===i?_h(u,s):ac(i,y,3)}},Th=function(s){return s},Nh=function(s){return function(i){return i?.[s]}},bh=function(s){return function(i){return hc(i,s)}},Ph=function(s){return Is(s)?Nh($l(s)):bh(s)},Oh=function(s){return typeof s=="function"?s:s==null?Th:typeof s=="object"?ft(s)?Eh(s[0],s[1]):dh(s):Ph(s)},Ih=function(s,i){var u={};return i=Oh(i),Pl(s,function(y,b,M){xl(u,i(y,b,M),y)}),u};function mc(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}class Lh{constructor(i,u){mc(this,"ctx",void 0),mc(this,"serviceHooks",void 0),this.ctx=i,this.serviceHooks=u,i._execCallableAPI("register-search-service",i.baseInfo.id,u.name,u.options),Object.entries({query:{f:"onQuery",args:["graph","q",!0],reply:!0,transformOutput:y=>(ft(y?.blocks)&&(y.blocks=y.blocks.map(b=>b&&Ih(b,(M,D)=>`block/${D}`))),y)},rebuildBlocksIndice:{f:"onIndiceInit",args:["graph","blocks"]},transactBlocks:{f:"onBlocksChanged",args:["graph","data"]},truncateBlocks:{f:"onIndiceReset",args:["graph"]},removeDb:{f:"onGraph",args:["graph"]}}).forEach(([y,b])=>{const M=(D=>`service:search:${D}:${u.name}`)(y);i.caller.on(M,async D=>{if(ze(u?.[b.f])){let F=null;try{F=await u[b.f].apply(u,(b.args||[]).map(V=>{if(D){if(V===!0)return D;if(D.hasOwnProperty(V)){const Z=D[V];return delete D[V],Z}}})),b.transformOutput&&(F=b.transformOutput(F))}catch(V){console.error("[SearchService] ",V),F=V}finally{b.reply&&i.caller.call(`${M}:reply`,F)}}})})}}function $t(s,i,u){return i in s?Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[i]=u,s}const jh=Symbol.for("proxy-continue"),Mh=R()("LSPlugin:user"),gc=new P("",{console:!0});function Dr(s,i,u){var y;const{key:b,label:M,desc:D,palette:F,keybinding:V,extras:Z}=i;if(typeof u!="function")return this.logger.error(`${b||M}: command action should be function.`),!1;const re=function(ie){if(typeof ie=="string")return ie.trim().replace(/\s/g,"_").toLowerCase()}(b);if(!re)return this.logger.error(`${M}: command key is required.`),!1;const X=`SimpleCommandHook${re}${++wc}`;this.Editor["on"+X](u),(y=this.caller)===null||y===void 0||y.call("api:call",{method:"register-plugin-simple-command",args:[this.baseInfo.id,[{key:re,label:M,type:s,desc:D,keybinding:V,extras:Z},["editor/hook",X]],F]})}function yc(s){return!(typeof(i=s)!="string"||i.length!==36||!/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi.test(i))||(gc.error(`#${s} is not a valid UUID string.`),!1);var i}let Bl=null,vc=new Map;const Ah={async getInfo(s){return Bl||(Bl=await this._execCallableAPIAsync("get-app-info")),typeof s=="string"?Bl[s]:Bl},registerCommand:Dr,registerSearchService(s){if(vc.has(s.name))throw new Error(`SearchService: #${s.name} has registered!`);vc.set(s.name,new Lh(this,s))},registerCommandPalette(s,i){const{key:u,label:y,keybinding:b}=s;return Dr.call(this,"$palette$",{key:u,label:y,palette:!0,keybinding:b},i)},registerCommandShortcut(s,i,u={}){typeof s=="string"&&(s={mode:"global",binding:s});const{binding:y}=s,b="$shortcut$",M=u.key||b+f(y?.toString());return Dr.call(this,b,{...u,key:M,palette:!1,keybinding:s},i)},registerUIItem(s,i){var u;const y=this.baseInfo.id;(u=this.caller)===null||u===void 0||u.call("api:call",{method:"register-plugin-ui-item",args:[y,s,i]})},registerPageMenuItem(s,i){if(typeof i!="function")return!1;const u=s+"_"+this.baseInfo.id,y=s;Dr.call(this,"page-menu-item",{key:u,label:y},i)},onBlockRendererSlotted(s,i){if(!yc(s))return;const u=this.baseInfo.id,y=`hook:editor:${f(`slot:${s}`)}`;return this.caller.on(y,i),this.App._installPluginHook(u,y),()=>{this.caller.off(y,i),this.App._uninstallPluginHook(u,y)}},invokeExternalPlugin(s,...i){var u;if(!(s=(u=s)===null||u===void 0?void 0:u.trim()))return;let[y,b]=s.split(".");if(!["models","commands"].includes(b?.toLowerCase()))throw new Error("Type only support '.models' or '.commands' currently.");const M=s.replace(`${y}.${b}.`,"");if(!y||!b||!M)throw new Error(`Illegal type of #${s} to invoke external plugin.`);return this._execCallableAPIAsync("invoke_external_plugin_cmd",y,b.toLowerCase(),M,i)},setFullScreen(s){const i=(...u)=>this._callWin("setFullScreen",...u);s==="toggle"?this._callWin("isFullScreen").then(u=>{u?i():i(!0)}):s?i(!0):i()}};let wc=0;const Rh={newBlockUUID(){return this._execCallableAPIAsync("new_block_uuid")},registerSlashCommand(s,i){var u;Mh("Register slash command #",this.baseInfo.id,s,i),typeof i=="function"&&(i=[["editor/clear-current-slash",!1],["editor/restore-saved-cursor"],["editor/hook",i]]),i=i.map(y=>{const[b,...M]=y;if(b==="editor/hook"){let D=M[0],F=()=>{var Z;(Z=this.caller)===null||Z===void 0||Z.callUserModel(D)};typeof D=="function"&&(F=D);const V=`SlashCommandHook${b}${++wc}`;y[1]=V,this.Editor["on"+V](F)}return y}),(u=this.caller)===null||u===void 0||u.call("api:call",{method:"register-plugin-slash-command",args:[this.baseInfo.id,[s,i]]})},registerBlockContextMenuItem(s,i){if(typeof i!="function")return!1;const u=s+"_"+this.baseInfo.id;Dr.call(this,"block-context-menu-item",{key:u,label:s},i)},registerHighlightContextMenuItem(s,i,u){if(typeof i!="function")return!1;const y=s+"_"+this.baseInfo.id;Dr.call(this,"highlight-context-menu-item",{key:y,label:s,extras:u},i)},scrollToBlockInPage(s,i,u){const y="block-content-"+i;u!=null&&u.replaceState?this.App.replaceState("page",{name:s},{anchor:y}):this.App.pushState("page",{name:s},{anchor:y})}},zh={onBlockChanged(s,i){if(!yc(s))return;const u=this.baseInfo.id,y=`hook:db:${f(`block:${s}`)}`,b=({block:M,txData:D,txMeta:F})=>{M.uuid===s&&i(M,D,F)};return this.caller.on(y,b),this.App._installPluginHook(u,y),()=>{this.caller.off(y,b),this.App._uninstallPluginHook(u,y)}},datascriptQuery(s,...i){return i.pop(),i!=null&&i.some(u=>typeof u=="function")?this.Experiments.ensureHostScope().logseq.api.datascript_query(s,...i):this._execCallableAPIAsync("datascript_query",s,...i)}},Dh={},Fh={},Uh={makeSandboxStorage(){return new ve(this,{assets:!0})}};class js extends I(){constructor(i,u){super(),$t(this,"_baseInfo",void 0),$t(this,"_caller",void 0),$t(this,"_version","0.0.17"),$t(this,"_debugTag",""),$t(this,"_settingsSchema",void 0),$t(this,"_connected",!1),$t(this,"_ui",new Map),$t(this,"_mFileStorage",void 0),$t(this,"_mRequest",void 0),$t(this,"_mExperiments",void 0),$t(this,"_beforeunloadCallback",void 0),this._baseInfo=i,this._caller=u,u.on("sys:ui:visible",y=>{y!=null&&y.toggle&&this.toggleMainUI()}),u.on("settings:changed",y=>{const b=Object.assign({},this.settings),M=Object.assign(this._baseInfo.settings,y);this.emit("settings:changed",{...M},b)}),u.on("beforeunload",async y=>{const{actor:b,...M}=y,D=this._beforeunloadCallback;try{D&&await D(M),b?.resolve(null)}catch(F){this.logger.error("[beforeunload] ",F),b?.reject(F)}})}async ready(i,u){var y,b;if(!this._connected)try{var M;typeof i=="function"&&(u=i,i={});let D=await this._caller.connectToParent(i);this._connected=!0,y=this._baseInfo,b=D,D=d()(y,b,{arrayMerge:(F,V)=>V}),this._baseInfo=D,(M=D)!==null&&M!==void 0&&M.id&&(this._debugTag=this._caller.debugTag=`#${D.id} [${D.name}]`,this.logger.setTag(this._debugTag)),this._settingsSchema&&(D.settings=function(F,V){const Z=(V||[]).reduce((re,X)=>("default"in X&&(re[X.key]=X.default),re),{});return Object.assign(Z,F)}(D.settings,this._settingsSchema),await this.useSettingsSchema(this._settingsSchema));try{await this._execCallableAPIAsync("setSDKMetadata",{version:this._version})}catch(F){console.warn(F)}u&&u.call(this,D)}catch(D){console.error(`${this._debugTag} [Ready Error]`,D)}}ensureConnected(){if(!this._connected)throw new Error("not connected")}beforeunload(i){typeof i=="function"&&(this._beforeunloadCallback=i)}provideModel(i){return this.caller._extendUserModel(i),this}provideTheme(i){return this.caller.call("provider:theme",i),this}provideStyle(i){return this.caller.call("provider:style",i),this}provideUI(i){return this.caller.call("provider:ui",i),this}useSettingsSchema(i){return this.connected&&this.caller.call("settings:schema",{schema:i,isSync:!0}),this._settingsSchema=i,this}updateSettings(i){this.caller.call("settings:update",i)}onSettingsChanged(i){const u="settings:changed";return this.on(u,i),()=>this.off(u,i)}showSettingsUI(){this.caller.call("settings:visible:changed",{visible:!0})}hideSettingsUI(){this.caller.call("settings:visible:changed",{visible:!1})}setMainUIAttrs(i){this.caller.call("main-ui:attrs",i)}setMainUIInlineStyle(i){this.caller.call("main-ui:style",i)}hideMainUI(i){const u={key:0,visible:!1,cursor:i?.restoreEditingCursor};this.caller.call("main-ui:visible",u),this.emit("ui:visible:changed",u),this._ui.set(u.key,u)}showMainUI(i){const u={key:0,visible:!0,autoFocus:i?.autoFocus};this.caller.call("main-ui:visible",u),this.emit("ui:visible:changed",u),this._ui.set(u.key,u)}toggleMainUI(){const u=this._ui.get(0);u&&u.visible?this.hideMainUI():this.showMainUI()}get version(){return this._version}get isMainUIVisible(){const i=this._ui.get(0);return!!(i&&i.visible)}get connected(){return this._connected}get baseInfo(){return this._baseInfo}get effect(){return(i=this)&&(((u=i.baseInfo)===null||u===void 0?void 0:u.effect)||!((y=i.baseInfo)!==null&&y!==void 0&&y.iir));var i,u,y}get logger(){return gc}get settings(){var i;return(i=this.baseInfo)===null||i===void 0?void 0:i.settings}get caller(){return this._caller}resolveResourceFullUrl(i){if(this.ensureConnected(),i)return i=i.replace(/^[.\\/]+/,""),N(this._baseInfo.lsr,i)}_makeUserProxy(i,u){const y=this,b=this.caller;return new Proxy(i,{get(M,D,F){const V=M[D];return function(...Z){if(V){const X=V.apply(y,Z.concat(u));if(X!==jh)return X}if(u){const X=D.toString().match(/^(once|off|on)/i);if(X!=null){const ie=X[0].toLowerCase(),me=X.input,de=ie==="off",Ce=y.baseInfo.id;let Te=me.slice(ie.length),He=Z[0],Nt=Z[1];typeof He=="string"&&typeof Nt=="function"&&(He=He.replace(/^logseq./,":"),Te=`${Te}${He}`,He=Nt,Nt=Z[2]),Te=`hook:${u}:${f(Te)}`,b[ie](Te,He);const fr=()=>{b.off(Te,He),b.listenerCount(Te)||y.App._uninstallPluginHook(Ce,Te)};return de?void fr():(y.App._installPluginHook(Ce,Te,Nt),fr)}}let re=D;return["git","ui","assets"].includes(u)&&(re=u+"_"+re),b.callAsync("api:call",{tag:u,method:re,args:Z})}}})}_execCallableAPIAsync(i,...u){return this._caller.callAsync("api:call",{method:i,args:u})}_execCallableAPI(i,...u){this._caller.call("api:call",{method:i,args:u})}_callWin(...i){return this._execCallableAPIAsync("_callMainWin",...i)}get App(){return this._makeUserProxy(Ah,"app")}get Editor(){return this._makeUserProxy(Rh,"editor")}get DB(){return this._makeUserProxy(zh,"db")}get Git(){return this._makeUserProxy(Dh,"git")}get UI(){return this._makeUserProxy(Fh,"ui")}get Assets(){return this._makeUserProxy(Uh,"assets")}get FileStorage(){let i=this._mFileStorage;return i||(i=this._mFileStorage=new ve(this)),i}get Request(){let i=this._mRequest;return i||(i=this._mRequest=new wt(this)),i}get Experiments(){let i=this._mExperiments;return i||(i=this._mExperiments=new _e(this)),i}}function kc(s,i){return new js(s,i)}if(window.__LSP__HOST__==null){const s=new K(null);window.logseq=kc({},s)}})(),l})())})(ki,ki.exports);ki.exports;var Hd={exports:{}},Et={},Wd={exports:{}},Vd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(z,E){var L=z.length;z.push(E);e:for(;0<L;){var $=L-1>>>1,te=z[$];if(0<o(te,E))z[$]=E,z[L]=te,L=$;else break e}}function n(z){return z.length===0?null:z[0]}function r(z){if(z.length===0)return null;var E=z[0],L=z.pop();if(L!==E){z[0]=L;e:for(var $=0,te=z.length,ye=te>>>1;$<ye;){var B=2*($+1)-1,ne=z[B],ae=B+1,Re=z[ae];if(0>o(ne,L))ae<te&&0>o(Re,ne)?(z[$]=Re,z[ae]=L,$=ae):(z[$]=ne,z[B]=L,$=B);else if(ae<te&&0>o(Re,L))z[$]=Re,z[ae]=L,$=ae;else break e}}return E}function o(z,E){var L=z.sortIndex-E.sortIndex;return L!==0?L:z.id-E.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var a=Date,c=a.now();e.unstable_now=function(){return a.now()-c}}var d=[],h=[],v=1,S=null,T=3,k=!1,x=!1,_=!1,I=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(z){for(var E=n(h);E!==null;){if(E.callback===null)r(h);else if(E.startTime<=z)r(h),E.sortIndex=E.expirationTime,t(d,E);else break;E=n(h)}}function P(z){if(_=!1,f(z),!x)if(n(d)!==null)x=!0,Ee(N);else{var E=n(h);E!==null&&Se(P,E.startTime-z)}}function N(z,E){x=!1,_&&(_=!1,g(w),w=-1),k=!0;var L=T;try{for(f(E),S=n(d);S!==null&&(!(S.expirationTime>E)||z&&!J());){var $=S.callback;if(typeof $=="function"){S.callback=null,T=S.priorityLevel;var te=$(S.expirationTime<=E);E=e.unstable_now(),typeof te=="function"?S.callback=te:S===n(d)&&r(d),f(E)}else r(d);S=n(d)}if(S!==null)var ye=!0;else{var B=n(h);B!==null&&Se(P,B.startTime-E),ye=!1}return ye}finally{S=null,T=L,k=!1}}var O=!1,m=null,w=-1,R=5,j=-1;function J(){return!(e.unstable_now()-j<R)}function ce(){if(m!==null){var z=e.unstable_now();j=z;var E=!0;try{E=m(!0,z)}finally{E?ue():(O=!1,m=null)}}else O=!1}var ue;if(typeof p=="function")ue=function(){p(ce)};else if(typeof MessageChannel<"u"){var he=new MessageChannel,oe=he.port2;he.port1.onmessage=ce,ue=function(){oe.postMessage(null)}}else ue=function(){I(ce,0)};function Ee(z){m=z,O||(O=!0,ue())}function Se(z,E){w=I(function(){z(e.unstable_now())},E)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(z){z.callback=null},e.unstable_continueExecution=function(){x||k||(x=!0,Ee(N))},e.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<z?Math.floor(1e3/z):5},e.unstable_getCurrentPriorityLevel=function(){return T},e.unstable_getFirstCallbackNode=function(){return n(d)},e.unstable_next=function(z){switch(T){case 1:case 2:case 3:var E=3;break;default:E=T}var L=T;T=E;try{return z()}finally{T=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(z,E){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var L=T;T=z;try{return E()}finally{T=L}},e.unstable_scheduleCallback=function(z,E,L){var $=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?$+L:$):L=$,z){case 1:var te=-1;break;case 2:te=250;break;case 5:te=**********;break;case 4:te=1e4;break;default:te=5e3}return te=L+te,z={id:v++,callback:E,priorityLevel:z,startTime:L,expirationTime:te,sortIndex:-1},L>$?(z.sortIndex=L,t(h,z),n(d)===null&&z===n(h)&&(_?(g(w),w=-1):_=!0,Se(P,L-$))):(z.sortIndex=te,t(d,z),x||k||(x=!0,Ee(N))),z},e.unstable_shouldYield=J,e.unstable_wrapCallback=function(z){var E=T;return function(){var L=T;T=E;try{return z.apply(this,arguments)}finally{T=L}}}})(Vd);Wd.exports=Vd;var cm=Wd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dm=se,_t=cm;function U(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var qd=new Set,Xo={};function Tr(e,t){lo(e,t),lo(e+"Capture",t)}function lo(e,t){for(Xo[e]=t,e=0;e<t.length;e++)qd.add(t[e])}var wn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),aa=Object.prototype.hasOwnProperty,fm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,_c={},Ec={};function pm(e){return aa.call(Ec,e)?!0:aa.call(_c,e)?!1:fm.test(e)?Ec[e]=!0:(_c[e]=!0,!1)}function hm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function mm(e,t,n,r){if(t===null||typeof t>"u"||hm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ct(e,t,n,r,o,l,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=a}var et={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){et[e]=new ct(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];et[t]=new ct(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){et[e]=new ct(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){et[e]=new ct(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){et[e]=new ct(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){et[e]=new ct(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){et[e]=new ct(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){et[e]=new ct(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){et[e]=new ct(e,5,!1,e.toLowerCase(),null,!1,!1)});var iu=/[\-:]([a-z])/g;function su(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(iu,su);et[t]=new ct(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(iu,su);et[t]=new ct(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(iu,su);et[t]=new ct(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){et[e]=new ct(e,1,!1,e.toLowerCase(),null,!1,!1)});et.xlinkHref=new ct("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){et[e]=new ct(e,1,!1,e.toLowerCase(),null,!0,!0)});function au(e,t,n,r){var o=et.hasOwnProperty(t)?et[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(mm(t,n,o,r)&&(n=null),r||o===null?pm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Sn=dm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Vl=Symbol.for("react.element"),Ur=Symbol.for("react.portal"),$r=Symbol.for("react.fragment"),uu=Symbol.for("react.strict_mode"),ua=Symbol.for("react.profiler"),Qd=Symbol.for("react.provider"),Kd=Symbol.for("react.context"),cu=Symbol.for("react.forward_ref"),ca=Symbol.for("react.suspense"),da=Symbol.for("react.suspense_list"),du=Symbol.for("react.memo"),Rn=Symbol.for("react.lazy"),Gd=Symbol.for("react.offscreen"),Tc=Symbol.iterator;function Po(e){return e===null||typeof e!="object"?null:(e=Tc&&e[Tc]||e["@@iterator"],typeof e=="function"?e:null)}var Ae=Object.assign,As;function Do(e){if(As===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);As=t&&t[1]||""}return`
`+As+e}var Rs=!1;function zs(e,t){if(!e||Rs)return"";Rs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(h){var r=h}Reflect.construct(e,[],t)}else{try{t.call()}catch(h){r=h}e.call(t.prototype)}else{try{throw Error()}catch(h){r=h}e()}}catch(h){if(h&&r&&typeof h.stack=="string"){for(var o=h.stack.split(`
`),l=r.stack.split(`
`),a=o.length-1,c=l.length-1;1<=a&&0<=c&&o[a]!==l[c];)c--;for(;1<=a&&0<=c;a--,c--)if(o[a]!==l[c]){if(a!==1||c!==1)do if(a--,c--,0>c||o[a]!==l[c]){var d=`
`+o[a].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),d}while(1<=a&&0<=c);break}}}finally{Rs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Do(e):""}function gm(e){switch(e.tag){case 5:return Do(e.type);case 16:return Do("Lazy");case 13:return Do("Suspense");case 19:return Do("SuspenseList");case 0:case 2:case 15:return e=zs(e.type,!1),e;case 11:return e=zs(e.type.render,!1),e;case 1:return e=zs(e.type,!0),e;default:return""}}function fa(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case $r:return"Fragment";case Ur:return"Portal";case ua:return"Profiler";case uu:return"StrictMode";case ca:return"Suspense";case da:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Kd:return(e.displayName||"Context")+".Consumer";case Qd:return(e._context.displayName||"Context")+".Provider";case cu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case du:return t=e.displayName||null,t!==null?t:fa(e.type)||"Memo";case Rn:t=e._payload,e=e._init;try{return fa(e(t))}catch{}}return null}function ym(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fa(t);case 8:return t===uu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Yn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Yd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vm(e){var t=Yd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,l.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ql(e){e._valueTracker||(e._valueTracker=vm(e))}function Xd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Yd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ci(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function pa(e,t){var n=t.checked;return Ae({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Nc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Yn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Zd(e,t){t=t.checked,t!=null&&au(e,"checked",t,!1)}function ha(e,t){Zd(e,t);var n=Yn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ma(e,t.type,n):t.hasOwnProperty("defaultValue")&&ma(e,t.type,Yn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function bc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ma(e,t,n){(t!=="number"||Ci(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Fo=Array.isArray;function Jr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Yn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ga(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(U(91));return Ae({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(U(92));if(Fo(n)){if(1<n.length)throw Error(U(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Yn(n)}}function Jd(e,t){var n=Yn(t.value),r=Yn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Oc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ef(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ya(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ef(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ql,tf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ql=Ql||document.createElement("div"),Ql.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ql.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Zo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Bo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},wm=["Webkit","ms","Moz","O"];Object.keys(Bo).forEach(function(e){wm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Bo[t]=Bo[e]})});function nf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Bo.hasOwnProperty(e)&&Bo[e]?(""+t).trim():t+"px"}function rf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=nf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var km=Ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function va(e,t){if(t){if(km[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(U(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(U(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(U(61))}if(t.style!=null&&typeof t.style!="object")throw Error(U(62))}}function wa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ka=null;function fu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ca=null,eo=null,to=null;function Ic(e){if(e=yl(e)){if(typeof Ca!="function")throw Error(U(280));var t=e.stateNode;t&&(t=Xi(t),Ca(e.stateNode,e.type,t))}}function of(e){eo?to?to.push(e):to=[e]:eo=e}function lf(){if(eo){var e=eo,t=to;if(to=eo=null,Ic(e),t)for(e=0;e<t.length;e++)Ic(t[e])}}function sf(e,t){return e(t)}function af(){}var Ds=!1;function uf(e,t,n){if(Ds)return e(t,n);Ds=!0;try{return sf(e,t,n)}finally{Ds=!1,(eo!==null||to!==null)&&(af(),lf())}}function Jo(e,t){var n=e.stateNode;if(n===null)return null;var r=Xi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(U(231,t,typeof n));return n}var xa=!1;if(wn)try{var Oo={};Object.defineProperty(Oo,"passive",{get:function(){xa=!0}}),window.addEventListener("test",Oo,Oo),window.removeEventListener("test",Oo,Oo)}catch{xa=!1}function Cm(e,t,n,r,o,l,a,c,d){var h=Array.prototype.slice.call(arguments,3);try{t.apply(n,h)}catch(v){this.onError(v)}}var Ho=!1,xi=null,Si=!1,Sa=null,xm={onError:function(e){Ho=!0,xi=e}};function Sm(e,t,n,r,o,l,a,c,d){Ho=!1,xi=null,Cm.apply(xm,arguments)}function _m(e,t,n,r,o,l,a,c,d){if(Sm.apply(this,arguments),Ho){if(Ho){var h=xi;Ho=!1,xi=null}else throw Error(U(198));Si||(Si=!0,Sa=h)}}function Nr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function cf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Lc(e){if(Nr(e)!==e)throw Error(U(188))}function Em(e){var t=e.alternate;if(!t){if(t=Nr(e),t===null)throw Error(U(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var l=o.alternate;if(l===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===l.child){for(l=o.child;l;){if(l===n)return Lc(o),e;if(l===r)return Lc(o),t;l=l.sibling}throw Error(U(188))}if(n.return!==r.return)n=o,r=l;else{for(var a=!1,c=o.child;c;){if(c===n){a=!0,n=o,r=l;break}if(c===r){a=!0,r=o,n=l;break}c=c.sibling}if(!a){for(c=l.child;c;){if(c===n){a=!0,n=l,r=o;break}if(c===r){a=!0,r=l,n=o;break}c=c.sibling}if(!a)throw Error(U(189))}}if(n.alternate!==r)throw Error(U(190))}if(n.tag!==3)throw Error(U(188));return n.stateNode.current===n?e:t}function df(e){return e=Em(e),e!==null?ff(e):null}function ff(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ff(e);if(t!==null)return t;e=e.sibling}return null}var pf=_t.unstable_scheduleCallback,jc=_t.unstable_cancelCallback,Tm=_t.unstable_shouldYield,Nm=_t.unstable_requestPaint,Fe=_t.unstable_now,bm=_t.unstable_getCurrentPriorityLevel,pu=_t.unstable_ImmediatePriority,hf=_t.unstable_UserBlockingPriority,_i=_t.unstable_NormalPriority,Pm=_t.unstable_LowPriority,mf=_t.unstable_IdlePriority,Qi=null,ln=null;function Om(e){if(ln&&typeof ln.onCommitFiberRoot=="function")try{ln.onCommitFiberRoot(Qi,e,void 0,(e.current.flags&128)===128)}catch{}}var qt=Math.clz32?Math.clz32:jm,Im=Math.log,Lm=Math.LN2;function jm(e){return e>>>=0,e===0?32:31-(Im(e)/Lm|0)|0}var Kl=64,Gl=4194304;function Uo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ei(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,l=e.pingedLanes,a=n&268435455;if(a!==0){var c=a&~o;c!==0?r=Uo(c):(l&=a,l!==0&&(r=Uo(l)))}else a=n&~o,a!==0?r=Uo(a):l!==0&&(r=Uo(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,l=t&-t,o>=l||o===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qt(t),o=1<<n,r|=e[n],t&=~o;return r}function Mm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Am(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,l=e.pendingLanes;0<l;){var a=31-qt(l),c=1<<a,d=o[a];d===-1?(!(c&n)||c&r)&&(o[a]=Mm(c,t)):d<=t&&(e.expiredLanes|=c),l&=~c}}function _a(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function gf(){var e=Kl;return Kl<<=1,!(Kl&4194240)&&(Kl=64),e}function Fs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ml(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qt(t),e[t]=n}function Rm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-qt(n),l=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~l}}function hu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var xe=0;function yf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var vf,mu,wf,kf,Cf,Ea=!1,Yl=[],Bn=null,Hn=null,Wn=null,el=new Map,tl=new Map,Dn=[],zm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mc(e,t){switch(e){case"focusin":case"focusout":Bn=null;break;case"dragenter":case"dragleave":Hn=null;break;case"mouseover":case"mouseout":Wn=null;break;case"pointerover":case"pointerout":el.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":tl.delete(t.pointerId)}}function Io(e,t,n,r,o,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[o]},t!==null&&(t=yl(t),t!==null&&mu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Dm(e,t,n,r,o){switch(t){case"focusin":return Bn=Io(Bn,e,t,n,r,o),!0;case"dragenter":return Hn=Io(Hn,e,t,n,r,o),!0;case"mouseover":return Wn=Io(Wn,e,t,n,r,o),!0;case"pointerover":var l=o.pointerId;return el.set(l,Io(el.get(l)||null,e,t,n,r,o)),!0;case"gotpointercapture":return l=o.pointerId,tl.set(l,Io(tl.get(l)||null,e,t,n,r,o)),!0}return!1}function xf(e){var t=mr(e.target);if(t!==null){var n=Nr(t);if(n!==null){if(t=n.tag,t===13){if(t=cf(n),t!==null){e.blockedOn=t,Cf(e.priority,function(){wf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ci(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ta(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ka=r,n.target.dispatchEvent(r),ka=null}else return t=yl(n),t!==null&&mu(t),e.blockedOn=n,!1;t.shift()}return!0}function Ac(e,t,n){ci(e)&&n.delete(t)}function Fm(){Ea=!1,Bn!==null&&ci(Bn)&&(Bn=null),Hn!==null&&ci(Hn)&&(Hn=null),Wn!==null&&ci(Wn)&&(Wn=null),el.forEach(Ac),tl.forEach(Ac)}function Lo(e,t){e.blockedOn===t&&(e.blockedOn=null,Ea||(Ea=!0,_t.unstable_scheduleCallback(_t.unstable_NormalPriority,Fm)))}function nl(e){function t(o){return Lo(o,e)}if(0<Yl.length){Lo(Yl[0],e);for(var n=1;n<Yl.length;n++){var r=Yl[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Bn!==null&&Lo(Bn,e),Hn!==null&&Lo(Hn,e),Wn!==null&&Lo(Wn,e),el.forEach(t),tl.forEach(t),n=0;n<Dn.length;n++)r=Dn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Dn.length&&(n=Dn[0],n.blockedOn===null);)xf(n),n.blockedOn===null&&Dn.shift()}var no=Sn.ReactCurrentBatchConfig,Ti=!0;function Um(e,t,n,r){var o=xe,l=no.transition;no.transition=null;try{xe=1,gu(e,t,n,r)}finally{xe=o,no.transition=l}}function $m(e,t,n,r){var o=xe,l=no.transition;no.transition=null;try{xe=4,gu(e,t,n,r)}finally{xe=o,no.transition=l}}function gu(e,t,n,r){if(Ti){var o=Ta(e,t,n,r);if(o===null)Gs(e,t,r,Ni,n),Mc(e,r);else if(Dm(o,e,t,n,r))r.stopPropagation();else if(Mc(e,r),t&4&&-1<zm.indexOf(e)){for(;o!==null;){var l=yl(o);if(l!==null&&vf(l),l=Ta(e,t,n,r),l===null&&Gs(e,t,r,Ni,n),l===o)break;o=l}o!==null&&r.stopPropagation()}else Gs(e,t,r,null,n)}}var Ni=null;function Ta(e,t,n,r){if(Ni=null,e=fu(r),e=mr(e),e!==null)if(t=Nr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=cf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ni=e,null}function Sf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(bm()){case pu:return 1;case hf:return 4;case _i:case Pm:return 16;case mf:return 536870912;default:return 16}default:return 16}}var Un=null,yu=null,di=null;function _f(){if(di)return di;var e,t=yu,n=t.length,r,o="value"in Un?Un.value:Un.textContent,l=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[l-r];r++);return di=o.slice(e,1<r?1-r:void 0)}function fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Xl(){return!0}function Rc(){return!1}function Tt(e){function t(n,r,o,l,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=l,this.target=a,this.currentTarget=null;for(var c in e)e.hasOwnProperty(c)&&(n=e[c],this[c]=n?n(l):l[c]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Xl:Rc,this.isPropagationStopped=Rc,this}return Ae(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Xl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Xl)},persist:function(){},isPersistent:Xl}),t}var ho={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vu=Tt(ho),gl=Ae({},ho,{view:0,detail:0}),Bm=Tt(gl),Us,$s,jo,Ki=Ae({},gl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==jo&&(jo&&e.type==="mousemove"?(Us=e.screenX-jo.screenX,$s=e.screenY-jo.screenY):$s=Us=0,jo=e),Us)},movementY:function(e){return"movementY"in e?e.movementY:$s}}),zc=Tt(Ki),Hm=Ae({},Ki,{dataTransfer:0}),Wm=Tt(Hm),Vm=Ae({},gl,{relatedTarget:0}),Bs=Tt(Vm),qm=Ae({},ho,{animationName:0,elapsedTime:0,pseudoElement:0}),Qm=Tt(qm),Km=Ae({},ho,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Gm=Tt(Km),Ym=Ae({},ho,{data:0}),Dc=Tt(Ym),Xm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function eg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jm[e])?!!t[e]:!1}function wu(){return eg}var tg=Ae({},gl,{key:function(e){if(e.key){var t=Xm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wu,charCode:function(e){return e.type==="keypress"?fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ng=Tt(tg),rg=Ae({},Ki,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Fc=Tt(rg),og=Ae({},gl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wu}),lg=Tt(og),ig=Ae({},ho,{propertyName:0,elapsedTime:0,pseudoElement:0}),sg=Tt(ig),ag=Ae({},Ki,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ug=Tt(ag),cg=[9,13,27,32],ku=wn&&"CompositionEvent"in window,Wo=null;wn&&"documentMode"in document&&(Wo=document.documentMode);var dg=wn&&"TextEvent"in window&&!Wo,Ef=wn&&(!ku||Wo&&8<Wo&&11>=Wo),Uc=String.fromCharCode(32),$c=!1;function Tf(e,t){switch(e){case"keyup":return cg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Br=!1;function fg(e,t){switch(e){case"compositionend":return Nf(t);case"keypress":return t.which!==32?null:($c=!0,Uc);case"textInput":return e=t.data,e===Uc&&$c?null:e;default:return null}}function pg(e,t){if(Br)return e==="compositionend"||!ku&&Tf(e,t)?(e=_f(),di=yu=Un=null,Br=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ef&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function bf(e,t,n,r){of(r),t=bi(t,"onChange"),0<t.length&&(n=new vu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Vo=null,rl=null;function mg(e){Ff(e,0)}function Gi(e){var t=Vr(e);if(Xd(t))return e}function gg(e,t){if(e==="change")return t}var Pf=!1;if(wn){var Hs;if(wn){var Ws="oninput"in document;if(!Ws){var Hc=document.createElement("div");Hc.setAttribute("oninput","return;"),Ws=typeof Hc.oninput=="function"}Hs=Ws}else Hs=!1;Pf=Hs&&(!document.documentMode||9<document.documentMode)}function Wc(){Vo&&(Vo.detachEvent("onpropertychange",Of),rl=Vo=null)}function Of(e){if(e.propertyName==="value"&&Gi(rl)){var t=[];bf(t,rl,e,fu(e)),uf(mg,t)}}function yg(e,t,n){e==="focusin"?(Wc(),Vo=t,rl=n,Vo.attachEvent("onpropertychange",Of)):e==="focusout"&&Wc()}function vg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Gi(rl)}function wg(e,t){if(e==="click")return Gi(t)}function kg(e,t){if(e==="input"||e==="change")return Gi(t)}function Cg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Kt=typeof Object.is=="function"?Object.is:Cg;function ol(e,t){if(Kt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!aa.call(t,o)||!Kt(e[o],t[o]))return!1}return!0}function Vc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function qc(e,t){var n=Vc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Vc(n)}}function If(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?If(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Lf(){for(var e=window,t=Ci();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ci(e.document)}return t}function Cu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function xg(e){var t=Lf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&If(n.ownerDocument.documentElement,n)){if(r!==null&&Cu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,l=Math.min(r.start,o);r=r.end===void 0?l:Math.min(r.end,o),!e.extend&&l>r&&(o=r,r=l,l=o),o=qc(n,l);var a=qc(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Sg=wn&&"documentMode"in document&&11>=document.documentMode,Hr=null,Na=null,qo=null,ba=!1;function Qc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ba||Hr==null||Hr!==Ci(r)||(r=Hr,"selectionStart"in r&&Cu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),qo&&ol(qo,r)||(qo=r,r=bi(Na,"onSelect"),0<r.length&&(t=new vu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Hr)))}function Zl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Wr={animationend:Zl("Animation","AnimationEnd"),animationiteration:Zl("Animation","AnimationIteration"),animationstart:Zl("Animation","AnimationStart"),transitionend:Zl("Transition","TransitionEnd")},Vs={},jf={};wn&&(jf=document.createElement("div").style,"AnimationEvent"in window||(delete Wr.animationend.animation,delete Wr.animationiteration.animation,delete Wr.animationstart.animation),"TransitionEvent"in window||delete Wr.transitionend.transition);function Yi(e){if(Vs[e])return Vs[e];if(!Wr[e])return e;var t=Wr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in jf)return Vs[e]=t[n];return e}var Mf=Yi("animationend"),Af=Yi("animationiteration"),Rf=Yi("animationstart"),zf=Yi("transitionend"),Df=new Map,Kc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Zn(e,t){Df.set(e,t),Tr(t,[e])}for(var qs=0;qs<Kc.length;qs++){var Qs=Kc[qs],_g=Qs.toLowerCase(),Eg=Qs[0].toUpperCase()+Qs.slice(1);Zn(_g,"on"+Eg)}Zn(Mf,"onAnimationEnd");Zn(Af,"onAnimationIteration");Zn(Rf,"onAnimationStart");Zn("dblclick","onDoubleClick");Zn("focusin","onFocus");Zn("focusout","onBlur");Zn(zf,"onTransitionEnd");lo("onMouseEnter",["mouseout","mouseover"]);lo("onMouseLeave",["mouseout","mouseover"]);lo("onPointerEnter",["pointerout","pointerover"]);lo("onPointerLeave",["pointerout","pointerover"]);Tr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Tr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Tr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Tr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Tr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Tr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var $o="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tg=new Set("cancel close invalid load scroll toggle".split(" ").concat($o));function Gc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,_m(r,t,void 0,e),e.currentTarget=null}function Ff(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var a=r.length-1;0<=a;a--){var c=r[a],d=c.instance,h=c.currentTarget;if(c=c.listener,d!==l&&o.isPropagationStopped())break e;Gc(o,c,h),l=d}else for(a=0;a<r.length;a++){if(c=r[a],d=c.instance,h=c.currentTarget,c=c.listener,d!==l&&o.isPropagationStopped())break e;Gc(o,c,h),l=d}}}if(Si)throw e=Sa,Si=!1,Sa=null,e}function be(e,t){var n=t[ja];n===void 0&&(n=t[ja]=new Set);var r=e+"__bubble";n.has(r)||(Uf(t,e,2,!1),n.add(r))}function Ks(e,t,n){var r=0;t&&(r|=4),Uf(n,e,r,t)}var Jl="_reactListening"+Math.random().toString(36).slice(2);function ll(e){if(!e[Jl]){e[Jl]=!0,qd.forEach(function(n){n!=="selectionchange"&&(Tg.has(n)||Ks(n,!1,e),Ks(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Jl]||(t[Jl]=!0,Ks("selectionchange",!1,t))}}function Uf(e,t,n,r){switch(Sf(t)){case 1:var o=Um;break;case 4:o=$m;break;default:o=gu}n=o.bind(null,t,n,e),o=void 0,!xa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Gs(e,t,n,r,o){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var c=r.stateNode.containerInfo;if(c===o||c.nodeType===8&&c.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var d=a.tag;if((d===3||d===4)&&(d=a.stateNode.containerInfo,d===o||d.nodeType===8&&d.parentNode===o))return;a=a.return}for(;c!==null;){if(a=mr(c),a===null)return;if(d=a.tag,d===5||d===6){r=l=a;continue e}c=c.parentNode}}r=r.return}uf(function(){var h=l,v=fu(n),S=[];e:{var T=Df.get(e);if(T!==void 0){var k=vu,x=e;switch(e){case"keypress":if(fi(n)===0)break e;case"keydown":case"keyup":k=ng;break;case"focusin":x="focus",k=Bs;break;case"focusout":x="blur",k=Bs;break;case"beforeblur":case"afterblur":k=Bs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":k=zc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":k=Wm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":k=lg;break;case Mf:case Af:case Rf:k=Qm;break;case zf:k=sg;break;case"scroll":k=Bm;break;case"wheel":k=ug;break;case"copy":case"cut":case"paste":k=Gm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":k=Fc}var _=(t&4)!==0,I=!_&&e==="scroll",g=_?T!==null?T+"Capture":null:T;_=[];for(var p=h,f;p!==null;){f=p;var P=f.stateNode;if(f.tag===5&&P!==null&&(f=P,g!==null&&(P=Jo(p,g),P!=null&&_.push(il(p,P,f)))),I)break;p=p.return}0<_.length&&(T=new k(T,x,null,n,v),S.push({event:T,listeners:_}))}}if(!(t&7)){e:{if(T=e==="mouseover"||e==="pointerover",k=e==="mouseout"||e==="pointerout",T&&n!==ka&&(x=n.relatedTarget||n.fromElement)&&(mr(x)||x[kn]))break e;if((k||T)&&(T=v.window===v?v:(T=v.ownerDocument)?T.defaultView||T.parentWindow:window,k?(x=n.relatedTarget||n.toElement,k=h,x=x?mr(x):null,x!==null&&(I=Nr(x),x!==I||x.tag!==5&&x.tag!==6)&&(x=null)):(k=null,x=h),k!==x)){if(_=zc,P="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(_=Fc,P="onPointerLeave",g="onPointerEnter",p="pointer"),I=k==null?T:Vr(k),f=x==null?T:Vr(x),T=new _(P,p+"leave",k,n,v),T.target=I,T.relatedTarget=f,P=null,mr(v)===h&&(_=new _(g,p+"enter",x,n,v),_.target=f,_.relatedTarget=I,P=_),I=P,k&&x)t:{for(_=k,g=x,p=0,f=_;f;f=Fr(f))p++;for(f=0,P=g;P;P=Fr(P))f++;for(;0<p-f;)_=Fr(_),p--;for(;0<f-p;)g=Fr(g),f--;for(;p--;){if(_===g||g!==null&&_===g.alternate)break t;_=Fr(_),g=Fr(g)}_=null}else _=null;k!==null&&Yc(S,T,k,_,!1),x!==null&&I!==null&&Yc(S,I,x,_,!0)}}e:{if(T=h?Vr(h):window,k=T.nodeName&&T.nodeName.toLowerCase(),k==="select"||k==="input"&&T.type==="file")var N=gg;else if(Bc(T))if(Pf)N=kg;else{N=vg;var O=yg}else(k=T.nodeName)&&k.toLowerCase()==="input"&&(T.type==="checkbox"||T.type==="radio")&&(N=wg);if(N&&(N=N(e,h))){bf(S,N,n,v);break e}O&&O(e,T,h),e==="focusout"&&(O=T._wrapperState)&&O.controlled&&T.type==="number"&&ma(T,"number",T.value)}switch(O=h?Vr(h):window,e){case"focusin":(Bc(O)||O.contentEditable==="true")&&(Hr=O,Na=h,qo=null);break;case"focusout":qo=Na=Hr=null;break;case"mousedown":ba=!0;break;case"contextmenu":case"mouseup":case"dragend":ba=!1,Qc(S,n,v);break;case"selectionchange":if(Sg)break;case"keydown":case"keyup":Qc(S,n,v)}var m;if(ku)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Br?Tf(e,n)&&(w="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(w="onCompositionStart");w&&(Ef&&n.locale!=="ko"&&(Br||w!=="onCompositionStart"?w==="onCompositionEnd"&&Br&&(m=_f()):(Un=v,yu="value"in Un?Un.value:Un.textContent,Br=!0)),O=bi(h,w),0<O.length&&(w=new Dc(w,e,null,n,v),S.push({event:w,listeners:O}),m?w.data=m:(m=Nf(n),m!==null&&(w.data=m)))),(m=dg?fg(e,n):pg(e,n))&&(h=bi(h,"onBeforeInput"),0<h.length&&(v=new Dc("onBeforeInput","beforeinput",null,n,v),S.push({event:v,listeners:h}),v.data=m))}Ff(S,t)})}function il(e,t,n){return{instance:e,listener:t,currentTarget:n}}function bi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,l=o.stateNode;o.tag===5&&l!==null&&(o=l,l=Jo(e,n),l!=null&&r.unshift(il(e,l,o)),l=Jo(e,t),l!=null&&r.push(il(e,l,o))),e=e.return}return r}function Fr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Yc(e,t,n,r,o){for(var l=t._reactName,a=[];n!==null&&n!==r;){var c=n,d=c.alternate,h=c.stateNode;if(d!==null&&d===r)break;c.tag===5&&h!==null&&(c=h,o?(d=Jo(n,l),d!=null&&a.unshift(il(n,d,c))):o||(d=Jo(n,l),d!=null&&a.push(il(n,d,c)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Ng=/\r\n?/g,bg=/\u0000|\uFFFD/g;function Xc(e){return(typeof e=="string"?e:""+e).replace(Ng,`
`).replace(bg,"")}function ei(e,t,n){if(t=Xc(t),Xc(e)!==t&&n)throw Error(U(425))}function Pi(){}var Pa=null,Oa=null;function Ia(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var La=typeof setTimeout=="function"?setTimeout:void 0,Pg=typeof clearTimeout=="function"?clearTimeout:void 0,Zc=typeof Promise=="function"?Promise:void 0,Og=typeof queueMicrotask=="function"?queueMicrotask:typeof Zc<"u"?function(e){return Zc.resolve(null).then(e).catch(Ig)}:La;function Ig(e){setTimeout(function(){throw e})}function Ys(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),nl(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);nl(t)}function Vn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Jc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var mo=Math.random().toString(36).slice(2),rn="__reactFiber$"+mo,sl="__reactProps$"+mo,kn="__reactContainer$"+mo,ja="__reactEvents$"+mo,Lg="__reactListeners$"+mo,jg="__reactHandles$"+mo;function mr(e){var t=e[rn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[kn]||n[rn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Jc(e);e!==null;){if(n=e[rn])return n;e=Jc(e)}return t}e=n,n=e.parentNode}return null}function yl(e){return e=e[rn]||e[kn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Vr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(U(33))}function Xi(e){return e[sl]||null}var Ma=[],qr=-1;function Jn(e){return{current:e}}function Pe(e){0>qr||(e.current=Ma[qr],Ma[qr]=null,qr--)}function Ne(e,t){qr++,Ma[qr]=e.current,e.current=t}var Xn={},lt=Jn(Xn),gt=Jn(!1),Cr=Xn;function io(e,t){var n=e.type.contextTypes;if(!n)return Xn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},l;for(l in n)o[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function yt(e){return e=e.childContextTypes,e!=null}function Oi(){Pe(gt),Pe(lt)}function ed(e,t,n){if(lt.current!==Xn)throw Error(U(168));Ne(lt,t),Ne(gt,n)}function $f(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(U(108,ym(e)||"Unknown",o));return Ae({},n,r)}function Ii(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Xn,Cr=lt.current,Ne(lt,e),Ne(gt,gt.current),!0}function td(e,t,n){var r=e.stateNode;if(!r)throw Error(U(169));n?(e=$f(e,t,Cr),r.__reactInternalMemoizedMergedChildContext=e,Pe(gt),Pe(lt),Ne(lt,e)):Pe(gt),Ne(gt,n)}var hn=null,Zi=!1,Xs=!1;function Bf(e){hn===null?hn=[e]:hn.push(e)}function Mg(e){Zi=!0,Bf(e)}function er(){if(!Xs&&hn!==null){Xs=!0;var e=0,t=xe;try{var n=hn;for(xe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}hn=null,Zi=!1}catch(o){throw hn!==null&&(hn=hn.slice(e+1)),pf(pu,er),o}finally{xe=t,Xs=!1}}return null}var Qr=[],Kr=0,Li=null,ji=0,bt=[],Pt=0,xr=null,mn=1,gn="";function pr(e,t){Qr[Kr++]=ji,Qr[Kr++]=Li,Li=e,ji=t}function Hf(e,t,n){bt[Pt++]=mn,bt[Pt++]=gn,bt[Pt++]=xr,xr=e;var r=mn;e=gn;var o=32-qt(r)-1;r&=~(1<<o),n+=1;var l=32-qt(t)+o;if(30<l){var a=o-o%5;l=(r&(1<<a)-1).toString(32),r>>=a,o-=a,mn=1<<32-qt(t)+o|n<<o|r,gn=l+e}else mn=1<<l|n<<o|r,gn=e}function xu(e){e.return!==null&&(pr(e,1),Hf(e,1,0))}function Su(e){for(;e===Li;)Li=Qr[--Kr],Qr[Kr]=null,ji=Qr[--Kr],Qr[Kr]=null;for(;e===xr;)xr=bt[--Pt],bt[Pt]=null,gn=bt[--Pt],bt[Pt]=null,mn=bt[--Pt],bt[Pt]=null}var St=null,xt=null,Ie=!1,Vt=null;function Wf(e,t){var n=Ot(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function nd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,St=e,xt=Vn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,St=e,xt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=xr!==null?{id:mn,overflow:gn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ot(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,St=e,xt=null,!0):!1;default:return!1}}function Aa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ra(e){if(Ie){var t=xt;if(t){var n=t;if(!nd(e,t)){if(Aa(e))throw Error(U(418));t=Vn(n.nextSibling);var r=St;t&&nd(e,t)?Wf(r,n):(e.flags=e.flags&-4097|2,Ie=!1,St=e)}}else{if(Aa(e))throw Error(U(418));e.flags=e.flags&-4097|2,Ie=!1,St=e}}}function rd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;St=e}function ti(e){if(e!==St)return!1;if(!Ie)return rd(e),Ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ia(e.type,e.memoizedProps)),t&&(t=xt)){if(Aa(e))throw Vf(),Error(U(418));for(;t;)Wf(e,t),t=Vn(t.nextSibling)}if(rd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(U(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){xt=Vn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}xt=null}}else xt=St?Vn(e.stateNode.nextSibling):null;return!0}function Vf(){for(var e=xt;e;)e=Vn(e.nextSibling)}function so(){xt=St=null,Ie=!1}function _u(e){Vt===null?Vt=[e]:Vt.push(e)}var Ag=Sn.ReactCurrentBatchConfig;function Mo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(U(309));var r=n.stateNode}if(!r)throw Error(U(147,e));var o=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(a){var c=o.refs;a===null?delete c[l]:c[l]=a},t._stringRef=l,t)}if(typeof e!="string")throw Error(U(284));if(!n._owner)throw Error(U(290,e))}return e}function ni(e,t){throw e=Object.prototype.toString.call(t),Error(U(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function od(e){var t=e._init;return t(e._payload)}function qf(e){function t(g,p){if(e){var f=g.deletions;f===null?(g.deletions=[p],g.flags|=16):f.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function o(g,p){return g=Gn(g,p),g.index=0,g.sibling=null,g}function l(g,p,f){return g.index=f,e?(f=g.alternate,f!==null?(f=f.index,f<p?(g.flags|=2,p):f):(g.flags|=2,p)):(g.flags|=1048576,p)}function a(g){return e&&g.alternate===null&&(g.flags|=2),g}function c(g,p,f,P){return p===null||p.tag!==6?(p=oa(f,g.mode,P),p.return=g,p):(p=o(p,f),p.return=g,p)}function d(g,p,f,P){var N=f.type;return N===$r?v(g,p,f.props.children,P,f.key):p!==null&&(p.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===Rn&&od(N)===p.type)?(P=o(p,f.props),P.ref=Mo(g,p,f),P.return=g,P):(P=wi(f.type,f.key,f.props,null,g.mode,P),P.ref=Mo(g,p,f),P.return=g,P)}function h(g,p,f,P){return p===null||p.tag!==4||p.stateNode.containerInfo!==f.containerInfo||p.stateNode.implementation!==f.implementation?(p=la(f,g.mode,P),p.return=g,p):(p=o(p,f.children||[]),p.return=g,p)}function v(g,p,f,P,N){return p===null||p.tag!==7?(p=kr(f,g.mode,P,N),p.return=g,p):(p=o(p,f),p.return=g,p)}function S(g,p,f){if(typeof p=="string"&&p!==""||typeof p=="number")return p=oa(""+p,g.mode,f),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Vl:return f=wi(p.type,p.key,p.props,null,g.mode,f),f.ref=Mo(g,null,p),f.return=g,f;case Ur:return p=la(p,g.mode,f),p.return=g,p;case Rn:var P=p._init;return S(g,P(p._payload),f)}if(Fo(p)||Po(p))return p=kr(p,g.mode,f,null),p.return=g,p;ni(g,p)}return null}function T(g,p,f,P){var N=p!==null?p.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return N!==null?null:c(g,p,""+f,P);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Vl:return f.key===N?d(g,p,f,P):null;case Ur:return f.key===N?h(g,p,f,P):null;case Rn:return N=f._init,T(g,p,N(f._payload),P)}if(Fo(f)||Po(f))return N!==null?null:v(g,p,f,P,null);ni(g,f)}return null}function k(g,p,f,P,N){if(typeof P=="string"&&P!==""||typeof P=="number")return g=g.get(f)||null,c(p,g,""+P,N);if(typeof P=="object"&&P!==null){switch(P.$$typeof){case Vl:return g=g.get(P.key===null?f:P.key)||null,d(p,g,P,N);case Ur:return g=g.get(P.key===null?f:P.key)||null,h(p,g,P,N);case Rn:var O=P._init;return k(g,p,f,O(P._payload),N)}if(Fo(P)||Po(P))return g=g.get(f)||null,v(p,g,P,N,null);ni(p,P)}return null}function x(g,p,f,P){for(var N=null,O=null,m=p,w=p=0,R=null;m!==null&&w<f.length;w++){m.index>w?(R=m,m=null):R=m.sibling;var j=T(g,m,f[w],P);if(j===null){m===null&&(m=R);break}e&&m&&j.alternate===null&&t(g,m),p=l(j,p,w),O===null?N=j:O.sibling=j,O=j,m=R}if(w===f.length)return n(g,m),Ie&&pr(g,w),N;if(m===null){for(;w<f.length;w++)m=S(g,f[w],P),m!==null&&(p=l(m,p,w),O===null?N=m:O.sibling=m,O=m);return Ie&&pr(g,w),N}for(m=r(g,m);w<f.length;w++)R=k(m,g,w,f[w],P),R!==null&&(e&&R.alternate!==null&&m.delete(R.key===null?w:R.key),p=l(R,p,w),O===null?N=R:O.sibling=R,O=R);return e&&m.forEach(function(J){return t(g,J)}),Ie&&pr(g,w),N}function _(g,p,f,P){var N=Po(f);if(typeof N!="function")throw Error(U(150));if(f=N.call(f),f==null)throw Error(U(151));for(var O=N=null,m=p,w=p=0,R=null,j=f.next();m!==null&&!j.done;w++,j=f.next()){m.index>w?(R=m,m=null):R=m.sibling;var J=T(g,m,j.value,P);if(J===null){m===null&&(m=R);break}e&&m&&J.alternate===null&&t(g,m),p=l(J,p,w),O===null?N=J:O.sibling=J,O=J,m=R}if(j.done)return n(g,m),Ie&&pr(g,w),N;if(m===null){for(;!j.done;w++,j=f.next())j=S(g,j.value,P),j!==null&&(p=l(j,p,w),O===null?N=j:O.sibling=j,O=j);return Ie&&pr(g,w),N}for(m=r(g,m);!j.done;w++,j=f.next())j=k(m,g,w,j.value,P),j!==null&&(e&&j.alternate!==null&&m.delete(j.key===null?w:j.key),p=l(j,p,w),O===null?N=j:O.sibling=j,O=j);return e&&m.forEach(function(ce){return t(g,ce)}),Ie&&pr(g,w),N}function I(g,p,f,P){if(typeof f=="object"&&f!==null&&f.type===$r&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Vl:e:{for(var N=f.key,O=p;O!==null;){if(O.key===N){if(N=f.type,N===$r){if(O.tag===7){n(g,O.sibling),p=o(O,f.props.children),p.return=g,g=p;break e}}else if(O.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===Rn&&od(N)===O.type){n(g,O.sibling),p=o(O,f.props),p.ref=Mo(g,O,f),p.return=g,g=p;break e}n(g,O);break}else t(g,O);O=O.sibling}f.type===$r?(p=kr(f.props.children,g.mode,P,f.key),p.return=g,g=p):(P=wi(f.type,f.key,f.props,null,g.mode,P),P.ref=Mo(g,p,f),P.return=g,g=P)}return a(g);case Ur:e:{for(O=f.key;p!==null;){if(p.key===O)if(p.tag===4&&p.stateNode.containerInfo===f.containerInfo&&p.stateNode.implementation===f.implementation){n(g,p.sibling),p=o(p,f.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=la(f,g.mode,P),p.return=g,g=p}return a(g);case Rn:return O=f._init,I(g,p,O(f._payload),P)}if(Fo(f))return x(g,p,f,P);if(Po(f))return _(g,p,f,P);ni(g,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,p!==null&&p.tag===6?(n(g,p.sibling),p=o(p,f),p.return=g,g=p):(n(g,p),p=oa(f,g.mode,P),p.return=g,g=p),a(g)):n(g,p)}return I}var ao=qf(!0),Qf=qf(!1),Mi=Jn(null),Ai=null,Gr=null,Eu=null;function Tu(){Eu=Gr=Ai=null}function Nu(e){var t=Mi.current;Pe(Mi),e._currentValue=t}function za(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ro(e,t){Ai=e,Eu=Gr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(mt=!0),e.firstContext=null)}function Lt(e){var t=e._currentValue;if(Eu!==e)if(e={context:e,memoizedValue:t,next:null},Gr===null){if(Ai===null)throw Error(U(308));Gr=e,Ai.dependencies={lanes:0,firstContext:e}}else Gr=Gr.next=e;return t}var gr=null;function bu(e){gr===null?gr=[e]:gr.push(e)}function Kf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,bu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Cn(e,r)}function Cn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var zn=!1;function Pu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Gf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function yn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function qn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ge&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Cn(e,n)}return o=r.interleaved,o===null?(t.next=t,bu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Cn(e,n)}function pi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hu(e,n)}}function ld(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?o=l=a:l=l.next=a,n=n.next}while(n!==null);l===null?o=l=t:l=l.next=t}else o=l=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ri(e,t,n,r){var o=e.updateQueue;zn=!1;var l=o.firstBaseUpdate,a=o.lastBaseUpdate,c=o.shared.pending;if(c!==null){o.shared.pending=null;var d=c,h=d.next;d.next=null,a===null?l=h:a.next=h,a=d;var v=e.alternate;v!==null&&(v=v.updateQueue,c=v.lastBaseUpdate,c!==a&&(c===null?v.firstBaseUpdate=h:c.next=h,v.lastBaseUpdate=d))}if(l!==null){var S=o.baseState;a=0,v=h=d=null,c=l;do{var T=c.lane,k=c.eventTime;if((r&T)===T){v!==null&&(v=v.next={eventTime:k,lane:0,tag:c.tag,payload:c.payload,callback:c.callback,next:null});e:{var x=e,_=c;switch(T=t,k=n,_.tag){case 1:if(x=_.payload,typeof x=="function"){S=x.call(k,S,T);break e}S=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=_.payload,T=typeof x=="function"?x.call(k,S,T):x,T==null)break e;S=Ae({},S,T);break e;case 2:zn=!0}}c.callback!==null&&c.lane!==0&&(e.flags|=64,T=o.effects,T===null?o.effects=[c]:T.push(c))}else k={eventTime:k,lane:T,tag:c.tag,payload:c.payload,callback:c.callback,next:null},v===null?(h=v=k,d=S):v=v.next=k,a|=T;if(c=c.next,c===null){if(c=o.shared.pending,c===null)break;T=c,c=T.next,T.next=null,o.lastBaseUpdate=T,o.shared.pending=null}}while(1);if(v===null&&(d=S),o.baseState=d,o.firstBaseUpdate=h,o.lastBaseUpdate=v,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else l===null&&(o.shared.lanes=0);_r|=a,e.lanes=a,e.memoizedState=S}}function id(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(U(191,o));o.call(r)}}}var vl={},sn=Jn(vl),al=Jn(vl),ul=Jn(vl);function yr(e){if(e===vl)throw Error(U(174));return e}function Ou(e,t){switch(Ne(ul,t),Ne(al,e),Ne(sn,vl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ya(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ya(t,e)}Pe(sn),Ne(sn,t)}function uo(){Pe(sn),Pe(al),Pe(ul)}function Yf(e){yr(ul.current);var t=yr(sn.current),n=ya(t,e.type);t!==n&&(Ne(al,e),Ne(sn,n))}function Iu(e){al.current===e&&(Pe(sn),Pe(al))}var je=Jn(0);function zi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Zs=[];function Lu(){for(var e=0;e<Zs.length;e++)Zs[e]._workInProgressVersionPrimary=null;Zs.length=0}var hi=Sn.ReactCurrentDispatcher,Js=Sn.ReactCurrentBatchConfig,Sr=0,Me=null,Qe=null,Ye=null,Di=!1,Qo=!1,cl=0,Rg=0;function nt(){throw Error(U(321))}function ju(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Kt(e[n],t[n]))return!1;return!0}function Mu(e,t,n,r,o,l){if(Sr=l,Me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,hi.current=e===null||e.memoizedState===null?Ug:$g,e=n(r,o),Qo){l=0;do{if(Qo=!1,cl=0,25<=l)throw Error(U(301));l+=1,Ye=Qe=null,t.updateQueue=null,hi.current=Bg,e=n(r,o)}while(Qo)}if(hi.current=Fi,t=Qe!==null&&Qe.next!==null,Sr=0,Ye=Qe=Me=null,Di=!1,t)throw Error(U(300));return e}function Au(){var e=cl!==0;return cl=0,e}function tn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ye===null?Me.memoizedState=Ye=e:Ye=Ye.next=e,Ye}function jt(){if(Qe===null){var e=Me.alternate;e=e!==null?e.memoizedState:null}else e=Qe.next;var t=Ye===null?Me.memoizedState:Ye.next;if(t!==null)Ye=t,Qe=e;else{if(e===null)throw Error(U(310));Qe=e,e={memoizedState:Qe.memoizedState,baseState:Qe.baseState,baseQueue:Qe.baseQueue,queue:Qe.queue,next:null},Ye===null?Me.memoizedState=Ye=e:Ye=Ye.next=e}return Ye}function dl(e,t){return typeof t=="function"?t(e):t}function ea(e){var t=jt(),n=t.queue;if(n===null)throw Error(U(311));n.lastRenderedReducer=e;var r=Qe,o=r.baseQueue,l=n.pending;if(l!==null){if(o!==null){var a=o.next;o.next=l.next,l.next=a}r.baseQueue=o=l,n.pending=null}if(o!==null){l=o.next,r=r.baseState;var c=a=null,d=null,h=l;do{var v=h.lane;if((Sr&v)===v)d!==null&&(d=d.next={lane:0,action:h.action,hasEagerState:h.hasEagerState,eagerState:h.eagerState,next:null}),r=h.hasEagerState?h.eagerState:e(r,h.action);else{var S={lane:v,action:h.action,hasEagerState:h.hasEagerState,eagerState:h.eagerState,next:null};d===null?(c=d=S,a=r):d=d.next=S,Me.lanes|=v,_r|=v}h=h.next}while(h!==null&&h!==l);d===null?a=r:d.next=c,Kt(r,t.memoizedState)||(mt=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=d,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do l=o.lane,Me.lanes|=l,_r|=l,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ta(e){var t=jt(),n=t.queue;if(n===null)throw Error(U(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,l=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do l=e(l,a.action),a=a.next;while(a!==o);Kt(l,t.memoizedState)||(mt=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Xf(){}function Zf(e,t){var n=Me,r=jt(),o=t(),l=!Kt(r.memoizedState,o);if(l&&(r.memoizedState=o,mt=!0),r=r.queue,Ru(tp.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||Ye!==null&&Ye.memoizedState.tag&1){if(n.flags|=2048,fl(9,ep.bind(null,n,r,o,t),void 0,null),Xe===null)throw Error(U(349));Sr&30||Jf(n,t,o)}return o}function Jf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Me.updateQueue,t===null?(t={lastEffect:null,stores:null},Me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ep(e,t,n,r){t.value=n,t.getSnapshot=r,np(t)&&rp(e)}function tp(e,t,n){return n(function(){np(t)&&rp(e)})}function np(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Kt(e,n)}catch{return!0}}function rp(e){var t=Cn(e,1);t!==null&&Qt(t,e,1,-1)}function sd(e){var t=tn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:dl,lastRenderedState:e},t.queue=e,e=e.dispatch=Fg.bind(null,Me,e),[t.memoizedState,e]}function fl(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Me.updateQueue,t===null?(t={lastEffect:null,stores:null},Me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function op(){return jt().memoizedState}function mi(e,t,n,r){var o=tn();Me.flags|=e,o.memoizedState=fl(1|t,n,void 0,r===void 0?null:r)}function Ji(e,t,n,r){var o=jt();r=r===void 0?null:r;var l=void 0;if(Qe!==null){var a=Qe.memoizedState;if(l=a.destroy,r!==null&&ju(r,a.deps)){o.memoizedState=fl(t,n,l,r);return}}Me.flags|=e,o.memoizedState=fl(1|t,n,l,r)}function ad(e,t){return mi(8390656,8,e,t)}function Ru(e,t){return Ji(2048,8,e,t)}function lp(e,t){return Ji(4,2,e,t)}function ip(e,t){return Ji(4,4,e,t)}function sp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ap(e,t,n){return n=n!=null?n.concat([e]):null,Ji(4,4,sp.bind(null,t,e),n)}function zu(){}function up(e,t){var n=jt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ju(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function cp(e,t){var n=jt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ju(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function dp(e,t,n){return Sr&21?(Kt(n,t)||(n=gf(),Me.lanes|=n,_r|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,mt=!0),e.memoizedState=n)}function zg(e,t){var n=xe;xe=n!==0&&4>n?n:4,e(!0);var r=Js.transition;Js.transition={};try{e(!1),t()}finally{xe=n,Js.transition=r}}function fp(){return jt().memoizedState}function Dg(e,t,n){var r=Kn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},pp(e))hp(t,n);else if(n=Kf(e,t,n,r),n!==null){var o=at();Qt(n,e,r,o),mp(n,t,r)}}function Fg(e,t,n){var r=Kn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(pp(e))hp(t,o);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var a=t.lastRenderedState,c=l(a,n);if(o.hasEagerState=!0,o.eagerState=c,Kt(c,a)){var d=t.interleaved;d===null?(o.next=o,bu(t)):(o.next=d.next,d.next=o),t.interleaved=o;return}}catch{}finally{}n=Kf(e,t,o,r),n!==null&&(o=at(),Qt(n,e,r,o),mp(n,t,r))}}function pp(e){var t=e.alternate;return e===Me||t!==null&&t===Me}function hp(e,t){Qo=Di=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function mp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hu(e,n)}}var Fi={readContext:Lt,useCallback:nt,useContext:nt,useEffect:nt,useImperativeHandle:nt,useInsertionEffect:nt,useLayoutEffect:nt,useMemo:nt,useReducer:nt,useRef:nt,useState:nt,useDebugValue:nt,useDeferredValue:nt,useTransition:nt,useMutableSource:nt,useSyncExternalStore:nt,useId:nt,unstable_isNewReconciler:!1},Ug={readContext:Lt,useCallback:function(e,t){return tn().memoizedState=[e,t===void 0?null:t],e},useContext:Lt,useEffect:ad,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,mi(4194308,4,sp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mi(4194308,4,e,t)},useInsertionEffect:function(e,t){return mi(4,2,e,t)},useMemo:function(e,t){var n=tn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Dg.bind(null,Me,e),[r.memoizedState,e]},useRef:function(e){var t=tn();return e={current:e},t.memoizedState=e},useState:sd,useDebugValue:zu,useDeferredValue:function(e){return tn().memoizedState=e},useTransition:function(){var e=sd(!1),t=e[0];return e=zg.bind(null,e[1]),tn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Me,o=tn();if(Ie){if(n===void 0)throw Error(U(407));n=n()}else{if(n=t(),Xe===null)throw Error(U(349));Sr&30||Jf(r,t,n)}o.memoizedState=n;var l={value:n,getSnapshot:t};return o.queue=l,ad(tp.bind(null,r,l,e),[e]),r.flags|=2048,fl(9,ep.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=tn(),t=Xe.identifierPrefix;if(Ie){var n=gn,r=mn;n=(r&~(1<<32-qt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=cl++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Rg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},$g={readContext:Lt,useCallback:up,useContext:Lt,useEffect:Ru,useImperativeHandle:ap,useInsertionEffect:lp,useLayoutEffect:ip,useMemo:cp,useReducer:ea,useRef:op,useState:function(){return ea(dl)},useDebugValue:zu,useDeferredValue:function(e){var t=jt();return dp(t,Qe.memoizedState,e)},useTransition:function(){var e=ea(dl)[0],t=jt().memoizedState;return[e,t]},useMutableSource:Xf,useSyncExternalStore:Zf,useId:fp,unstable_isNewReconciler:!1},Bg={readContext:Lt,useCallback:up,useContext:Lt,useEffect:Ru,useImperativeHandle:ap,useInsertionEffect:lp,useLayoutEffect:ip,useMemo:cp,useReducer:ta,useRef:op,useState:function(){return ta(dl)},useDebugValue:zu,useDeferredValue:function(e){var t=jt();return Qe===null?t.memoizedState=e:dp(t,Qe.memoizedState,e)},useTransition:function(){var e=ta(dl)[0],t=jt().memoizedState;return[e,t]},useMutableSource:Xf,useSyncExternalStore:Zf,useId:fp,unstable_isNewReconciler:!1};function Ht(e,t){if(e&&e.defaultProps){t=Ae({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Da(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Ae({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var es={isMounted:function(e){return(e=e._reactInternals)?Nr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=at(),o=Kn(e),l=yn(r,o);l.payload=t,n!=null&&(l.callback=n),t=qn(e,l,o),t!==null&&(Qt(t,e,o,r),pi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=at(),o=Kn(e),l=yn(r,o);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=qn(e,l,o),t!==null&&(Qt(t,e,o,r),pi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=at(),r=Kn(e),o=yn(n,r);o.tag=2,t!=null&&(o.callback=t),t=qn(e,o,r),t!==null&&(Qt(t,e,r,n),pi(t,e,r))}};function ud(e,t,n,r,o,l,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,a):t.prototype&&t.prototype.isPureReactComponent?!ol(n,r)||!ol(o,l):!0}function gp(e,t,n){var r=!1,o=Xn,l=t.contextType;return typeof l=="object"&&l!==null?l=Lt(l):(o=yt(t)?Cr:lt.current,r=t.contextTypes,l=(r=r!=null)?io(e,o):Xn),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=es,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=l),t}function cd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&es.enqueueReplaceState(t,t.state,null)}function Fa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Pu(e);var l=t.contextType;typeof l=="object"&&l!==null?o.context=Lt(l):(l=yt(t)?Cr:lt.current,o.context=io(e,l)),o.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(Da(e,t,l,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&es.enqueueReplaceState(o,o.state,null),Ri(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function co(e,t){try{var n="",r=t;do n+=gm(r),r=r.return;while(r);var o=n}catch(l){o=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:o,digest:null}}function na(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ua(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Hg=typeof WeakMap=="function"?WeakMap:Map;function yp(e,t,n){n=yn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){$i||($i=!0,Ya=r),Ua(e,t)},n}function vp(e,t,n){n=yn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ua(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){Ua(e,t),typeof r!="function"&&(Qn===null?Qn=new Set([this]):Qn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function dd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Hg;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=ry.bind(null,e,t,n),t.then(e,e))}function fd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function pd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=yn(-1,1),t.tag=2,qn(n,t,1))),n.lanes|=1),e)}var Wg=Sn.ReactCurrentOwner,mt=!1;function st(e,t,n,r){t.child=e===null?Qf(t,null,n,r):ao(t,e.child,n,r)}function hd(e,t,n,r,o){n=n.render;var l=t.ref;return ro(t,o),r=Mu(e,t,n,r,l,o),n=Au(),e!==null&&!mt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,xn(e,t,o)):(Ie&&n&&xu(t),t.flags|=1,st(e,t,r,o),t.child)}function md(e,t,n,r,o){if(e===null){var l=n.type;return typeof l=="function"&&!Vu(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,wp(e,t,l,r,o)):(e=wi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&o)){var a=l.memoizedProps;if(n=n.compare,n=n!==null?n:ol,n(a,r)&&e.ref===t.ref)return xn(e,t,o)}return t.flags|=1,e=Gn(l,r),e.ref=t.ref,e.return=t,t.child=e}function wp(e,t,n,r,o){if(e!==null){var l=e.memoizedProps;if(ol(l,r)&&e.ref===t.ref)if(mt=!1,t.pendingProps=r=l,(e.lanes&o)!==0)e.flags&131072&&(mt=!0);else return t.lanes=e.lanes,xn(e,t,o)}return $a(e,t,n,r,o)}function kp(e,t,n){var r=t.pendingProps,o=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ne(Xr,Ct),Ct|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ne(Xr,Ct),Ct|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,Ne(Xr,Ct),Ct|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,Ne(Xr,Ct),Ct|=r;return st(e,t,o,n),t.child}function Cp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function $a(e,t,n,r,o){var l=yt(n)?Cr:lt.current;return l=io(t,l),ro(t,o),n=Mu(e,t,n,r,l,o),r=Au(),e!==null&&!mt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,xn(e,t,o)):(Ie&&r&&xu(t),t.flags|=1,st(e,t,n,o),t.child)}function gd(e,t,n,r,o){if(yt(n)){var l=!0;Ii(t)}else l=!1;if(ro(t,o),t.stateNode===null)gi(e,t),gp(t,n,r),Fa(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,c=t.memoizedProps;a.props=c;var d=a.context,h=n.contextType;typeof h=="object"&&h!==null?h=Lt(h):(h=yt(n)?Cr:lt.current,h=io(t,h));var v=n.getDerivedStateFromProps,S=typeof v=="function"||typeof a.getSnapshotBeforeUpdate=="function";S||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(c!==r||d!==h)&&cd(t,a,r,h),zn=!1;var T=t.memoizedState;a.state=T,Ri(t,r,a,o),d=t.memoizedState,c!==r||T!==d||gt.current||zn?(typeof v=="function"&&(Da(t,n,v,r),d=t.memoizedState),(c=zn||ud(t,n,c,r,T,d,h))?(S||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=d),a.props=r,a.state=d,a.context=h,r=c):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Gf(e,t),c=t.memoizedProps,h=t.type===t.elementType?c:Ht(t.type,c),a.props=h,S=t.pendingProps,T=a.context,d=n.contextType,typeof d=="object"&&d!==null?d=Lt(d):(d=yt(n)?Cr:lt.current,d=io(t,d));var k=n.getDerivedStateFromProps;(v=typeof k=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(c!==S||T!==d)&&cd(t,a,r,d),zn=!1,T=t.memoizedState,a.state=T,Ri(t,r,a,o);var x=t.memoizedState;c!==S||T!==x||gt.current||zn?(typeof k=="function"&&(Da(t,n,k,r),x=t.memoizedState),(h=zn||ud(t,n,h,r,T,x,d)||!1)?(v||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,x,d),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,x,d)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||c===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),a.props=r,a.state=x,a.context=d,r=h):(typeof a.componentDidUpdate!="function"||c===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),r=!1)}return Ba(e,t,n,r,l,o)}function Ba(e,t,n,r,o,l){Cp(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&td(t,n,!1),xn(e,t,l);r=t.stateNode,Wg.current=t;var c=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=ao(t,e.child,null,l),t.child=ao(t,null,c,l)):st(e,t,c,l),t.memoizedState=r.state,o&&td(t,n,!0),t.child}function xp(e){var t=e.stateNode;t.pendingContext?ed(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ed(e,t.context,!1),Ou(e,t.containerInfo)}function yd(e,t,n,r,o){return so(),_u(o),t.flags|=256,st(e,t,n,r),t.child}var Ha={dehydrated:null,treeContext:null,retryLane:0};function Wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function Sp(e,t,n){var r=t.pendingProps,o=je.current,l=!1,a=(t.flags&128)!==0,c;if((c=a)||(c=e!==null&&e.memoizedState===null?!1:(o&2)!==0),c?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Ne(je,o&1),e===null)return Ra(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,l?(r=t.mode,l=t.child,a={mode:"hidden",children:a},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=a):l=rs(a,r,0,null),e=kr(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Wa(n),t.memoizedState=Ha,e):Du(t,a));if(o=e.memoizedState,o!==null&&(c=o.dehydrated,c!==null))return Vg(e,t,a,r,c,o,n);if(l){l=r.fallback,a=t.mode,o=e.child,c=o.sibling;var d={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=d,t.deletions=null):(r=Gn(o,d),r.subtreeFlags=o.subtreeFlags&14680064),c!==null?l=Gn(c,l):(l=kr(l,a,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,a=e.child.memoizedState,a=a===null?Wa(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},l.memoizedState=a,l.childLanes=e.childLanes&~n,t.memoizedState=Ha,r}return l=e.child,e=l.sibling,r=Gn(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Du(e,t){return t=rs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ri(e,t,n,r){return r!==null&&_u(r),ao(t,e.child,null,n),e=Du(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Vg(e,t,n,r,o,l,a){if(n)return t.flags&256?(t.flags&=-257,r=na(Error(U(422))),ri(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,o=t.mode,r=rs({mode:"visible",children:r.children},o,0,null),l=kr(l,o,a,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&ao(t,e.child,null,a),t.child.memoizedState=Wa(a),t.memoizedState=Ha,l);if(!(t.mode&1))return ri(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var c=r.dgst;return r=c,l=Error(U(419)),r=na(l,r,void 0),ri(e,t,a,r)}if(c=(a&e.childLanes)!==0,mt||c){if(r=Xe,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==l.retryLane&&(l.retryLane=o,Cn(e,o),Qt(r,e,o,-1))}return Wu(),r=na(Error(U(421))),ri(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=oy.bind(null,e),o._reactRetry=t,null):(e=l.treeContext,xt=Vn(o.nextSibling),St=t,Ie=!0,Vt=null,e!==null&&(bt[Pt++]=mn,bt[Pt++]=gn,bt[Pt++]=xr,mn=e.id,gn=e.overflow,xr=t),t=Du(t,r.children),t.flags|=4096,t)}function vd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),za(e.return,t,n)}function ra(e,t,n,r,o){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=o)}function _p(e,t,n){var r=t.pendingProps,o=r.revealOrder,l=r.tail;if(st(e,t,r.children,n),r=je.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&vd(e,n,t);else if(e.tag===19)vd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ne(je,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&zi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ra(t,!1,o,n,l);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&zi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ra(t,!0,n,null,l);break;case"together":ra(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function gi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function xn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),_r|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(U(153));if(t.child!==null){for(e=t.child,n=Gn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Gn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qg(e,t,n){switch(t.tag){case 3:xp(t),so();break;case 5:Yf(t);break;case 1:yt(t.type)&&Ii(t);break;case 4:Ou(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Ne(Mi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Ne(je,je.current&1),t.flags|=128,null):n&t.child.childLanes?Sp(e,t,n):(Ne(je,je.current&1),e=xn(e,t,n),e!==null?e.sibling:null);Ne(je,je.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return _p(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Ne(je,je.current),r)break;return null;case 22:case 23:return t.lanes=0,kp(e,t,n)}return xn(e,t,n)}var Ep,Va,Tp,Np;Ep=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Va=function(){};Tp=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,yr(sn.current);var l=null;switch(n){case"input":o=pa(e,o),r=pa(e,r),l=[];break;case"select":o=Ae({},o,{value:void 0}),r=Ae({},r,{value:void 0}),l=[];break;case"textarea":o=ga(e,o),r=ga(e,r),l=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Pi)}va(n,r);var a;n=null;for(h in o)if(!r.hasOwnProperty(h)&&o.hasOwnProperty(h)&&o[h]!=null)if(h==="style"){var c=o[h];for(a in c)c.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else h!=="dangerouslySetInnerHTML"&&h!=="children"&&h!=="suppressContentEditableWarning"&&h!=="suppressHydrationWarning"&&h!=="autoFocus"&&(Xo.hasOwnProperty(h)?l||(l=[]):(l=l||[]).push(h,null));for(h in r){var d=r[h];if(c=o?.[h],r.hasOwnProperty(h)&&d!==c&&(d!=null||c!=null))if(h==="style")if(c){for(a in c)!c.hasOwnProperty(a)||d&&d.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in d)d.hasOwnProperty(a)&&c[a]!==d[a]&&(n||(n={}),n[a]=d[a])}else n||(l||(l=[]),l.push(h,n)),n=d;else h==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,c=c?c.__html:void 0,d!=null&&c!==d&&(l=l||[]).push(h,d)):h==="children"?typeof d!="string"&&typeof d!="number"||(l=l||[]).push(h,""+d):h!=="suppressContentEditableWarning"&&h!=="suppressHydrationWarning"&&(Xo.hasOwnProperty(h)?(d!=null&&h==="onScroll"&&be("scroll",e),l||c===d||(l=[])):(l=l||[]).push(h,d))}n&&(l=l||[]).push("style",n);var h=l;(t.updateQueue=h)&&(t.flags|=4)}};Np=function(e,t,n,r){n!==r&&(t.flags|=4)};function Ao(e,t){if(!Ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function rt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qg(e,t,n){var r=t.pendingProps;switch(Su(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rt(t),null;case 1:return yt(t.type)&&Oi(),rt(t),null;case 3:return r=t.stateNode,uo(),Pe(gt),Pe(lt),Lu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ti(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Vt!==null&&(Ja(Vt),Vt=null))),Va(e,t),rt(t),null;case 5:Iu(t);var o=yr(ul.current);if(n=t.type,e!==null&&t.stateNode!=null)Tp(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(U(166));return rt(t),null}if(e=yr(sn.current),ti(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[rn]=t,r[sl]=l,e=(t.mode&1)!==0,n){case"dialog":be("cancel",r),be("close",r);break;case"iframe":case"object":case"embed":be("load",r);break;case"video":case"audio":for(o=0;o<$o.length;o++)be($o[o],r);break;case"source":be("error",r);break;case"img":case"image":case"link":be("error",r),be("load",r);break;case"details":be("toggle",r);break;case"input":Nc(r,l),be("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},be("invalid",r);break;case"textarea":Pc(r,l),be("invalid",r)}va(n,l),o=null;for(var a in l)if(l.hasOwnProperty(a)){var c=l[a];a==="children"?typeof c=="string"?r.textContent!==c&&(l.suppressHydrationWarning!==!0&&ei(r.textContent,c,e),o=["children",c]):typeof c=="number"&&r.textContent!==""+c&&(l.suppressHydrationWarning!==!0&&ei(r.textContent,c,e),o=["children",""+c]):Xo.hasOwnProperty(a)&&c!=null&&a==="onScroll"&&be("scroll",r)}switch(n){case"input":ql(r),bc(r,l,!0);break;case"textarea":ql(r),Oc(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=Pi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ef(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[rn]=t,e[sl]=r,Ep(e,t,!1,!1),t.stateNode=e;e:{switch(a=wa(n,r),n){case"dialog":be("cancel",e),be("close",e),o=r;break;case"iframe":case"object":case"embed":be("load",e),o=r;break;case"video":case"audio":for(o=0;o<$o.length;o++)be($o[o],e);o=r;break;case"source":be("error",e),o=r;break;case"img":case"image":case"link":be("error",e),be("load",e),o=r;break;case"details":be("toggle",e),o=r;break;case"input":Nc(e,r),o=pa(e,r),be("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Ae({},r,{value:void 0}),be("invalid",e);break;case"textarea":Pc(e,r),o=ga(e,r),be("invalid",e);break;default:o=r}va(n,o),c=o;for(l in c)if(c.hasOwnProperty(l)){var d=c[l];l==="style"?rf(e,d):l==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,d!=null&&tf(e,d)):l==="children"?typeof d=="string"?(n!=="textarea"||d!=="")&&Zo(e,d):typeof d=="number"&&Zo(e,""+d):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(Xo.hasOwnProperty(l)?d!=null&&l==="onScroll"&&be("scroll",e):d!=null&&au(e,l,d,a))}switch(n){case"input":ql(e),bc(e,r,!1);break;case"textarea":ql(e),Oc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Yn(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?Jr(e,!!r.multiple,l,!1):r.defaultValue!=null&&Jr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Pi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return rt(t),null;case 6:if(e&&t.stateNode!=null)Np(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(U(166));if(n=yr(ul.current),yr(sn.current),ti(t)){if(r=t.stateNode,n=t.memoizedProps,r[rn]=t,(l=r.nodeValue!==n)&&(e=St,e!==null))switch(e.tag){case 3:ei(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ei(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[rn]=t,t.stateNode=r}return rt(t),null;case 13:if(Pe(je),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ie&&xt!==null&&t.mode&1&&!(t.flags&128))Vf(),so(),t.flags|=98560,l=!1;else if(l=ti(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(U(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(U(317));l[rn]=t}else so(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;rt(t),l=!1}else Vt!==null&&(Ja(Vt),Vt=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||je.current&1?Ke===0&&(Ke=3):Wu())),t.updateQueue!==null&&(t.flags|=4),rt(t),null);case 4:return uo(),Va(e,t),e===null&&ll(t.stateNode.containerInfo),rt(t),null;case 10:return Nu(t.type._context),rt(t),null;case 17:return yt(t.type)&&Oi(),rt(t),null;case 19:if(Pe(je),l=t.memoizedState,l===null)return rt(t),null;if(r=(t.flags&128)!==0,a=l.rendering,a===null)if(r)Ao(l,!1);else{if(Ke!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=zi(e),a!==null){for(t.flags|=128,Ao(l,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,a=l.alternate,a===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=a.childLanes,l.lanes=a.lanes,l.child=a.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=a.memoizedProps,l.memoizedState=a.memoizedState,l.updateQueue=a.updateQueue,l.type=a.type,e=a.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ne(je,je.current&1|2),t.child}e=e.sibling}l.tail!==null&&Fe()>fo&&(t.flags|=128,r=!0,Ao(l,!1),t.lanes=4194304)}else{if(!r)if(e=zi(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ao(l,!0),l.tail===null&&l.tailMode==="hidden"&&!a.alternate&&!Ie)return rt(t),null}else 2*Fe()-l.renderingStartTime>fo&&n!==1073741824&&(t.flags|=128,r=!0,Ao(l,!1),t.lanes=4194304);l.isBackwards?(a.sibling=t.child,t.child=a):(n=l.last,n!==null?n.sibling=a:t.child=a,l.last=a)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Fe(),t.sibling=null,n=je.current,Ne(je,r?n&1|2:n&1),t):(rt(t),null);case 22:case 23:return Hu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ct&1073741824&&(rt(t),t.subtreeFlags&6&&(t.flags|=8192)):rt(t),null;case 24:return null;case 25:return null}throw Error(U(156,t.tag))}function Kg(e,t){switch(Su(t),t.tag){case 1:return yt(t.type)&&Oi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return uo(),Pe(gt),Pe(lt),Lu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Iu(t),null;case 13:if(Pe(je),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(U(340));so()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Pe(je),null;case 4:return uo(),null;case 10:return Nu(t.type._context),null;case 22:case 23:return Hu(),null;case 24:return null;default:return null}}var oi=!1,ot=!1,Gg=typeof WeakSet=="function"?WeakSet:Set,G=null;function Yr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){De(e,t,r)}else n.current=null}function qa(e,t,n){try{n()}catch(r){De(e,t,r)}}var wd=!1;function Yg(e,t){if(Pa=Ti,e=Lf(),Cu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var a=0,c=-1,d=-1,h=0,v=0,S=e,T=null;t:for(;;){for(var k;S!==n||o!==0&&S.nodeType!==3||(c=a+o),S!==l||r!==0&&S.nodeType!==3||(d=a+r),S.nodeType===3&&(a+=S.nodeValue.length),(k=S.firstChild)!==null;)T=S,S=k;for(;;){if(S===e)break t;if(T===n&&++h===o&&(c=a),T===l&&++v===r&&(d=a),(k=S.nextSibling)!==null)break;S=T,T=S.parentNode}S=k}n=c===-1||d===-1?null:{start:c,end:d}}else n=null}n=n||{start:0,end:0}}else n=null;for(Oa={focusedElem:e,selectionRange:n},Ti=!1,G=t;G!==null;)if(t=G,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,G=e;else for(;G!==null;){t=G;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var _=x.memoizedProps,I=x.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?_:Ht(t.type,_),I);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(U(163))}}catch(P){De(t,t.return,P)}if(e=t.sibling,e!==null){e.return=t.return,G=e;break}G=t.return}return x=wd,wd=!1,x}function Ko(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var l=o.destroy;o.destroy=void 0,l!==void 0&&qa(t,n,l)}o=o.next}while(o!==r)}}function ts(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Qa(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function bp(e){var t=e.alternate;t!==null&&(e.alternate=null,bp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[rn],delete t[sl],delete t[ja],delete t[Lg],delete t[jg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Pp(e){return e.tag===5||e.tag===3||e.tag===4}function kd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Pp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ka(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Pi));else if(r!==4&&(e=e.child,e!==null))for(Ka(e,t,n),e=e.sibling;e!==null;)Ka(e,t,n),e=e.sibling}function Ga(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ga(e,t,n),e=e.sibling;e!==null;)Ga(e,t,n),e=e.sibling}var Ze=null,Wt=!1;function An(e,t,n){for(n=n.child;n!==null;)Op(e,t,n),n=n.sibling}function Op(e,t,n){if(ln&&typeof ln.onCommitFiberUnmount=="function")try{ln.onCommitFiberUnmount(Qi,n)}catch{}switch(n.tag){case 5:ot||Yr(n,t);case 6:var r=Ze,o=Wt;Ze=null,An(e,t,n),Ze=r,Wt=o,Ze!==null&&(Wt?(e=Ze,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ze.removeChild(n.stateNode));break;case 18:Ze!==null&&(Wt?(e=Ze,n=n.stateNode,e.nodeType===8?Ys(e.parentNode,n):e.nodeType===1&&Ys(e,n),nl(e)):Ys(Ze,n.stateNode));break;case 4:r=Ze,o=Wt,Ze=n.stateNode.containerInfo,Wt=!0,An(e,t,n),Ze=r,Wt=o;break;case 0:case 11:case 14:case 15:if(!ot&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var l=o,a=l.destroy;l=l.tag,a!==void 0&&(l&2||l&4)&&qa(n,t,a),o=o.next}while(o!==r)}An(e,t,n);break;case 1:if(!ot&&(Yr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(c){De(n,t,c)}An(e,t,n);break;case 21:An(e,t,n);break;case 22:n.mode&1?(ot=(r=ot)||n.memoizedState!==null,An(e,t,n),ot=r):An(e,t,n);break;default:An(e,t,n)}}function Cd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Gg),t.forEach(function(r){var o=ly.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var l=e,a=t,c=a;e:for(;c!==null;){switch(c.tag){case 5:Ze=c.stateNode,Wt=!1;break e;case 3:Ze=c.stateNode.containerInfo,Wt=!0;break e;case 4:Ze=c.stateNode.containerInfo,Wt=!0;break e}c=c.return}if(Ze===null)throw Error(U(160));Op(l,a,o),Ze=null,Wt=!1;var d=o.alternate;d!==null&&(d.return=null),o.return=null}catch(h){De(o,t,h)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Ip(t,e),t=t.sibling}function Ip(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),en(e),r&4){try{Ko(3,e,e.return),ts(3,e)}catch(_){De(e,e.return,_)}try{Ko(5,e,e.return)}catch(_){De(e,e.return,_)}}break;case 1:Bt(t,e),en(e),r&512&&n!==null&&Yr(n,n.return);break;case 5:if(Bt(t,e),en(e),r&512&&n!==null&&Yr(n,n.return),e.flags&32){var o=e.stateNode;try{Zo(o,"")}catch(_){De(e,e.return,_)}}if(r&4&&(o=e.stateNode,o!=null)){var l=e.memoizedProps,a=n!==null?n.memoizedProps:l,c=e.type,d=e.updateQueue;if(e.updateQueue=null,d!==null)try{c==="input"&&l.type==="radio"&&l.name!=null&&Zd(o,l),wa(c,a);var h=wa(c,l);for(a=0;a<d.length;a+=2){var v=d[a],S=d[a+1];v==="style"?rf(o,S):v==="dangerouslySetInnerHTML"?tf(o,S):v==="children"?Zo(o,S):au(o,v,S,h)}switch(c){case"input":ha(o,l);break;case"textarea":Jd(o,l);break;case"select":var T=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!l.multiple;var k=l.value;k!=null?Jr(o,!!l.multiple,k,!1):T!==!!l.multiple&&(l.defaultValue!=null?Jr(o,!!l.multiple,l.defaultValue,!0):Jr(o,!!l.multiple,l.multiple?[]:"",!1))}o[sl]=l}catch(_){De(e,e.return,_)}}break;case 6:if(Bt(t,e),en(e),r&4){if(e.stateNode===null)throw Error(U(162));o=e.stateNode,l=e.memoizedProps;try{o.nodeValue=l}catch(_){De(e,e.return,_)}}break;case 3:if(Bt(t,e),en(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{nl(t.containerInfo)}catch(_){De(e,e.return,_)}break;case 4:Bt(t,e),en(e);break;case 13:Bt(t,e),en(e),o=e.child,o.flags&8192&&(l=o.memoizedState!==null,o.stateNode.isHidden=l,!l||o.alternate!==null&&o.alternate.memoizedState!==null||($u=Fe())),r&4&&Cd(e);break;case 22:if(v=n!==null&&n.memoizedState!==null,e.mode&1?(ot=(h=ot)||v,Bt(t,e),ot=h):Bt(t,e),en(e),r&8192){if(h=e.memoizedState!==null,(e.stateNode.isHidden=h)&&!v&&e.mode&1)for(G=e,v=e.child;v!==null;){for(S=G=v;G!==null;){switch(T=G,k=T.child,T.tag){case 0:case 11:case 14:case 15:Ko(4,T,T.return);break;case 1:Yr(T,T.return);var x=T.stateNode;if(typeof x.componentWillUnmount=="function"){r=T,n=T.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(_){De(r,n,_)}}break;case 5:Yr(T,T.return);break;case 22:if(T.memoizedState!==null){Sd(S);continue}}k!==null?(k.return=T,G=k):Sd(S)}v=v.sibling}e:for(v=null,S=e;;){if(S.tag===5){if(v===null){v=S;try{o=S.stateNode,h?(l=o.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(c=S.stateNode,d=S.memoizedProps.style,a=d!=null&&d.hasOwnProperty("display")?d.display:null,c.style.display=nf("display",a))}catch(_){De(e,e.return,_)}}}else if(S.tag===6){if(v===null)try{S.stateNode.nodeValue=h?"":S.memoizedProps}catch(_){De(e,e.return,_)}}else if((S.tag!==22&&S.tag!==23||S.memoizedState===null||S===e)&&S.child!==null){S.child.return=S,S=S.child;continue}if(S===e)break e;for(;S.sibling===null;){if(S.return===null||S.return===e)break e;v===S&&(v=null),S=S.return}v===S&&(v=null),S.sibling.return=S.return,S=S.sibling}}break;case 19:Bt(t,e),en(e),r&4&&Cd(e);break;case 21:break;default:Bt(t,e),en(e)}}function en(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Pp(n)){var r=n;break e}n=n.return}throw Error(U(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Zo(o,""),r.flags&=-33);var l=kd(e);Ga(e,l,o);break;case 3:case 4:var a=r.stateNode.containerInfo,c=kd(e);Ka(e,c,a);break;default:throw Error(U(161))}}catch(d){De(e,e.return,d)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Xg(e,t,n){G=e,Lp(e)}function Lp(e,t,n){for(var r=(e.mode&1)!==0;G!==null;){var o=G,l=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||oi;if(!a){var c=o.alternate,d=c!==null&&c.memoizedState!==null||ot;c=oi;var h=ot;if(oi=a,(ot=d)&&!h)for(G=o;G!==null;)a=G,d=a.child,a.tag===22&&a.memoizedState!==null?_d(o):d!==null?(d.return=a,G=d):_d(o);for(;l!==null;)G=l,Lp(l),l=l.sibling;G=o,oi=c,ot=h}xd(e)}else o.subtreeFlags&8772&&l!==null?(l.return=o,G=l):xd(e)}}function xd(e){for(;G!==null;){var t=G;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ot||ts(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ot)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ht(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&id(t,l,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}id(t,a,n)}break;case 5:var c=t.stateNode;if(n===null&&t.flags&4){n=c;var d=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":d.autoFocus&&n.focus();break;case"img":d.src&&(n.src=d.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var h=t.alternate;if(h!==null){var v=h.memoizedState;if(v!==null){var S=v.dehydrated;S!==null&&nl(S)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(U(163))}ot||t.flags&512&&Qa(t)}catch(T){De(t,t.return,T)}}if(t===e){G=null;break}if(n=t.sibling,n!==null){n.return=t.return,G=n;break}G=t.return}}function Sd(e){for(;G!==null;){var t=G;if(t===e){G=null;break}var n=t.sibling;if(n!==null){n.return=t.return,G=n;break}G=t.return}}function _d(e){for(;G!==null;){var t=G;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ts(4,t)}catch(d){De(t,n,d)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(d){De(t,o,d)}}var l=t.return;try{Qa(t)}catch(d){De(t,l,d)}break;case 5:var a=t.return;try{Qa(t)}catch(d){De(t,a,d)}}}catch(d){De(t,t.return,d)}if(t===e){G=null;break}var c=t.sibling;if(c!==null){c.return=t.return,G=c;break}G=t.return}}var Zg=Math.ceil,Ui=Sn.ReactCurrentDispatcher,Fu=Sn.ReactCurrentOwner,It=Sn.ReactCurrentBatchConfig,ge=0,Xe=null,We=null,Je=0,Ct=0,Xr=Jn(0),Ke=0,pl=null,_r=0,ns=0,Uu=0,Go=null,ht=null,$u=0,fo=1/0,pn=null,$i=!1,Ya=null,Qn=null,li=!1,$n=null,Bi=0,Yo=0,Xa=null,yi=-1,vi=0;function at(){return ge&6?Fe():yi!==-1?yi:yi=Fe()}function Kn(e){return e.mode&1?ge&2&&Je!==0?Je&-Je:Ag.transition!==null?(vi===0&&(vi=gf()),vi):(e=xe,e!==0||(e=window.event,e=e===void 0?16:Sf(e.type)),e):1}function Qt(e,t,n,r){if(50<Yo)throw Yo=0,Xa=null,Error(U(185));ml(e,n,r),(!(ge&2)||e!==Xe)&&(e===Xe&&(!(ge&2)&&(ns|=n),Ke===4&&Fn(e,Je)),vt(e,r),n===1&&ge===0&&!(t.mode&1)&&(fo=Fe()+500,Zi&&er()))}function vt(e,t){var n=e.callbackNode;Am(e,t);var r=Ei(e,e===Xe?Je:0);if(r===0)n!==null&&jc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&jc(n),t===1)e.tag===0?Mg(Ed.bind(null,e)):Bf(Ed.bind(null,e)),Og(function(){!(ge&6)&&er()}),n=null;else{switch(yf(r)){case 1:n=pu;break;case 4:n=hf;break;case 16:n=_i;break;case 536870912:n=mf;break;default:n=_i}n=Up(n,jp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function jp(e,t){if(yi=-1,vi=0,ge&6)throw Error(U(327));var n=e.callbackNode;if(oo()&&e.callbackNode!==n)return null;var r=Ei(e,e===Xe?Je:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Hi(e,r);else{t=r;var o=ge;ge|=2;var l=Ap();(Xe!==e||Je!==t)&&(pn=null,fo=Fe()+500,wr(e,t));do try{ty();break}catch(c){Mp(e,c)}while(1);Tu(),Ui.current=l,ge=o,We!==null?t=0:(Xe=null,Je=0,t=Ke)}if(t!==0){if(t===2&&(o=_a(e),o!==0&&(r=o,t=Za(e,o))),t===1)throw n=pl,wr(e,0),Fn(e,r),vt(e,Fe()),n;if(t===6)Fn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Jg(o)&&(t=Hi(e,r),t===2&&(l=_a(e),l!==0&&(r=l,t=Za(e,l))),t===1))throw n=pl,wr(e,0),Fn(e,r),vt(e,Fe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(U(345));case 2:hr(e,ht,pn);break;case 3:if(Fn(e,r),(r&130023424)===r&&(t=$u+500-Fe(),10<t)){if(Ei(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){at(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=La(hr.bind(null,e,ht,pn),t);break}hr(e,ht,pn);break;case 4:if(Fn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-qt(r);l=1<<a,a=t[a],a>o&&(o=a),r&=~l}if(r=o,r=Fe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Zg(r/1960))-r,10<r){e.timeoutHandle=La(hr.bind(null,e,ht,pn),r);break}hr(e,ht,pn);break;case 5:hr(e,ht,pn);break;default:throw Error(U(329))}}}return vt(e,Fe()),e.callbackNode===n?jp.bind(null,e):null}function Za(e,t){var n=Go;return e.current.memoizedState.isDehydrated&&(wr(e,t).flags|=256),e=Hi(e,t),e!==2&&(t=ht,ht=n,t!==null&&Ja(t)),e}function Ja(e){ht===null?ht=e:ht.push.apply(ht,e)}function Jg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],l=o.getSnapshot;o=o.value;try{if(!Kt(l(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Fn(e,t){for(t&=~Uu,t&=~ns,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qt(t),r=1<<n;e[n]=-1,t&=~r}}function Ed(e){if(ge&6)throw Error(U(327));oo();var t=Ei(e,0);if(!(t&1))return vt(e,Fe()),null;var n=Hi(e,t);if(e.tag!==0&&n===2){var r=_a(e);r!==0&&(t=r,n=Za(e,r))}if(n===1)throw n=pl,wr(e,0),Fn(e,t),vt(e,Fe()),n;if(n===6)throw Error(U(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,hr(e,ht,pn),vt(e,Fe()),null}function Bu(e,t){var n=ge;ge|=1;try{return e(t)}finally{ge=n,ge===0&&(fo=Fe()+500,Zi&&er())}}function Er(e){$n!==null&&$n.tag===0&&!(ge&6)&&oo();var t=ge;ge|=1;var n=It.transition,r=xe;try{if(It.transition=null,xe=1,e)return e()}finally{xe=r,It.transition=n,ge=t,!(ge&6)&&er()}}function Hu(){Ct=Xr.current,Pe(Xr)}function wr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Pg(n)),We!==null)for(n=We.return;n!==null;){var r=n;switch(Su(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Oi();break;case 3:uo(),Pe(gt),Pe(lt),Lu();break;case 5:Iu(r);break;case 4:uo();break;case 13:Pe(je);break;case 19:Pe(je);break;case 10:Nu(r.type._context);break;case 22:case 23:Hu()}n=n.return}if(Xe=e,We=e=Gn(e.current,null),Je=Ct=t,Ke=0,pl=null,Uu=ns=_r=0,ht=Go=null,gr!==null){for(t=0;t<gr.length;t++)if(n=gr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,l=n.pending;if(l!==null){var a=l.next;l.next=o,r.next=a}n.pending=r}gr=null}return e}function Mp(e,t){do{var n=We;try{if(Tu(),hi.current=Fi,Di){for(var r=Me.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Di=!1}if(Sr=0,Ye=Qe=Me=null,Qo=!1,cl=0,Fu.current=null,n===null||n.return===null){Ke=1,pl=t,We=null;break}e:{var l=e,a=n.return,c=n,d=t;if(t=Je,c.flags|=32768,d!==null&&typeof d=="object"&&typeof d.then=="function"){var h=d,v=c,S=v.tag;if(!(v.mode&1)&&(S===0||S===11||S===15)){var T=v.alternate;T?(v.updateQueue=T.updateQueue,v.memoizedState=T.memoizedState,v.lanes=T.lanes):(v.updateQueue=null,v.memoizedState=null)}var k=fd(a);if(k!==null){k.flags&=-257,pd(k,a,c,l,t),k.mode&1&&dd(l,h,t),t=k,d=h;var x=t.updateQueue;if(x===null){var _=new Set;_.add(d),t.updateQueue=_}else x.add(d);break e}else{if(!(t&1)){dd(l,h,t),Wu();break e}d=Error(U(426))}}else if(Ie&&c.mode&1){var I=fd(a);if(I!==null){!(I.flags&65536)&&(I.flags|=256),pd(I,a,c,l,t),_u(co(d,c));break e}}l=d=co(d,c),Ke!==4&&(Ke=2),Go===null?Go=[l]:Go.push(l),l=a;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var g=yp(l,d,t);ld(l,g);break e;case 1:c=d;var p=l.type,f=l.stateNode;if(!(l.flags&128)&&(typeof p.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Qn===null||!Qn.has(f)))){l.flags|=65536,t&=-t,l.lanes|=t;var P=vp(l,c,t);ld(l,P);break e}}l=l.return}while(l!==null)}zp(n)}catch(N){t=N,We===n&&n!==null&&(We=n=n.return);continue}break}while(1)}function Ap(){var e=Ui.current;return Ui.current=Fi,e===null?Fi:e}function Wu(){(Ke===0||Ke===3||Ke===2)&&(Ke=4),Xe===null||!(_r&268435455)&&!(ns&268435455)||Fn(Xe,Je)}function Hi(e,t){var n=ge;ge|=2;var r=Ap();(Xe!==e||Je!==t)&&(pn=null,wr(e,t));do try{ey();break}catch(o){Mp(e,o)}while(1);if(Tu(),ge=n,Ui.current=r,We!==null)throw Error(U(261));return Xe=null,Je=0,Ke}function ey(){for(;We!==null;)Rp(We)}function ty(){for(;We!==null&&!Tm();)Rp(We)}function Rp(e){var t=Fp(e.alternate,e,Ct);e.memoizedProps=e.pendingProps,t===null?zp(e):We=t,Fu.current=null}function zp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Kg(n,t),n!==null){n.flags&=32767,We=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ke=6,We=null;return}}else if(n=Qg(n,t,Ct),n!==null){We=n;return}if(t=t.sibling,t!==null){We=t;return}We=t=e}while(t!==null);Ke===0&&(Ke=5)}function hr(e,t,n){var r=xe,o=It.transition;try{It.transition=null,xe=1,ny(e,t,n,r)}finally{It.transition=o,xe=r}return null}function ny(e,t,n,r){do oo();while($n!==null);if(ge&6)throw Error(U(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(U(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(Rm(e,l),e===Xe&&(We=Xe=null,Je=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||li||(li=!0,Up(_i,function(){return oo(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=It.transition,It.transition=null;var a=xe;xe=1;var c=ge;ge|=4,Fu.current=null,Yg(e,n),Ip(n,e),xg(Oa),Ti=!!Pa,Oa=Pa=null,e.current=n,Xg(n),Nm(),ge=c,xe=a,It.transition=l}else e.current=n;if(li&&(li=!1,$n=e,Bi=o),l=e.pendingLanes,l===0&&(Qn=null),Om(n.stateNode),vt(e,Fe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if($i)throw $i=!1,e=Ya,Ya=null,e;return Bi&1&&e.tag!==0&&oo(),l=e.pendingLanes,l&1?e===Xa?Yo++:(Yo=0,Xa=e):Yo=0,er(),null}function oo(){if($n!==null){var e=yf(Bi),t=It.transition,n=xe;try{if(It.transition=null,xe=16>e?16:e,$n===null)var r=!1;else{if(e=$n,$n=null,Bi=0,ge&6)throw Error(U(331));var o=ge;for(ge|=4,G=e.current;G!==null;){var l=G,a=l.child;if(G.flags&16){var c=l.deletions;if(c!==null){for(var d=0;d<c.length;d++){var h=c[d];for(G=h;G!==null;){var v=G;switch(v.tag){case 0:case 11:case 15:Ko(8,v,l)}var S=v.child;if(S!==null)S.return=v,G=S;else for(;G!==null;){v=G;var T=v.sibling,k=v.return;if(bp(v),v===h){G=null;break}if(T!==null){T.return=k,G=T;break}G=k}}}var x=l.alternate;if(x!==null){var _=x.child;if(_!==null){x.child=null;do{var I=_.sibling;_.sibling=null,_=I}while(_!==null)}}G=l}}if(l.subtreeFlags&2064&&a!==null)a.return=l,G=a;else e:for(;G!==null;){if(l=G,l.flags&2048)switch(l.tag){case 0:case 11:case 15:Ko(9,l,l.return)}var g=l.sibling;if(g!==null){g.return=l.return,G=g;break e}G=l.return}}var p=e.current;for(G=p;G!==null;){a=G;var f=a.child;if(a.subtreeFlags&2064&&f!==null)f.return=a,G=f;else e:for(a=p;G!==null;){if(c=G,c.flags&2048)try{switch(c.tag){case 0:case 11:case 15:ts(9,c)}}catch(N){De(c,c.return,N)}if(c===a){G=null;break e}var P=c.sibling;if(P!==null){P.return=c.return,G=P;break e}G=c.return}}if(ge=o,er(),ln&&typeof ln.onPostCommitFiberRoot=="function")try{ln.onPostCommitFiberRoot(Qi,e)}catch{}r=!0}return r}finally{xe=n,It.transition=t}}return!1}function Td(e,t,n){t=co(n,t),t=yp(e,t,1),e=qn(e,t,1),t=at(),e!==null&&(ml(e,1,t),vt(e,t))}function De(e,t,n){if(e.tag===3)Td(e,e,n);else for(;t!==null;){if(t.tag===3){Td(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Qn===null||!Qn.has(r))){e=co(n,e),e=vp(t,e,1),t=qn(t,e,1),e=at(),t!==null&&(ml(t,1,e),vt(t,e));break}}t=t.return}}function ry(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=at(),e.pingedLanes|=e.suspendedLanes&n,Xe===e&&(Je&n)===n&&(Ke===4||Ke===3&&(Je&130023424)===Je&&500>Fe()-$u?wr(e,0):Uu|=n),vt(e,t)}function Dp(e,t){t===0&&(e.mode&1?(t=Gl,Gl<<=1,!(Gl&130023424)&&(Gl=4194304)):t=1);var n=at();e=Cn(e,t),e!==null&&(ml(e,t,n),vt(e,n))}function oy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Dp(e,n)}function ly(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(U(314))}r!==null&&r.delete(t),Dp(e,n)}var Fp;Fp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||gt.current)mt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return mt=!1,qg(e,t,n);mt=!!(e.flags&131072)}else mt=!1,Ie&&t.flags&1048576&&Hf(t,ji,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;gi(e,t),e=t.pendingProps;var o=io(t,lt.current);ro(t,n),o=Mu(null,t,r,e,o,n);var l=Au();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,yt(r)?(l=!0,Ii(t)):l=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Pu(t),o.updater=es,t.stateNode=o,o._reactInternals=t,Fa(t,r,e,n),t=Ba(null,t,r,!0,l,n)):(t.tag=0,Ie&&l&&xu(t),st(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(gi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=sy(r),e=Ht(r,e),o){case 0:t=$a(null,t,r,e,n);break e;case 1:t=gd(null,t,r,e,n);break e;case 11:t=hd(null,t,r,e,n);break e;case 14:t=md(null,t,r,Ht(r.type,e),n);break e}throw Error(U(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),$a(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),gd(e,t,r,o,n);case 3:e:{if(xp(t),e===null)throw Error(U(387));r=t.pendingProps,l=t.memoizedState,o=l.element,Gf(e,t),Ri(t,r,null,n);var a=t.memoizedState;if(r=a.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){o=co(Error(U(423)),t),t=yd(e,t,r,n,o);break e}else if(r!==o){o=co(Error(U(424)),t),t=yd(e,t,r,n,o);break e}else for(xt=Vn(t.stateNode.containerInfo.firstChild),St=t,Ie=!0,Vt=null,n=Qf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(so(),r===o){t=xn(e,t,n);break e}st(e,t,r,n)}t=t.child}return t;case 5:return Yf(t),e===null&&Ra(t),r=t.type,o=t.pendingProps,l=e!==null?e.memoizedProps:null,a=o.children,Ia(r,o)?a=null:l!==null&&Ia(r,l)&&(t.flags|=32),Cp(e,t),st(e,t,a,n),t.child;case 6:return e===null&&Ra(t),null;case 13:return Sp(e,t,n);case 4:return Ou(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=ao(t,null,r,n):st(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),hd(e,t,r,o,n);case 7:return st(e,t,t.pendingProps,n),t.child;case 8:return st(e,t,t.pendingProps.children,n),t.child;case 12:return st(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,l=t.memoizedProps,a=o.value,Ne(Mi,r._currentValue),r._currentValue=a,l!==null)if(Kt(l.value,a)){if(l.children===o.children&&!gt.current){t=xn(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var c=l.dependencies;if(c!==null){a=l.child;for(var d=c.firstContext;d!==null;){if(d.context===r){if(l.tag===1){d=yn(-1,n&-n),d.tag=2;var h=l.updateQueue;if(h!==null){h=h.shared;var v=h.pending;v===null?d.next=d:(d.next=v.next,v.next=d),h.pending=d}}l.lanes|=n,d=l.alternate,d!==null&&(d.lanes|=n),za(l.return,n,t),c.lanes|=n;break}d=d.next}}else if(l.tag===10)a=l.type===t.type?null:l.child;else if(l.tag===18){if(a=l.return,a===null)throw Error(U(341));a.lanes|=n,c=a.alternate,c!==null&&(c.lanes|=n),za(a,n,t),a=l.sibling}else a=l.child;if(a!==null)a.return=l;else for(a=l;a!==null;){if(a===t){a=null;break}if(l=a.sibling,l!==null){l.return=a.return,a=l;break}a=a.return}l=a}st(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,ro(t,n),o=Lt(o),r=r(o),t.flags|=1,st(e,t,r,n),t.child;case 14:return r=t.type,o=Ht(r,t.pendingProps),o=Ht(r.type,o),md(e,t,r,o,n);case 15:return wp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),gi(e,t),t.tag=1,yt(r)?(e=!0,Ii(t)):e=!1,ro(t,n),gp(t,r,o),Fa(t,r,o,n),Ba(null,t,r,!0,e,n);case 19:return _p(e,t,n);case 22:return kp(e,t,n)}throw Error(U(156,t.tag))};function Up(e,t){return pf(e,t)}function iy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ot(e,t,n,r){return new iy(e,t,n,r)}function Vu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sy(e){if(typeof e=="function")return Vu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===cu)return 11;if(e===du)return 14}return 2}function Gn(e,t){var n=e.alternate;return n===null?(n=Ot(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function wi(e,t,n,r,o,l){var a=2;if(r=e,typeof e=="function")Vu(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case $r:return kr(n.children,o,l,t);case uu:a=8,o|=8;break;case ua:return e=Ot(12,n,t,o|2),e.elementType=ua,e.lanes=l,e;case ca:return e=Ot(13,n,t,o),e.elementType=ca,e.lanes=l,e;case da:return e=Ot(19,n,t,o),e.elementType=da,e.lanes=l,e;case Gd:return rs(n,o,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Qd:a=10;break e;case Kd:a=9;break e;case cu:a=11;break e;case du:a=14;break e;case Rn:a=16,r=null;break e}throw Error(U(130,e==null?e:typeof e,""))}return t=Ot(a,n,t,o),t.elementType=e,t.type=r,t.lanes=l,t}function kr(e,t,n,r){return e=Ot(7,e,r,t),e.lanes=n,e}function rs(e,t,n,r){return e=Ot(22,e,r,t),e.elementType=Gd,e.lanes=n,e.stateNode={isHidden:!1},e}function oa(e,t,n){return e=Ot(6,e,null,t),e.lanes=n,e}function la(e,t,n){return t=Ot(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ay(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Fs(0),this.expirationTimes=Fs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function qu(e,t,n,r,o,l,a,c,d){return e=new ay(e,t,n,c,d),t===1?(t=1,l===!0&&(t|=8)):t=0,l=Ot(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Pu(l),e}function uy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ur,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function $p(e){if(!e)return Xn;e=e._reactInternals;e:{if(Nr(e)!==e||e.tag!==1)throw Error(U(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(yt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(U(171))}if(e.tag===1){var n=e.type;if(yt(n))return $f(e,n,t)}return t}function Bp(e,t,n,r,o,l,a,c,d){return e=qu(n,r,!0,e,o,l,a,c,d),e.context=$p(null),n=e.current,r=at(),o=Kn(n),l=yn(r,o),l.callback=t??null,qn(n,l,o),e.current.lanes=o,ml(e,o,r),vt(e,r),e}function os(e,t,n,r){var o=t.current,l=at(),a=Kn(o);return n=$p(n),t.context===null?t.context=n:t.pendingContext=n,t=yn(l,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=qn(o,t,a),e!==null&&(Qt(e,o,a,l),pi(e,o,a)),a}function Wi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Nd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Qu(e,t){Nd(e,t),(e=e.alternate)&&Nd(e,t)}function cy(){return null}var Hp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}ls.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(U(409));os(e,t,null,null)};ls.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Er(function(){os(null,e,null,null)}),t[kn]=null}};function ls(e){this._internalRoot=e}ls.prototype.unstable_scheduleHydration=function(e){if(e){var t=kf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dn.length&&t!==0&&t<Dn[n].priority;n++);Dn.splice(n,0,e),n===0&&xf(e)}};function Gu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function is(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function bd(){}function dy(e,t,n,r,o){if(o){if(typeof r=="function"){var l=r;r=function(){var h=Wi(a);l.call(h)}}var a=Bp(t,r,e,0,null,!1,!1,"",bd);return e._reactRootContainer=a,e[kn]=a.current,ll(e.nodeType===8?e.parentNode:e),Er(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var c=r;r=function(){var h=Wi(d);c.call(h)}}var d=qu(e,0,!1,null,null,!1,!1,"",bd);return e._reactRootContainer=d,e[kn]=d.current,ll(e.nodeType===8?e.parentNode:e),Er(function(){os(t,d,n,r)}),d}function ss(e,t,n,r,o){var l=n._reactRootContainer;if(l){var a=l;if(typeof o=="function"){var c=o;o=function(){var d=Wi(a);c.call(d)}}os(t,a,e,o)}else a=dy(n,t,e,o,r);return Wi(a)}vf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Uo(t.pendingLanes);n!==0&&(hu(t,n|1),vt(t,Fe()),!(ge&6)&&(fo=Fe()+500,er()))}break;case 13:Er(function(){var r=Cn(e,1);if(r!==null){var o=at();Qt(r,e,1,o)}}),Qu(e,1)}};mu=function(e){if(e.tag===13){var t=Cn(e,134217728);if(t!==null){var n=at();Qt(t,e,134217728,n)}Qu(e,134217728)}};wf=function(e){if(e.tag===13){var t=Kn(e),n=Cn(e,t);if(n!==null){var r=at();Qt(n,e,t,r)}Qu(e,t)}};kf=function(){return xe};Cf=function(e,t){var n=xe;try{return xe=e,t()}finally{xe=n}};Ca=function(e,t,n){switch(t){case"input":if(ha(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Xi(r);if(!o)throw Error(U(90));Xd(r),ha(r,o)}}}break;case"textarea":Jd(e,n);break;case"select":t=n.value,t!=null&&Jr(e,!!n.multiple,t,!1)}};sf=Bu;af=Er;var fy={usingClientEntryPoint:!1,Events:[yl,Vr,Xi,of,lf,Bu]},Ro={findFiberByHostInstance:mr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},py={bundleType:Ro.bundleType,version:Ro.version,rendererPackageName:Ro.rendererPackageName,rendererConfig:Ro.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Sn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=df(e),e===null?null:e.stateNode},findFiberByHostInstance:Ro.findFiberByHostInstance||cy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ii=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ii.isDisabled&&ii.supportsFiber)try{Qi=ii.inject(py),ln=ii}catch{}}Et.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fy;Et.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Gu(t))throw Error(U(200));return uy(e,t,null,n)};Et.createRoot=function(e,t){if(!Gu(e))throw Error(U(299));var n=!1,r="",o=Hp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=qu(e,1,!1,null,null,n,!1,r,o),e[kn]=t.current,ll(e.nodeType===8?e.parentNode:e),new Ku(t)};Et.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(U(188)):(e=Object.keys(e).join(","),Error(U(268,e)));return e=df(t),e=e===null?null:e.stateNode,e};Et.flushSync=function(e){return Er(e)};Et.hydrate=function(e,t,n){if(!is(t))throw Error(U(200));return ss(null,e,t,!0,n)};Et.hydrateRoot=function(e,t,n){if(!Gu(e))throw Error(U(405));var r=n!=null&&n.hydratedSources||null,o=!1,l="",a=Hp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Bp(t,null,e,1,n??null,o,!1,l,a),e[kn]=t.current,ll(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ls(t)};Et.render=function(e,t,n){if(!is(t))throw Error(U(200));return ss(null,e,t,!1,n)};Et.unmountComponentAtNode=function(e){if(!is(e))throw Error(U(40));return e._reactRootContainer?(Er(function(){ss(null,null,e,!1,function(){e._reactRootContainer=null,e[kn]=null})}),!0):!1};Et.unstable_batchedUpdates=Bu;Et.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!is(n))throw Error(U(200));if(e==null||e._reactInternals===void 0)throw Error(U(38));return ss(e,t,n,!1,r)};Et.version="18.3.1-next-f1338f8080-20240426";function Wp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Wp)}catch(e){console.error(e)}}Wp(),Hd.exports=Et;var hy=Hd.exports,Vp,Pd=hy;Vp=Pd.createRoot,Pd.hydrateRoot;let qp=logseq.isMainUIVisible;function my(e,t){return logseq.on(e,t),()=>{logseq.off(e,t)}}const gy=e=>my("ui:visible:changed",({visible:t})=>{qp=t,e()}),yy=()=>$d.useSyncExternalStore(gy,()=>qp),nn={apiUrl:"https://api.openai.com/v1/chat/completions",apiKey:"",modelName:"gpt-3.5-turbo",temperature:.7,maxTokens:2e3,enableHistory:!1,customPrompts:[{name:"总结",prompt:"请总结以下内容的要点："},{name:"扩展",prompt:"请基于以下内容进行扩展和补充："}],keybindings:{openChat:{binding:"ctrl+g",mac:"cmd+g",description:"打开AI聊天"},quickReply:{binding:"ctrl+shift+g",mac:"cmd+shift+g",description:"快速AI回复"},openChatWindow:{binding:"ctrl+alt+g",mac:"cmd+alt+g",description:"打开聊天窗口"}}};async function vy(){const e=logseq.settings||{},t=Object.assign({},nn,e);return Array.isArray(t.customPrompts)||(t.customPrompts=nn.customPrompts),t.temperature===void 0&&(t.temperature=nn.temperature),t.maxTokens===void 0&&(t.maxTokens=nn.maxTokens),t.keybindings?(t.keybindings.openChat||(t.keybindings.openChat=nn.keybindings.openChat),t.keybindings.quickReply||(t.keybindings.quickReply=nn.keybindings.quickReply),t.keybindings.openChatWindow||(t.keybindings.openChatWindow=nn.keybindings.openChatWindow)):t.keybindings=nn.keybindings,await logseq.updateSettings(t),t}async function wy(e){await logseq.updateSettings(e)}async function as(){const e=logseq.settings||{};return Object.assign({},nn,e)}function ky(e){if(!e||typeof e!="string")return{isValid:!1,error:"快捷键不能为空"};const t=e.trim();if(!t)return{isValid:!1,error:"快捷键不能为空"};const n=["ctrl","cmd","alt","shift","meta"],r=t.toLowerCase().split("+");if(r.length===0)return{isValid:!1,error:"快捷键格式无效"};const o=r[r.length-1];if(!o||o.length===0)return{isValid:!1,error:"缺少按键部分"};const l=r.slice(0,-1);for(const d of l)if(!n.includes(d))return{isValid:!1,error:`无效的修饰键: ${d}`};return[...new Set(l)].length!==l.length?{isValid:!1,error:"不能有重复的修饰键"}:/^[a-z0-9]$|^f[1-9]$|^f1[0-2]$|^(space|enter|tab|escape|backspace|delete|home|end|pageup|pagedown|insert|up|down|left|right)$/.test(o)?{isValid:!0}:{isValid:!1,error:`无效的按键: ${o}`}}function ia(e,t=!1){const n=t?e.mac:e.binding;return n?n.split("+").map(r=>{switch(r.toLowerCase()){case"ctrl":return t?"⌃":"Ctrl";case"cmd":return"⌘";case"alt":return t?"⌥":"Alt";case"shift":return t?"⇧":"Shift";case"meta":return t?"⌘":"Win";default:return r.toUpperCase()}}).join(t?"":"+"):""}function Cy(e,t){const n=e.toLowerCase().trim();if(!n)return{hasConflict:!1};const r=["ctrl+c","cmd+c","ctrl+v","cmd+v","ctrl+x","cmd+x","ctrl+z","cmd+z","ctrl+y","cmd+y","ctrl+a","cmd+a","ctrl+s","cmd+s","ctrl+f","cmd+f","ctrl+n","cmd+n","ctrl+o","cmd+o","ctrl+p","cmd+p","ctrl+w","cmd+w","ctrl+t","cmd+t","ctrl+r","cmd+r","ctrl+l","cmd+l","ctrl+d","cmd+d","ctrl+h","cmd+h","ctrl+j","cmd+j","ctrl+k","cmd+k","ctrl+u","cmd+u","alt+f4","cmd+q","f5","cmd+r","f11","f12"],o=["ctrl+shift+p","cmd+shift+p","ctrl+k","cmd+k","ctrl+shift+k","cmd+shift+k","ctrl+/","cmd+/","ctrl+shift+/","cmd+shift+/","ctrl+e","cmd+e","ctrl+shift+e","cmd+shift+e"];if(r.includes(n))return{hasConflict:!0,type:"system",message:`可能与系统快捷键冲突: ${n}`};if(o.includes(n))return{hasConflict:!0,type:"system",message:`可能与Logseq快捷键冲突: ${n}`};if(t){for(const[l,a]of Object.entries(t))if(a.binding.toLowerCase()===n||a.mac.toLowerCase()===n)return{hasConflict:!0,type:"internal",message:`与 "${a.description}" 快捷键冲突`}}return{hasConflict:!1}}class Zr{static instance;currentTheme;listeners=[];mediaQuery;constructor(){this.mediaQuery=window.matchMedia("(prefers-color-scheme: dark)"),this.currentTheme={mode:"auto",isDark:!1,systemPrefersDark:this.mediaQuery.matches},this.mediaQuery.addEventListener("change",this.handleSystemThemeChange.bind(this)),this.initializeTheme()}static getInstance(){return Zr.instance||(Zr.instance=new Zr),Zr.instance}async initializeTheme(){try{const t=await logseq.App.getStateFromStore("ui/theme");this.currentTheme.isDark=t==="dark",this.currentTheme.mode=t==="dark"?"dark":"light",this.applyTheme(),logseq.App.onThemeModeChanged(({mode:n})=>{this.currentTheme.isDark=n==="dark",this.currentTheme.mode=n==="dark"?"dark":"light",this.applyTheme(),this.notifyListeners()})}catch(t){console.error("主题初始化失败:",t),this.currentTheme.isDark=this.currentTheme.systemPrefersDark,this.applyTheme()}}handleSystemThemeChange=t=>{this.currentTheme.systemPrefersDark=t.matches,this.currentTheme.mode==="auto"&&(this.currentTheme.isDark=t.matches,this.applyTheme(),this.notifyListeners())};applyTheme(){const t=document.documentElement;this.currentTheme.isDark?t.classList.add("dark"):t.classList.remove("dark"),this.setCSSVariables()}setCSSVariables(){const t=document.documentElement;this.currentTheme.isDark?(t.style.setProperty("--theme-bg-primary","#1a1a1a"),t.style.setProperty("--theme-bg-secondary","#2a2a2a"),t.style.setProperty("--theme-bg-tertiary","#3a3a3a"),t.style.setProperty("--theme-text-primary","#e0e0e0"),t.style.setProperty("--theme-text-secondary","#a0a0a0"),t.style.setProperty("--theme-text-muted","#666666"),t.style.setProperty("--theme-border-primary","#404040"),t.style.setProperty("--theme-border-secondary","#505050"),t.style.setProperty("--theme-accent","#0A84FF"),t.style.setProperty("--theme-accent-hover","#0066CC")):(t.style.setProperty("--theme-bg-primary","#ffffff"),t.style.setProperty("--theme-bg-secondary","#f8f9fa"),t.style.setProperty("--theme-bg-tertiary","#e9ecef"),t.style.setProperty("--theme-text-primary","#333333"),t.style.setProperty("--theme-text-secondary","#666666"),t.style.setProperty("--theme-text-muted","#999999"),t.style.setProperty("--theme-border-primary","#e0e0e0"),t.style.setProperty("--theme-border-secondary","#f0f0f0"),t.style.setProperty("--theme-accent","#007AFF"),t.style.setProperty("--theme-accent-hover","#0056CC"))}getTheme(){return{...this.currentTheme}}setTheme(t){switch(this.currentTheme.mode=t,t){case"light":this.currentTheme.isDark=!1;break;case"dark":this.currentTheme.isDark=!0;break;case"auto":this.currentTheme.isDark=this.currentTheme.systemPrefersDark;break}this.applyTheme(),this.notifyListeners()}toggleTheme(){const t=this.currentTheme.isDark?"light":"dark";this.setTheme(t)}addListener(t){this.listeners.push(t)}removeListener(t){const n=this.listeners.indexOf(t);n>-1&&this.listeners.splice(n,1)}notifyListeners(){this.listeners.forEach(t=>{try{t(this.getTheme())}catch(n){console.error("主题监听器执行失败:",n)}})}getThemeClasses(){const t=["theme-transition"];return this.currentTheme.isDark&&t.push("dark"),t.join(" ")}isDarkMode(){return this.currentTheme.isDark}getThemeColor(t){const n=this.currentTheme.isDark?{primary:"#e0e0e0",secondary:"#a0a0a0",muted:"#666666",accent:"#0A84FF",background:"#1a1a1a",surface:"#2a2a2a",border:"#404040"}:{primary:"#333333",secondary:"#666666",muted:"#999999",accent:"#007AFF",background:"#ffffff",surface:"#f8f9fa",border:"#e0e0e0"};return n[t]||n.primary}destroy(){this.mediaQuery.removeEventListener("change",this.handleSystemThemeChange),this.listeners=[]}}const br=()=>Zr.getInstance(),xy=({className:e="",showLabel:t=!0})=>{const n=br(),[r,o]=se.useState(n.getTheme());se.useEffect(()=>{const d=h=>{o(h)};return n.addListener(d),()=>{n.removeListener(d)}},[n]);const l=d=>{n.setTheme(d)},a=d=>{switch(d){case"light":return"☀️";case"dark":return"🌙";case"auto":return"🔄";default:return"☀️"}},c=d=>{switch(d){case"light":return"亮色模式";case"dark":return"暗色模式";case"auto":return"跟随系统";default:return"亮色模式"}};return ee("div",{className:`theme-toggle ${e}`,children:[t&&A("label",{className:"block text-sm font-medium mb-2",style:{color:n.getThemeColor("primary")},children:"主题设置"}),A("div",{className:"flex items-center space-x-2",children:["light","dark","auto"].map(d=>ee("button",{onClick:()=>l(d),className:`
              flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
              ${r.mode===d?"ring-2 ring-offset-2":"hover:opacity-80"}
            `,style:{backgroundColor:r.mode===d?n.getThemeColor("accent"):n.getThemeColor("surface"),color:r.mode===d?"#ffffff":n.getThemeColor("primary")},children:[A("span",{className:"text-base",children:a(d)}),A("span",{children:c(d)})]},d))}),r.mode==="auto"&&ee("div",{className:"mt-2 text-xs opacity-75",style:{color:n.getThemeColor("secondary")},children:["当前: ",r.isDark?"暗色":"亮色",r.systemPrefersDark?" (系统偏好暗色)":" (系统偏好亮色)"]})]})},Sy=({className:e=""})=>{const t=br(),[n,r]=se.useState(t.getTheme());return se.useEffect(()=>{const o=l=>{r(l)};return t.addListener(o),()=>{t.removeListener(o)}},[t]),ee("div",{className:`flex items-center space-x-2 text-sm ${e}`,style:{color:t.getThemeColor("secondary")},children:[A("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:n.isDark?"#4A90E2":"#F5A623"}}),A("span",{children:n.mode==="auto"?`自动 (${n.isDark?"暗色":"亮色"})`:n.isDark?"暗色模式":"亮色模式"})]})},_y=({onBackToChat:e})=>{const[t,n]=se.useState(nn),[r,o]=se.useState(!0),[l,a]=se.useState(!1),[c,d]=se.useState(""),[h,v]=se.useState({name:"",prompt:""}),[S,T]=se.useState(null),[k,x]=se.useState({}),[_,I]=se.useState({}),[g,p]=se.useState(!1),f=br(),[P,N]=se.useState(f.isDarkMode());se.useEffect(()=>{const z=E=>{N(E.isDark)};return f.addListener(z),()=>{f.removeListener(z)}},[f]),se.useEffect(()=>{p(navigator.platform.toUpperCase().indexOf("MAC")>=0)},[]),se.useEffect(()=>{(async()=>{try{const E=await as();n(E)}catch(E){console.error("加载设置出错:",E)}finally{o(!1)}})()},[]),se.useEffect(()=>{const z=E=>{E.key==="Escape"&&Se()};return window.addEventListener("keydown",z),()=>{window.removeEventListener("keydown",z)}},[]);const O=z=>{const{name:E,value:L}=z.target;n($=>({...$,[E]:L}))},m=z=>{const{name:E,checked:L}=z.target;n($=>({...$,[E]:L}))},w=(z,E,L)=>{const $={...t.keybindings[z],[E]:L},te=ky(L),ye=`${z}.${E}`;!te.isValid&&L.trim()!==""?x(ne=>({...ne,[ye]:te.error||"快捷键格式无效"})):x(ne=>{const ae={...ne};return delete ae[ye],ae});const B=Cy(L,t.keybindings);B.hasConflict?I(ne=>({...ne,[ye]:B.message||"存在快捷键冲突"})):I(ne=>{const ae={...ne};return delete ae[ye],ae}),n(ne=>({...ne,keybindings:{...ne.keybindings,[z]:$}}))},R=z=>{const{name:E,value:L}=z.target;v($=>({...$,[E]:L}))},j=(z,E)=>{const{name:L,value:$}=z.target;n(te=>{const ye=[...te.customPrompts];return ye[E]={...ye[E],[L]:$},{...te,customPrompts:ye}})},J=()=>{!h.name||!h.prompt||(n(z=>({...z,customPrompts:[...z.customPrompts,{...h}]})),v({name:"",prompt:""}))},ce=z=>{n(E=>({...E,customPrompts:E.customPrompts.filter((L,$)=>$!==z)})),S===z&&T(null)},ue=z=>{T(z)},he=()=>{T(null)},oe=()=>{T(null)},Ee=async()=>{a(!0),d("");try{await wy(t),window.reloadKeybindings&&await window.reloadKeybindings(),d("设置已保存！快捷键将在下次使用时生效。"),setTimeout(()=>d(""),5e3)}catch(z){console.error("保存设置出错:",z),d("保存设置失败，请重试。")}finally{a(!1)}},Se=()=>{window.logseq&&window.logseq.hideMainUI()};return r?A("div",{className:"p-4 text-center",style:{color:f.getThemeColor("primary")},children:"加载中..."}):ee("div",{className:"p-4 max-w-2xl mx-auto theme-transition",style:{backgroundColor:f.getThemeColor("background"),color:f.getThemeColor("primary")},children:[ee("div",{className:"flex justify-between items-center mb-6",children:[A("h1",{className:"text-2xl font-bold",style:{color:f.getThemeColor("primary")},children:"AI 聊天设置"}),ee("div",{className:"flex items-center space-x-2",children:[e&&A("button",{onClick:e,className:"p-2 rounded-full transition-colors",style:{color:f.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:z=>{z.currentTarget.style.backgroundColor=f.getThemeColor("surface")},onMouseLeave:z=>{z.currentTarget.style.backgroundColor="transparent"},title:"返回聊天",children:A("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:A("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),A("button",{onClick:Se,className:"p-2 rounded-full transition-colors",style:{color:f.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:z=>{z.currentTarget.style.backgroundColor=f.getThemeColor("surface")},onMouseLeave:z=>{z.currentTarget.style.backgroundColor="transparent"},title:"关闭设置",children:A("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:A("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),ee("div",{className:"mb-6",children:[A("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"界面设置"}),A("div",{className:"mb-4",children:A(xy,{})}),A("div",{className:"mb-4",children:A(Sy,{})})]}),ee("div",{className:"mb-6",children:[A("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"快捷键设置"}),ee("div",{className:"mb-4",children:[A("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"打开AI聊天"}),ee("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[ee("div",{children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Windows/Linux"}),A("input",{type:"text",value:t.keybindings.openChat.binding,onChange:z=>w("openChat","binding",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:k["openChat.binding"]?"#ef4444":f.getThemeColor("border")},placeholder:"ctrl+g"}),k["openChat.binding"]&&A("p",{className:"text-red-500 text-xs mt-1",children:k["openChat.binding"]}),_["openChat.binding"]&&A("p",{className:"text-yellow-500 text-xs mt-1",children:_["openChat.binding"]})]}),ee("div",{children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Mac"}),A("input",{type:"text",value:t.keybindings.openChat.mac,onChange:z=>w("openChat","mac",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:k["openChat.mac"]?"#ef4444":f.getThemeColor("border")},placeholder:"cmd+g"}),k["openChat.mac"]&&A("p",{className:"text-red-500 text-xs mt-1",children:k["openChat.mac"]}),_["openChat.mac"]&&A("p",{className:"text-yellow-500 text-xs mt-1",children:_["openChat.mac"]})]})]}),ee("div",{className:"mt-2",children:[A("span",{className:"text-sm",style:{color:f.getThemeColor("secondary")},children:"当前快捷键:"}),A("span",{className:"text-sm font-mono ml-1 px-2 py-1 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("accent")},children:ia(t.keybindings.openChat,g)})]})]}),ee("div",{className:"mb-4",children:[A("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"快速AI回复"}),ee("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[ee("div",{children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Windows/Linux"}),A("input",{type:"text",value:t.keybindings.quickReply.binding,onChange:z=>w("quickReply","binding",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:k["quickReply.binding"]?"#ef4444":f.getThemeColor("border")},placeholder:"ctrl+shift+g"}),k["quickReply.binding"]&&A("p",{className:"text-red-500 text-xs mt-1",children:k["quickReply.binding"]}),_["quickReply.binding"]&&A("p",{className:"text-yellow-500 text-xs mt-1",children:_["quickReply.binding"]})]}),ee("div",{children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Mac"}),A("input",{type:"text",value:t.keybindings.quickReply.mac,onChange:z=>w("quickReply","mac",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:k["quickReply.mac"]?"#ef4444":f.getThemeColor("border")},placeholder:"cmd+shift+g"}),k["quickReply.mac"]&&A("p",{className:"text-red-500 text-xs mt-1",children:k["quickReply.mac"]}),_["quickReply.mac"]&&A("p",{className:"text-yellow-500 text-xs mt-1",children:_["quickReply.mac"]})]})]}),ee("div",{className:"mt-2",children:[A("span",{className:"text-sm",style:{color:f.getThemeColor("secondary")},children:"当前快捷键:"}),A("span",{className:"text-sm font-mono ml-1 px-2 py-1 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("accent")},children:ia(t.keybindings.quickReply,g)})]})]}),ee("div",{className:"mb-4",children:[A("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"打开聊天窗口"}),ee("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[ee("div",{children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Windows/Linux"}),A("input",{type:"text",value:t.keybindings.openChatWindow.binding,onChange:z=>w("openChatWindow","binding",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:k["openChatWindow.binding"]?"#ef4444":f.getThemeColor("border")},placeholder:"ctrl+alt+g"}),k["openChatWindow.binding"]&&A("p",{className:"text-red-500 text-xs mt-1",children:k["openChatWindow.binding"]}),_["openChatWindow.binding"]&&A("p",{className:"text-yellow-500 text-xs mt-1",children:_["openChatWindow.binding"]})]}),ee("div",{children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("secondary")},children:"Mac"}),A("input",{type:"text",value:t.keybindings.openChatWindow.mac,onChange:z=>w("openChatWindow","mac",z.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:k["openChatWindow.mac"]?"#ef4444":f.getThemeColor("border")},placeholder:"cmd+alt+g"}),k["openChatWindow.mac"]&&A("p",{className:"text-red-500 text-xs mt-1",children:k["openChatWindow.mac"]}),_["openChatWindow.mac"]&&A("p",{className:"text-yellow-500 text-xs mt-1",children:_["openChatWindow.mac"]})]})]}),ee("div",{className:"mt-2",children:[A("span",{className:"text-sm",style:{color:f.getThemeColor("secondary")},children:"当前快捷键:"}),A("span",{className:"text-sm font-mono ml-1 px-2 py-1 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("accent")},children:ia(t.keybindings.openChatWindow,g)})]}),A("div",{className:"mt-2",children:A("span",{className:"text-xs",style:{color:f.getThemeColor("muted")},children:"独立聊天窗口，不依赖选中内容，支持多轮对话和历史记录"})})]}),ee("div",{className:"p-3 rounded-lg text-sm",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("secondary")},children:[A("p",{className:"mb-2",children:A("strong",{children:"快捷键格式说明："})}),ee("ul",{className:"list-disc list-inside space-y-1",children:[A("li",{children:"使用 + 号连接修饰键和按键，如：ctrl+g"}),A("li",{children:"支持的修饰键：ctrl, cmd, alt, shift, meta"}),A("li",{children:"按键可以是字母、数字或功能键"}),A("li",{children:"修改后需要重新加载插件才能生效"})]})]})]}),ee("div",{className:"mb-6",children:[A("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"API 配置"}),ee("div",{className:"mb-4",children:[A("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"API URL"}),A("input",{type:"text",name:"apiUrl",value:t.apiUrl,onChange:O,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"https://api.openai.com/v1/chat/completions"})]}),ee("div",{className:"mb-4",children:[A("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"API Key"}),A("input",{type:"password",name:"apiKey",value:t.apiKey,onChange:O,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"sk-..."})]}),ee("div",{className:"mb-4",children:[A("label",{className:"block mb-2 font-medium",style:{color:f.getThemeColor("primary")},children:"模型名称"}),A("input",{type:"text",name:"modelName",value:t.modelName,onChange:O,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("surface"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"gpt-3.5-turbo"})]})]}),ee("div",{className:"mb-6",children:[A("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"历史记录设置"}),ee("div",{className:"flex items-center",children:[A("input",{type:"checkbox",id:"enableHistory",name:"enableHistory",checked:t.enableHistory,onChange:m,className:"mr-2",style:{accentColor:f.getThemeColor("accent")}}),A("label",{htmlFor:"enableHistory",className:"font-medium",style:{color:f.getThemeColor("primary")},children:"启用会话历史记录"})]}),A("p",{className:"text-sm mt-1",style:{color:f.getThemeColor("secondary")},children:"启用后，将在同一聊天会话中保存历史对话记录。"})]}),ee("div",{className:"mb-6",children:[A("h2",{className:"text-xl font-semibold mb-3",style:{color:f.getThemeColor("primary")},children:"自定义提示词"}),ee("div",{className:"p-4 rounded mb-4",style:{backgroundColor:f.getThemeColor("surface")},children:[A("h3",{className:"font-medium mb-2",style:{color:f.getThemeColor("primary")},children:"添加新提示词"}),ee("div",{className:"mb-3",children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("primary")},children:"提示词名称"}),A("input",{type:"text",name:"name",value:h.name,onChange:R,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("background"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},placeholder:"提示名称，如：总结、翻译等"})]}),ee("div",{className:"mb-3",children:[A("label",{className:"block mb-1 text-sm",style:{color:f.getThemeColor("primary")},children:"提示词内容"}),A("textarea",{name:"prompt",value:h.prompt,onChange:R,className:"apple-input w-full p-2 rounded",style:{backgroundColor:f.getThemeColor("background"),color:f.getThemeColor("primary"),borderColor:f.getThemeColor("border")},rows:3,placeholder:"提示词内容，如：请总结以下内容的要点："})]}),A("button",{onClick:J,disabled:!h.name||!h.prompt,className:"apple-button px-3 py-1 rounded",style:{backgroundColor:!h.name||!h.prompt?f.getThemeColor("muted"):f.getThemeColor("accent"),color:"#ffffff",opacity:!h.name||!h.prompt?.5:1},children:"添加提示词"})]}),ee("div",{children:[A("h3",{className:"font-medium mb-2",style:{color:f.getThemeColor("primary")},children:"现有提示词"}),t.customPrompts.length===0?A("p",{style:{color:f.getThemeColor("secondary")},children:"暂无自定义提示词"}):A("div",{className:"space-y-3",children:t.customPrompts.map((z,E)=>A("div",{className:"rounded-lg overflow-hidden",style:{border:`1px solid ${f.getThemeColor("border")}`,backgroundColor:f.getThemeColor("surface")},children:S===E?ee("div",{className:"p-3",children:[ee("div",{className:"mb-2",children:[A("label",{className:"block mb-1 text-sm",children:"提示词名称"}),A("input",{type:"text",name:"name",value:z.name,onChange:L=>j(L,E),className:"w-full p-2 border border-gray-300 rounded"})]}),ee("div",{className:"mb-2",children:[A("label",{className:"block mb-1 text-sm",children:"提示词内容"}),A("textarea",{name:"prompt",value:z.prompt,onChange:L=>j(L,E),className:"w-full p-2 border border-gray-300 rounded",rows:3})]}),ee("div",{className:"flex space-x-2",children:[A("button",{onClick:oe,className:"px-2 py-1 bg-green-500 text-white text-sm rounded",children:"完成"}),A("button",{onClick:he,className:"px-2 py-1 bg-gray-300 text-gray-700 text-sm rounded",children:"取消"})]})]}):ee("div",{className:"flex justify-between p-3",children:[ee("div",{children:[A("div",{className:"font-medium",style:{color:f.getThemeColor("primary")},children:z.name}),A("div",{className:"text-sm mt-1 line-clamp-2",style:{color:f.getThemeColor("secondary")},children:z.prompt})]}),ee("div",{className:"flex items-start space-x-2",children:[A("button",{onClick:()=>ue(E),className:"transition-colors",style:{color:f.getThemeColor("accent")},title:"编辑提示词",onMouseEnter:L=>{L.currentTarget.style.opacity="0.7"},onMouseLeave:L=>{L.currentTarget.style.opacity="1"},children:A("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:A("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),A("button",{onClick:()=>ce(E),className:"transition-colors",style:{color:"#EF4444"},title:"删除提示词",onMouseEnter:L=>{L.currentTarget.style.opacity="0.7"},onMouseLeave:L=>{L.currentTarget.style.opacity="1"},children:A("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:A("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},E))})]})]}),ee("div",{className:"mt-6 flex items-center",children:[A("button",{onClick:Ee,disabled:l,className:"apple-button px-4 py-2 rounded font-medium",style:{backgroundColor:l?f.getThemeColor("muted"):"#10B981",color:"#ffffff",opacity:l?.5:1,cursor:l?"not-allowed":"pointer"},children:l?"保存中...":"保存设置"}),c&&A("span",{className:"ml-3",style:{color:"#10B981"},children:c})]})]})};class si extends Error{constructor(t){super(t),this.name="ApiConnectionError"}}function Ey(e,t,n,r){const o=[];n?o.push({role:"system",content:n}):o.push({role:"system",content:"你是一个有用的助手，可以帮助用户处理和分析文本内容。提供简洁、准确的回答。"}),r&&r.length>0&&r.forEach(a=>{o.push({role:a.role,content:a.content})});let l=e;if(t&&t.content&&!e.includes(t.content)){let a="";switch(t.type){case"selection":a=`以下是选中的文本：

`;break;case"block":a=`以下是当前块的内容：

`;break;case"blocks":a=`以下是选中的多个块：

`;break}l=`${a}${t.content}

${e}`}return o.push({role:"user",content:l}),o}function sa(e){try{const t=e.split(`
`).filter(r=>r.trim()!==""&&r.trim()!=="data: [DONE]");let n="";for(const r of t)if(r.startsWith("data: ")){const o=r.replace(/^data: /,"");if(!o||o==="[DONE]")continue;try{const l=JSON.parse(o);l.choices&&l.choices[0]?l.choices[0].delta&&l.choices[0].delta.content?n+=l.choices[0].delta.content:l.choices[0].message&&l.choices[0].message.content&&(n+=l.choices[0].message.content):l.content?n+=l.content:l.text&&(n+=l.text)}catch{console.warn("解析 JSON 失败:",o)}}return n}catch(t){return console.error("解析数据块失败:",t),""}}async function Ty(e,t,n,r,o){try{const l=await as();if(!l.apiUrl||!l.apiKey)throw new si("API URL 或 API Key 未配置");const c={messages:Ey(e,t,r,o),stream:!0,model:l.modelName};l.temperature!==void 0&&(c.temperature=l.temperature),l.maxTokens!==void 0&&l.maxTokens>0&&(c.max_tokens=l.maxTokens);const d=await fetch(l.apiUrl,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l.apiKey}`},body:JSON.stringify(c)});if(!d.ok){let h=`API 请求失败: ${d.status} ${d.statusText}`;try{const v=await d.json();h=`${h}. ${v.error?.message||JSON.stringify(v)}`}catch{}throw new si(h)}if(d.body){const h=d.body.getReader(),v=new TextDecoder("utf-8");let S="",T="",k=Date.now();const x=50;for(;;){const{done:_,value:I}=await h.read();if(_){if(T.length>0){const N=sa(T);N&&(S+=N,n.onChunk(N))}n.onComplete(S);break}const g=v.decode(I,{stream:!0});T+=g;const p=T.split(`

`),f=p.pop()||"";if(p.length>0){const N=sa(p.join(`

`));N&&(S+=N,n.onChunk(N))}T=f;const P=Date.now();if(P-k>=x&&T.includes("data: ")){const N=sa(T);N&&(S+=N,n.onChunk(N),T=""),k=P}}}else throw new si("API 响应中没有正文")}catch(l){l instanceof si?n.onError(l.message):n.onError(`请求失败: ${l instanceof Error?l.message:String(l)}`)}}function Vi(e){if(!e||typeof e!="string")return[];e=e.replace(/^>+\s/gm,"");const t=e.split(/\r?\n/),n=[];let r="",o=!1,l="",a=0;const c=t.filter(h=>h.trim().length>0).map(h=>{const v=h.match(/^(\s+)/);return v?v[1].length:0}).filter(h=>h>0);let d=2;if(c.length>0){const h=c.filter(v=>v>0);h.length>0&&(d=Math.min(...h))}for(let h=0;h<t.length;h++){const v=t[h],S=v.match(/^(\s+)/),T=S?S[1].length:0,k=Math.floor(T/d);if(v.trim().startsWith("```")){if(o){o=!1;const x=a>0?"  ".repeat(a):"";n.push(`${x}\`\`\``)}else{if(r.trim()){const g=a>0?"  ".repeat(a):"";n.push(`${g}${r.trim()}`),r=""}o=!0,l="    ";const x=v.trim().match(/^```(.*)$/),_=x?x[1].trim():"",I=k>0?"  ".repeat(k):"";_?r=`\`\`\` ${_}`:r="```",n.push(`${I}${r}`),r="",a=k}continue}if(o){const x=a>0?"  ".repeat(a):"";n.push(`${x}${l}${v.trimStart()}`);continue}if(v.trim().length>0&&(a=k),v.trim().match(/^(\d+\.|-|\*|\+)\s/)){if(r.trim()){const I=a>0?"  ".repeat(a):"";n.push(`${I}${r.trim()}`),r=""}const x=k>0?"  ".repeat(k):"",_=v.trim().replace(/^(\d+\.|-|\*|\+)\s+/,"");n.push(`${x}${_}`);continue}if(v.trim().match(/^#{1,6}\s/)){if(r.trim()){const _=a>0?"  ".repeat(a):"";n.push(`${_}${r.trim()}`),r=""}const x=k>0?"  ".repeat(k):"";n.push(`${x}${v.trim()}`);continue}if(!v.trim()){if(r.trim()){const x=a>0?"  ".repeat(a):"";n.push(`${x}${r.trim()}`),r=""}continue}r?r+=" "+v.trim():r=v.trim()}if(r.trim()){const h=a>0?"  ".repeat(a):"";n.push(`${h}${r.trim()}`)}return n}const Ny=({context:e,onClose:t,onReplace:n,onInsert:r,onOpenSettings:o})=>{const[l,a]=se.useState(""),[c,d]=se.useState(!1),[h,v]=se.useState(""),[S,T]=se.useState(null),[k,x]=se.useState("custom"),[_,I]=se.useState(""),[g,p]=se.useState(!0),[f,P]=se.useState(0),[N,O]=se.useState([]),[m,w]=se.useState(-1),[R,j]=se.useState(!1),J=se.useRef(null),ce=se.useRef(null),ue=se.useRef(null),he=se.useRef(null),oe=br(),[Ee,Se]=se.useState(oe.isDarkMode());se.useEffect(()=>{const K=Y=>{Se(Y.isDark)};return oe.addListener(K),()=>{oe.removeListener(K)}},[oe]),se.useEffect(()=>{(async()=>{try{const Y=await as();T(Y)}catch(Y){console.error("加载设置出错:",Y),v("无法加载插件设置，请检查配置")}})()},[]),se.useEffect(()=>{k!=="default"&&k!=="custom"&&e?.content&&B(),k==="custom"?(p(!0),setTimeout(()=>{ce.current?.focus()},50)):p(!1)},[k]),se.useEffect(()=>{g&&setTimeout(()=>{ce.current?.focus()},50)},[]),se.useEffect(()=>{const K=Y=>{he.current&&!he.current.contains(Y.target)&&j(!1)};return document.addEventListener("mousedown",K),()=>{document.removeEventListener("mousedown",K)}},[]);const z=K=>{x(K.target.value)},E=K=>{const Y=K.target.value;if(I(Y),S&&Y.trim()!==""){const ve=S.customPrompts.filter(_e=>_e.name.toLowerCase().includes(Y.toLowerCase()));O(ve),j(ve.length>0),w(-1)}else O([]),j(!1)},L=(K,Y)=>{const ve=`使用提示词: ${K}`;I(ve),x("custom"),j(!1),setTimeout(()=>{B(Y)},100)},$=K=>{if(K.key==="Escape"){j(!1);return}if(!R){K.key==="Enter"&&_.trim()&&!c&&(K.preventDefault(),B());return}if(K.key==="ArrowDown")K.preventDefault(),w(Y=>Y<N.length-1?Y+1:Y);else if(K.key==="ArrowUp")K.preventDefault(),w(Y=>Y>0?Y-1:0);else if(K.key==="Enter"&&m>=0){K.preventDefault();const Y=N[m];L(Y.name,Y.prompt)}},te=()=>{_.trim()&&!c&&B()},ye=K=>{K.key==="Enter"&&_.trim()&&!c&&!R&&(K.preventDefault(),B())},B=async K=>{if(!(!e?.content||c)){if(!S||!S.apiKey){v("请先在设置中配置API密钥");return}d(!0),v(""),a(""),P(0),j(!1);try{let Y;if(K)Y=K;else if(k==="custom"){if(!_.trim()){v("请输入自定义提示词"),d(!1);return}Y=_}else if(k!=="default"&&S){const _e=S.customPrompts.find(qe=>qe.name===k);_e&&(Y=_e.prompt)}const ve={onChunk:_e=>{a(qe=>{const dt=qe+_e;return setTimeout(()=>{P(dt.length)},10),dt})},onComplete:_e=>{d(!1),P(_e.length)},onError:_e=>{v(`API 请求失败: ${_e}`),d(!1)}};await Ty(e.content,e,ve,Y)}catch(Y){console.error("发送消息出错:",Y),v("发送消息失败，请重试"),d(!1)}}},ne=()=>l?ee("div",{className:"typewriter-text",children:[l.slice(0,f),c&&f===l.length&&A("span",{className:"blinking-cursor",children:"|"})]}):null,ae=()=>S?ee(um,{children:[A("option",{value:"custom",children:"自定义提示词"}),S.customPrompts.map((K,Y)=>A("option",{value:K.name,children:K.name},Y))]}):null,Re=()=>!R||N.length===0?null:A("div",{ref:he,className:"absolute z-10 left-4 right-16 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto",children:N.map((K,Y)=>A("div",{className:`px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${Y===m?"bg-gray-100 dark:bg-gray-700":""}`,onClick:()=>L(K.name,K.prompt),children:A("div",{className:"font-medium text-gray-900 dark:text-white",children:K.name})},Y))}),Ve=async K=>{try{if(Vi(K).length===0||e.blockUUIDs.length===0){v("无法替换原文：无有效块数据或无原始块 UUID");return}n(K)}catch(Y){console.error("替换原文出错:",Y),v("替换原文失败，请重试")}},it=async K=>{try{if(Vi(K).length===0||e.blockUUIDs.length===0){v("无法插入子块：无有效块数据或无原始块 UUID");return}r(K)}catch(Y){console.error("插入子块出错:",Y),v("插入子块失败，请重试")}};return ee("div",{ref:J,className:`apple-modal rounded-xl shadow-lg w-full flex flex-col ${oe.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",maxHeight:"60vh",border:`1px solid ${oe.getThemeColor("border")}`,backgroundColor:oe.getThemeColor("background"),color:oe.getThemeColor("primary")},children:[ee("div",{className:"p-4 border-b flex justify-between items-center",style:{borderColor:oe.getThemeColor("border")},children:[A("h2",{className:"text-lg font-medium",style:{color:oe.getThemeColor("primary")},children:"AI 聊天"}),ee("div",{className:"flex items-center",children:[A("select",{value:k,onChange:z,className:"apple-select mr-3 text-sm rounded-md border-none h-8 px-3",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary")},children:ae()}),o&&A("button",{onClick:o,className:"h-8 w-8 flex items-center justify-center rounded-full transition-colors mr-2",style:{color:oe.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:K=>{K.currentTarget.style.backgroundColor=oe.getThemeColor("surface")},onMouseLeave:K=>{K.currentTarget.style.backgroundColor="transparent"},title:"打开设置",children:"⚙️"}),A("button",{onClick:t,className:"h-8 w-8 flex items-center justify-center rounded-full transition-colors",style:{color:oe.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:K=>{K.currentTarget.style.backgroundColor=oe.getThemeColor("surface")},onMouseLeave:K=>{K.currentTarget.style.backgroundColor="transparent"},title:"关闭",children:"✕"})]})]}),g&&ee("div",{className:"p-4 border-b relative",style:{borderColor:oe.getThemeColor("border")},children:[ee("div",{className:"flex items-center",children:[A("input",{ref:ce,type:"text",value:_,onChange:E,onKeyPress:ye,onKeyDown:$,placeholder:"输入提示词，支持自动补全",className:"apple-input flex-grow text-sm rounded-lg px-4 py-2",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary"),borderColor:oe.getThemeColor("border")}}),A("button",{onClick:te,disabled:!_.trim()||c,className:"apple-button ml-2 px-4 py-2 rounded-lg text-sm font-medium",style:{backgroundColor:!_.trim()||c?oe.getThemeColor("muted"):oe.getThemeColor("accent"),color:"#ffffff",opacity:!_.trim()||c?.5:1,cursor:!_.trim()||c?"not-allowed":"pointer"},children:"发送"})]}),Re()]}),ee("div",{ref:ue,className:"p-4 flex-grow overflow-y-auto max-h-[40vh] relative",children:[h&&A("div",{className:"mb-4 p-3 rounded-lg text-sm",style:{backgroundColor:"rgba(239, 68, 68, 0.1)",borderColor:"rgba(239, 68, 68, 0.3)",color:Ee?"#FCA5A5":"#DC2626",border:"1px solid"},children:h}),c&&!l&&A("div",{className:"flex justify-center items-center py-8",children:A("div",{className:"apple-spinner w-6 h-6 border-2 rounded-full animate-spin",style:{borderColor:oe.getThemeColor("border"),borderTopColor:oe.getThemeColor("accent")}})}),l&&A("div",{className:"ai-response rounded-lg p-4 text-sm whitespace-pre-wrap relative",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary")},children:ne()})]}),l&&!c&&ee("div",{className:"p-3 border-t flex justify-end space-x-2",style:{borderColor:oe.getThemeColor("border")},children:[A("button",{onClick:()=>Ve(l),className:"apple-button-secondary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:oe.getThemeColor("surface"),color:oe.getThemeColor("primary"),border:`1px solid ${oe.getThemeColor("border")}`},onMouseEnter:K=>{K.currentTarget.style.backgroundColor=oe.getThemeColor("border")},onMouseLeave:K=>{K.currentTarget.style.backgroundColor=oe.getThemeColor("surface")},children:"替换原文"}),A("button",{onClick:()=>it(l),className:"apple-button-primary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:oe.getThemeColor("accent"),color:"#ffffff"},onMouseEnter:K=>{K.currentTarget.style.opacity="0.9"},onMouseLeave:K=>{K.currentTarget.style.opacity="1"},children:"插入子块"})]})]})},by=({onClose:e,onOpenSettings:t})=>{console.log("SimpleChatWindow组件渲染");const n=br();return A("div",{className:`apple-modal rounded-xl shadow-lg w-full flex ${n.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",height:"70vh",maxHeight:"600px",minHeight:"400px",border:`1px solid ${n.getThemeColor("border")}`,backgroundColor:n.getThemeColor("background"),color:n.getThemeColor("primary")},children:ee("div",{className:"flex-1 flex flex-col",children:[ee("div",{className:"p-4 border-b flex items-center justify-between",style:{borderColor:n.getThemeColor("border")},children:[A("div",{className:"flex items-center space-x-2",children:A("h2",{className:"font-semibold",children:"聊天窗口测试"})}),ee("div",{className:"flex items-center space-x-2",children:[t&&A("button",{onClick:t,className:"p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:"设置",children:"⚙️"}),A("button",{onClick:e,className:"p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:"关闭",children:"✕"})]})]}),A("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:A("div",{className:"flex justify-center",children:ee("div",{className:"text-center",children:[A("h3",{className:"text-lg font-semibold mb-2",children:"🎉 聊天窗口测试成功！"}),A("p",{className:"text-sm opacity-70",children:"这是一个简化版的聊天窗口，用于测试基本显示功能。"}),A("p",{className:"text-sm opacity-70 mt-2",children:"如果您能看到这个窗口，说明聊天窗口功能已经正常工作。"})]})})}),ee("div",{className:"p-4 border-t",style:{borderColor:n.getThemeColor("border")},children:[ee("div",{className:"flex space-x-2",children:[A("input",{type:"text",placeholder:"这是测试输入框...",className:"flex-1 p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600",style:{backgroundColor:n.getThemeColor("surface"),borderColor:n.getThemeColor("border"),color:n.getThemeColor("primary")},disabled:!0}),A("button",{disabled:!0,className:"px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed",children:"测试中"})]}),A("p",{className:"text-xs mt-2 opacity-50",children:"这是简化版测试窗口，输入功能暂时禁用"})]})]})})};function Py({chatContext:e,openChatWindow:t}={}){console.log("App组件渲染:",{chatContext:e,openChatWindow:t});const n=se.useRef(null),r=yy(),[o,l]=se.useState(t?"chat_window":"settings");console.log("当前页面状态:",o);const[a,c]=se.useState(e),[d,h]=se.useState(null),[v,S]=se.useState(!1),T=br();se.useEffect(()=>{e?(c(e),l("chat"),setTimeout(k,50)):t&&l("chat_window")},[e,t]),se.useEffect(()=>{S(T.isDarkMode());const N=O=>{S(O.isDark)};return T.addListener(N),()=>{T.removeListener(N)}},[T]);const k=async()=>{if(!(!a||!a.blockUUIDs.length))try{const N=a.blockUUIDs[0],O=document.querySelector(`[blockid="${N}"]`);if(O){const m=O.getBoundingClientRect();h({top:m.bottom,left:m.left,width:m.width})}}catch(N){console.error("获取块位置失败:",N)}},x=async N=>{if(!(!a||!a.blockUUIDs.length))try{const O=Vi(N);for(const R of a.blockUUIDs)await logseq.Editor.removeBlock(R);const m=a.blockUUIDs[0],w=await logseq.Editor.getBlock(m);if(!w){console.error("无法获取块信息");return}if(w.parent){const R=w.parent.id||w.parent.uuid;for(const j of O)await logseq.Editor.insertBlock(R,j,{sibling:!0,before:!1})}else if(w.page){const R=w.page.originalName||w.page.name;for(const j of O)await logseq.Editor.insertBlock(R,j)}logseq.hideMainUI()}catch(O){console.error("替换内容失败:",O)}},_=async N=>{if(!(!a||!a.blockUUIDs.length))try{const O=Vi(N),m=a.blockUUIDs[0],w=I(O);await g(m,w),logseq.hideMainUI()}catch(O){console.error("插入内容失败:",O)}},I=N=>{if(!N.length)return[];const O=[],m=[];for(const w of N){const R=w.match(/^(\s+)/),j=R?Math.floor(R[1].length/2):0,ue={content:w.trimStart().replace(/^(\d+\.|-|\*|\+)\s+/,"")};if(m.length===0)O.push(ue),m.push({node:ue,indent:j});else{for(;m.length>0&&m[m.length-1].indent>=j;)m.pop();if(m.length===0)O.push(ue);else{const he=m[m.length-1].node;he.children||(he.children=[]),he.children.push(ue)}m.push({node:ue,indent:j})}}return O},g=async(N,O)=>{for(const m of O){const w=await logseq.Editor.insertBlock(N,m.content,{sibling:!1});m.children&&m.children.length>0&&w&&await g(w.uuid,m.children)}},p=()=>{logseq.hideMainUI()},f=()=>{l("settings")},P=()=>{a?l("chat"):logseq.hideMainUI()};return r?ee("main",{className:"fixed inset-0 z-50 flex items-center justify-center",style:{backdropFilter:"none",WebkitBackdropFilter:"none",backgroundColor:"transparent"},onClick:N=>{n.current?.contains(N.target)||window.logseq.hideMainUI()},children:[o==="settings"&&A("div",{ref:n,className:`apple-modal rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto pointer-events-auto mx-auto ${T.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",backgroundColor:T.getThemeColor("background"),color:T.getThemeColor("primary")},onClick:N=>N.stopPropagation(),children:A(_y,{onBackToChat:a?P:void 0})}),o==="chat"&&a&&A("div",{ref:n,className:"pointer-events-auto",style:d?{position:"absolute",top:`${d.top+10}px`,left:`${d.left}px`,zIndex:9999,maxWidth:"700px",width:`${Math.max(580,Math.min(d.width*1.5,700))}px`}:{maxWidth:"700px",width:"100%"},onClick:N=>N.stopPropagation(),children:A(Ny,{context:a,onClose:p,onReplace:x,onInsert:_,onOpenSettings:f})}),o==="chat_window"&&A("div",{ref:n,className:"pointer-events-auto",style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:9999,width:"90vw",maxWidth:"1000px",minWidth:"600px"},onClick:N=>N.stopPropagation(),children:A(by,{onClose:p,onOpenSettings:f})})]}):null}async function Oy(){try{const e=await logseq.Editor.getEditingCursorPosition();if(e&&e.pos){const t=await logseq.Editor.getCurrentBlock();if(t){const n=t.content,r=e.pos;if(r.start!==r.end)return n.substring(r.start,r.end)}}return null}catch(e){return console.error("获取选中文本失败:",e),null}}async function Iy(){try{return await logseq.Editor.getSelectedBlocks()||[]}catch(e){return console.error("获取选中块失败:",e),[]}}async function Od(){try{return await logseq.Editor.getCurrentBlock()}catch(e){return console.error("获取当前块失败:",e),null}}async function eu(){const e=await Oy();if(e){const r=await Od();return{type:"selection",content:e,blockUUIDs:r?[r.uuid]:[]}}const t=await Iy();if(t&&t.length>0)return{type:"blocks",content:t.map(o=>o.content).join(`

`),blockUUIDs:t.map(o=>o.uuid)};const n=await Od();return n?{type:"block",content:n.content,blockUUIDs:[n.uuid]}:{type:"none",content:"",blockUUIDs:[]}}const Ly={id:"logseq-plugin-ai-chat",icon:"./logo.svg",title:"AI 聊天助手",description:"在Logseq中使用自定义API进行AI聊天，支持智能上下文感知和完整暗色模式"},jy=(e,...t)=>String.raw(e,...t),Qp=Ly.id,My=Vp(document.getElementById("app"));let on,vn=!1,zo=[];async function Kp(){try{const e=await as();for(const o of zo)try{console.log(`尝试清除命令: ${o}`)}catch(l){console.warn(`清除命令失败: ${o}`,l)}zo=[];const t="open-ai-chat";logseq.App.registerCommandPalette({key:t,label:e.keybindings.openChat.description,keybinding:{binding:e.keybindings.openChat.binding,mac:e.keybindings.openChat.mac}},async()=>{const o=await eu();console.log("获取到的上下文:",o),on=o,vr(),logseq.showMainUI()}),zo.push(t);const n="quick-ai-reply";logseq.App.registerCommandPalette({key:n,label:e.keybindings.quickReply.description,keybinding:{binding:e.keybindings.quickReply.binding,mac:e.keybindings.quickReply.mac}},async()=>{const o=await eu();console.log("快速回复上下文:",o),on=o,vr(),logseq.showMainUI()}),zo.push(n);const r="open-chat-window";logseq.App.registerCommandPalette({key:r,label:e.keybindings.openChatWindow.description,keybinding:{binding:e.keybindings.openChatWindow.binding,mac:e.keybindings.openChatWindow.mac}},async()=>{console.log("打开聊天窗口快捷键被触发"),console.log("当前状态:",{currentContext:on,shouldOpenChatWindow:vn}),on=void 0,vn=!0,console.log("设置状态后:",{currentContext:on,shouldOpenChatWindow:vn}),vr(),logseq.showMainUI(),console.log("UI已显示")}),zo.push(r),console.log("快捷键注册完成:",e.keybindings)}catch(e){console.error("注册快捷键失败:",e)}}async function Ay(){console.log("重新加载快捷键..."),await Kp()}async function Ry(){console.info(`#${Qp}: MAIN`),await vy(),await zy(),logseq.useSettingsSchema([{key:"apiUrl",type:"string",default:"https://api.openai.com/v1/chat/completions",title:"API URL",description:"AI服务的API地址"},{key:"apiKey",type:"string",default:"",title:"API Key",description:"访问AI服务所需的API密钥"},{key:"modelName",type:"string",default:"gpt-3.5-turbo",title:"模型名称",description:"使用的AI模型名称"},{key:"enableHistory",type:"boolean",default:!1,title:"启用历史记录",description:"是否保存聊天历史记录"}]),vr();function e(){return{show(){on=void 0,vn=!1,vr(),logseq.showMainUI()},async openAIChat(){const r=await eu();console.log("获取到的上下文:",r),on=r,vn=!1,vr(),logseq.showMainUI()},openChatWindow(){console.log("打开聊天窗口"),on=void 0,vn=!0,vr(),logseq.showMainUI()}}}const t=e();logseq.provideModel(t),logseq.setMainUIInlineStyle({zIndex:9}),logseq.Editor.registerSlashCommand("AI聊天",async()=>t.openAIChat()),logseq.Editor.registerSlashCommand("聊天窗口",async()=>t.openChatWindow()),await Kp(),window.reloadKeybindings=Ay;const n="ai-chat-plugin-open";logseq.provideStyle(jy`
    .${n} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${n}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `),logseq.App.registerUIItem("toolbar",{key:n,template:`
    <a data-on-click="show">
        <div class="${n}">🤖</div>
    </a>    
`})}function vr(){console.log("渲染应用:",{currentContext:on,shouldOpenChatWindow:vn}),My.render(A($d.StrictMode,{children:A(Py,{chatContext:on,openChatWindow:vn})})),vn=!1}async function zy(){try{const e=br();console.info(`#${Qp}: 主题管理器已初始化`)}catch(e){console.error("主题初始化失败:",e)}}logseq.ready(Ry).catch(console.error);
