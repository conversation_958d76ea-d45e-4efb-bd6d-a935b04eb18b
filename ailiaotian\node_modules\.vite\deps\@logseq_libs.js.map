{"version": 3, "sources": ["../../@logseq/libs/dist/lsplugin.user.js"], "sourcesContent": ["/*! For license information please see lsplugin.user.js.LICENSE.txt */\n!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.LSPluginEntry=t():e.LSPluginEntry=t()}(self,(()=>(()=>{var e={227:(e,t,n)=>{var r=n(155);t.formatArgs=function(t){if(t[0]=(this.useColors?\"%c\":\"\")+this.namespace+(this.useColors?\" %c\":\" \")+t[0]+(this.useColors?\"%c \":\" \")+\"+\"+e.exports.humanize(this.diff),!this.useColors)return;const n=\"color: \"+this.color;t.splice(1,0,n,\"color: inherit\");let r=0,o=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{\"%%\"!==e&&(r++,\"%c\"===e&&(o=r))})),t.splice(o,0,n)},t.save=function(e){try{e?t.storage.setItem(\"debug\",e):t.storage.removeItem(\"debug\")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem(\"debug\")}catch(e){}!e&&void 0!==r&&\"env\"in r&&(e=r.env.DEBUG);return e},t.useColors=function(){if(\"undefined\"!=typeof window&&window.process&&(\"renderer\"===window.process.type||window.process.__nwjs))return!0;if(\"undefined\"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/))return!1;return\"undefined\"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||\"undefined\"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||\"undefined\"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)&&parseInt(RegExp.$1,10)>=31||\"undefined\"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\"))}})(),t.colors=[\"#0000CC\",\"#0000FF\",\"#0033CC\",\"#0033FF\",\"#0066CC\",\"#0066FF\",\"#0099CC\",\"#0099FF\",\"#00CC00\",\"#00CC33\",\"#00CC66\",\"#00CC99\",\"#00CCCC\",\"#00CCFF\",\"#3300CC\",\"#3300FF\",\"#3333CC\",\"#3333FF\",\"#3366CC\",\"#3366FF\",\"#3399CC\",\"#3399FF\",\"#33CC00\",\"#33CC33\",\"#33CC66\",\"#33CC99\",\"#33CCCC\",\"#33CCFF\",\"#6600CC\",\"#6600FF\",\"#6633CC\",\"#6633FF\",\"#66CC00\",\"#66CC33\",\"#9900CC\",\"#9900FF\",\"#9933CC\",\"#9933FF\",\"#99CC00\",\"#99CC33\",\"#CC0000\",\"#CC0033\",\"#CC0066\",\"#CC0099\",\"#CC00CC\",\"#CC00FF\",\"#CC3300\",\"#CC3333\",\"#CC3366\",\"#CC3399\",\"#CC33CC\",\"#CC33FF\",\"#CC6600\",\"#CC6633\",\"#CC9900\",\"#CC9933\",\"#CCCC00\",\"#CCCC33\",\"#FF0000\",\"#FF0033\",\"#FF0066\",\"#FF0099\",\"#FF00CC\",\"#FF00FF\",\"#FF3300\",\"#FF3333\",\"#FF3366\",\"#FF3399\",\"#FF33CC\",\"#FF33FF\",\"#FF6600\",\"#FF6633\",\"#FF9900\",\"#FF9933\",\"#FFCC00\",\"#FFCC33\"],t.log=console.debug||console.log||(()=>{}),e.exports=n(447)(t);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return\"[UnexpectedJSONParseError]: \"+e.message}}},447:(e,t,n)=>{e.exports=function(e){function t(e){let n,o,i,s=null;function a(...e){if(!a.enabled)return;const r=a,o=Number(new Date),i=o-(n||o);r.diff=i,r.prev=n,r.curr=o,n=o,e[0]=t.coerce(e[0]),\"string\"!=typeof e[0]&&e.unshift(\"%O\");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,o)=>{if(\"%%\"===n)return\"%\";s++;const i=t.formatters[o];if(\"function\"==typeof i){const t=e[s];n=i.call(r,t),e.splice(s,1),s--}return n})),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,\"enabled\",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(o!==t.namespaces&&(o=t.namespaces,i=t.enabled(e)),i),set:e=>{s=e}}),\"function\"==typeof t.init&&t.init(a),a}function r(e,n){const r=t(this.namespace+(void 0===n?\":\":n)+e);return r.log=this.log,r}function o(e){return e.toString().substring(2,e.toString().length-2).replace(/\\.\\*\\?$/,\"*\")}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names.map(o),...t.skips.map(o).map((e=>\"-\"+e))].join(\",\");return t.enable(\"\"),e},t.enable=function(e){let n;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=(\"string\"==typeof e?e:\"\").split(/[\\s,]+/),o=r.length;for(n=0;n<o;n++)r[n]&&(\"-\"===(e=r[n].replace(/\\*/g,\".*?\"))[0]?t.skips.push(new RegExp(\"^\"+e.slice(1)+\"$\")):t.names.push(new RegExp(\"^\"+e+\"$\")))},t.enabled=function(e){if(\"*\"===e[e.length-1])return!0;let n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(824),t.destroy=function(){console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},996:e=>{\"use strict\";var t=function(e){return function(e){return!!e&&\"object\"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return\"[object RegExp]\"===t||\"[object Date]\"===t||function(e){return e.$$typeof===n}(e)}(e)};var n=\"function\"==typeof Symbol&&Symbol.for?Symbol.for(\"react.element\"):60103;function r(e,t){return!1!==t.clone&&t.isMergeableObject(e)?c((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function o(e,t,n){return e.concat(t).map((function(e){return r(e,n)}))}function i(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function s(e,t){try{return t in e}catch(e){return!1}}function a(e,t,n){var o={};return n.isMergeableObject(e)&&i(e).forEach((function(t){o[t]=r(e[t],n)})),i(t).forEach((function(i){(function(e,t){return s(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,i)||(s(e,i)&&n.isMergeableObject(t[i])?o[i]=function(e,t){if(!t.customMerge)return c;var n=t.customMerge(e);return\"function\"==typeof n?n:c}(i,n)(e[i],t[i],n):o[i]=r(t[i],n))})),o}function c(e,n,i){(i=i||{}).arrayMerge=i.arrayMerge||o,i.isMergeableObject=i.isMergeableObject||t,i.cloneUnlessOtherwiseSpecified=r;var s=Array.isArray(n);return s===Array.isArray(e)?s?i.arrayMerge(e,n,i):a(e,n,i):r(n,i)}c.all=function(e,t){if(!Array.isArray(e))throw new Error(\"first argument should be an array\");return e.reduce((function(e,n){return c(e,n,t)}),{})};var l=c;e.exports=l},856:function(e){e.exports=function(){\"use strict\";function e(t){return e=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},e(t)}function t(e,n){return t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(e,n)}function n(){if(\"undefined\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\"function\"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function r(e,o,i){return r=n()?Reflect.construct:function(e,n,r){var o=[null];o.push.apply(o,n);var i=new(Function.bind.apply(e,o));return r&&t(i,r.prototype),i},r.apply(null,arguments)}function o(e){return i(e)||s(e)||a(e)||l()}function i(e){if(Array.isArray(e))return c(e)}function s(e){if(\"undefined\"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e[\"@@iterator\"])return Array.from(e)}function a(e,t){if(e){if(\"string\"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return\"Object\"===n&&e.constructor&&(n=e.constructor.name),\"Map\"===n||\"Set\"===n?Array.from(e):\"Arguments\"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}var u=Object.hasOwnProperty,f=Object.setPrototypeOf,p=Object.isFrozen,h=Object.getPrototypeOf,d=Object.getOwnPropertyDescriptor,m=Object.freeze,g=Object.seal,y=Object.create,v=\"undefined\"!=typeof Reflect&&Reflect,b=v.apply,_=v.construct;b||(b=function(e,t,n){return e.apply(t,n)}),m||(m=function(e){return e}),g||(g=function(e){return e}),_||(_=function(e,t){return r(e,o(t))});var w=I(Array.prototype.forEach),x=I(Array.prototype.pop),C=I(Array.prototype.push),S=I(String.prototype.toLowerCase),O=I(String.prototype.match),j=I(String.prototype.replace),A=I(String.prototype.indexOf),E=I(String.prototype.trim),k=I(RegExp.prototype.test),T=M(TypeError);function I(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return b(e,t,r)}}function M(e){return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return _(e,n)}}function F(e,t){f&&f(e,null);for(var n=t.length;n--;){var r=t[n];if(\"string\"==typeof r){var o=S(r);o!==r&&(p(t)||(t[n]=o),r=o)}e[r]=!0}return e}function L(e){var t,n=y(null);for(t in e)b(u,e,[t])&&(n[t]=e[t]);return n}function N(e,t){for(;null!==e;){var n=d(e,t);if(n){if(n.get)return I(n.get);if(\"function\"==typeof n.value)return I(n.value)}e=h(e)}function r(e){return console.warn(\"fallback value for\",e),null}return r}var P=m([\"a\",\"abbr\",\"acronym\",\"address\",\"area\",\"article\",\"aside\",\"audio\",\"b\",\"bdi\",\"bdo\",\"big\",\"blink\",\"blockquote\",\"body\",\"br\",\"button\",\"canvas\",\"caption\",\"center\",\"cite\",\"code\",\"col\",\"colgroup\",\"content\",\"data\",\"datalist\",\"dd\",\"decorator\",\"del\",\"details\",\"dfn\",\"dialog\",\"dir\",\"div\",\"dl\",\"dt\",\"element\",\"em\",\"fieldset\",\"figcaption\",\"figure\",\"font\",\"footer\",\"form\",\"h1\",\"h2\",\"h3\",\"h4\",\"h5\",\"h6\",\"head\",\"header\",\"hgroup\",\"hr\",\"html\",\"i\",\"img\",\"input\",\"ins\",\"kbd\",\"label\",\"legend\",\"li\",\"main\",\"map\",\"mark\",\"marquee\",\"menu\",\"menuitem\",\"meter\",\"nav\",\"nobr\",\"ol\",\"optgroup\",\"option\",\"output\",\"p\",\"picture\",\"pre\",\"progress\",\"q\",\"rp\",\"rt\",\"ruby\",\"s\",\"samp\",\"section\",\"select\",\"shadow\",\"small\",\"source\",\"spacer\",\"span\",\"strike\",\"strong\",\"style\",\"sub\",\"summary\",\"sup\",\"table\",\"tbody\",\"td\",\"template\",\"textarea\",\"tfoot\",\"th\",\"thead\",\"time\",\"tr\",\"track\",\"tt\",\"u\",\"ul\",\"var\",\"video\",\"wbr\"]),R=m([\"svg\",\"a\",\"altglyph\",\"altglyphdef\",\"altglyphitem\",\"animatecolor\",\"animatemotion\",\"animatetransform\",\"circle\",\"clippath\",\"defs\",\"desc\",\"ellipse\",\"filter\",\"font\",\"g\",\"glyph\",\"glyphref\",\"hkern\",\"image\",\"line\",\"lineargradient\",\"marker\",\"mask\",\"metadata\",\"mpath\",\"path\",\"pattern\",\"polygon\",\"polyline\",\"radialgradient\",\"rect\",\"stop\",\"style\",\"switch\",\"symbol\",\"text\",\"textpath\",\"title\",\"tref\",\"tspan\",\"view\",\"vkern\"]),D=m([\"feBlend\",\"feColorMatrix\",\"feComponentTransfer\",\"feComposite\",\"feConvolveMatrix\",\"feDiffuseLighting\",\"feDisplacementMap\",\"feDistantLight\",\"feFlood\",\"feFuncA\",\"feFuncB\",\"feFuncG\",\"feFuncR\",\"feGaussianBlur\",\"feImage\",\"feMerge\",\"feMergeNode\",\"feMorphology\",\"feOffset\",\"fePointLight\",\"feSpecularLighting\",\"feSpotLight\",\"feTile\",\"feTurbulence\"]),U=m([\"animate\",\"color-profile\",\"cursor\",\"discard\",\"fedropshadow\",\"font-face\",\"font-face-format\",\"font-face-name\",\"font-face-src\",\"font-face-uri\",\"foreignobject\",\"hatch\",\"hatchpath\",\"mesh\",\"meshgradient\",\"meshpatch\",\"meshrow\",\"missing-glyph\",\"script\",\"set\",\"solidcolor\",\"unknown\",\"use\"]),$=m([\"math\",\"menclose\",\"merror\",\"mfenced\",\"mfrac\",\"mglyph\",\"mi\",\"mlabeledtr\",\"mmultiscripts\",\"mn\",\"mo\",\"mover\",\"mpadded\",\"mphantom\",\"mroot\",\"mrow\",\"ms\",\"mspace\",\"msqrt\",\"mstyle\",\"msub\",\"msup\",\"msubsup\",\"mtable\",\"mtd\",\"mtext\",\"mtr\",\"munder\",\"munderover\"]),z=m([\"maction\",\"maligngroup\",\"malignmark\",\"mlongdiv\",\"mscarries\",\"mscarry\",\"msgroup\",\"mstack\",\"msline\",\"msrow\",\"semantics\",\"annotation\",\"annotation-xml\",\"mprescripts\",\"none\"]),H=m([\"#text\"]),B=m([\"accept\",\"action\",\"align\",\"alt\",\"autocapitalize\",\"autocomplete\",\"autopictureinpicture\",\"autoplay\",\"background\",\"bgcolor\",\"border\",\"capture\",\"cellpadding\",\"cellspacing\",\"checked\",\"cite\",\"class\",\"clear\",\"color\",\"cols\",\"colspan\",\"controls\",\"controlslist\",\"coords\",\"crossorigin\",\"datetime\",\"decoding\",\"default\",\"dir\",\"disabled\",\"disablepictureinpicture\",\"disableremoteplayback\",\"download\",\"draggable\",\"enctype\",\"enterkeyhint\",\"face\",\"for\",\"headers\",\"height\",\"hidden\",\"high\",\"href\",\"hreflang\",\"id\",\"inputmode\",\"integrity\",\"ismap\",\"kind\",\"label\",\"lang\",\"list\",\"loading\",\"loop\",\"low\",\"max\",\"maxlength\",\"media\",\"method\",\"min\",\"minlength\",\"multiple\",\"muted\",\"name\",\"nonce\",\"noshade\",\"novalidate\",\"nowrap\",\"open\",\"optimum\",\"pattern\",\"placeholder\",\"playsinline\",\"poster\",\"preload\",\"pubdate\",\"radiogroup\",\"readonly\",\"rel\",\"required\",\"rev\",\"reversed\",\"role\",\"rows\",\"rowspan\",\"spellcheck\",\"scope\",\"selected\",\"shape\",\"size\",\"sizes\",\"span\",\"srclang\",\"start\",\"src\",\"srcset\",\"step\",\"style\",\"summary\",\"tabindex\",\"title\",\"translate\",\"type\",\"usemap\",\"valign\",\"value\",\"width\",\"xmlns\",\"slot\"]),q=m([\"accent-height\",\"accumulate\",\"additive\",\"alignment-baseline\",\"ascent\",\"attributename\",\"attributetype\",\"azimuth\",\"basefrequency\",\"baseline-shift\",\"begin\",\"bias\",\"by\",\"class\",\"clip\",\"clippathunits\",\"clip-path\",\"clip-rule\",\"color\",\"color-interpolation\",\"color-interpolation-filters\",\"color-profile\",\"color-rendering\",\"cx\",\"cy\",\"d\",\"dx\",\"dy\",\"diffuseconstant\",\"direction\",\"display\",\"divisor\",\"dur\",\"edgemode\",\"elevation\",\"end\",\"fill\",\"fill-opacity\",\"fill-rule\",\"filter\",\"filterunits\",\"flood-color\",\"flood-opacity\",\"font-family\",\"font-size\",\"font-size-adjust\",\"font-stretch\",\"font-style\",\"font-variant\",\"font-weight\",\"fx\",\"fy\",\"g1\",\"g2\",\"glyph-name\",\"glyphref\",\"gradientunits\",\"gradienttransform\",\"height\",\"href\",\"id\",\"image-rendering\",\"in\",\"in2\",\"k\",\"k1\",\"k2\",\"k3\",\"k4\",\"kerning\",\"keypoints\",\"keysplines\",\"keytimes\",\"lang\",\"lengthadjust\",\"letter-spacing\",\"kernelmatrix\",\"kernelunitlength\",\"lighting-color\",\"local\",\"marker-end\",\"marker-mid\",\"marker-start\",\"markerheight\",\"markerunits\",\"markerwidth\",\"maskcontentunits\",\"maskunits\",\"max\",\"mask\",\"media\",\"method\",\"mode\",\"min\",\"name\",\"numoctaves\",\"offset\",\"operator\",\"opacity\",\"order\",\"orient\",\"orientation\",\"origin\",\"overflow\",\"paint-order\",\"path\",\"pathlength\",\"patterncontentunits\",\"patterntransform\",\"patternunits\",\"points\",\"preservealpha\",\"preserveaspectratio\",\"primitiveunits\",\"r\",\"rx\",\"ry\",\"radius\",\"refx\",\"refy\",\"repeatcount\",\"repeatdur\",\"restart\",\"result\",\"rotate\",\"scale\",\"seed\",\"shape-rendering\",\"specularconstant\",\"specularexponent\",\"spreadmethod\",\"startoffset\",\"stddeviation\",\"stitchtiles\",\"stop-color\",\"stop-opacity\",\"stroke-dasharray\",\"stroke-dashoffset\",\"stroke-linecap\",\"stroke-linejoin\",\"stroke-miterlimit\",\"stroke-opacity\",\"stroke\",\"stroke-width\",\"style\",\"surfacescale\",\"systemlanguage\",\"tabindex\",\"targetx\",\"targety\",\"transform\",\"transform-origin\",\"text-anchor\",\"text-decoration\",\"text-rendering\",\"textlength\",\"type\",\"u1\",\"u2\",\"unicode\",\"values\",\"viewbox\",\"visibility\",\"version\",\"vert-adv-y\",\"vert-origin-x\",\"vert-origin-y\",\"width\",\"word-spacing\",\"wrap\",\"writing-mode\",\"xchannelselector\",\"ychannelselector\",\"x\",\"x1\",\"x2\",\"xmlns\",\"y\",\"y1\",\"y2\",\"z\",\"zoomandpan\"]),W=m([\"accent\",\"accentunder\",\"align\",\"bevelled\",\"close\",\"columnsalign\",\"columnlines\",\"columnspan\",\"denomalign\",\"depth\",\"dir\",\"display\",\"displaystyle\",\"encoding\",\"fence\",\"frame\",\"height\",\"href\",\"id\",\"largeop\",\"length\",\"linethickness\",\"lspace\",\"lquote\",\"mathbackground\",\"mathcolor\",\"mathsize\",\"mathvariant\",\"maxsize\",\"minsize\",\"movablelimits\",\"notation\",\"numalign\",\"open\",\"rowalign\",\"rowlines\",\"rowspacing\",\"rowspan\",\"rspace\",\"rquote\",\"scriptlevel\",\"scriptminsize\",\"scriptsizemultiplier\",\"selection\",\"separator\",\"separators\",\"stretchy\",\"subscriptshift\",\"supscriptshift\",\"symmetric\",\"voffset\",\"width\",\"xmlns\"]),G=m([\"xlink:href\",\"xml:id\",\"xlink:title\",\"xml:space\",\"xmlns:xlink\"]),J=g(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm),Z=g(/<%[\\w\\W]*|[\\w\\W]*%>/gm),V=g(/^data-[\\-\\w.\\u00B7-\\uFFFF]/),K=g(/^aria-[\\-\\w]+$/),Y=g(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i),Q=g(/^(?:\\w+script|data):/i),X=g(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g),ee=g(/^html$/i),te=function(){return\"undefined\"==typeof window?null:window},ne=function(t,n){if(\"object\"!==e(t)||\"function\"!=typeof t.createPolicy)return null;var r=null,o=\"data-tt-policy-suffix\";n.currentScript&&n.currentScript.hasAttribute(o)&&(r=n.currentScript.getAttribute(o));var i=\"dompurify\"+(r?\"#\"+r:\"\");try{return t.createPolicy(i,{createHTML:function(e){return e}})}catch(e){return console.warn(\"TrustedTypes policy \"+i+\" could not be created.\"),null}};function re(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:te(),n=function(e){return re(e)};if(n.version=\"2.3.8\",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;var r=t.document,i=t.document,s=t.DocumentFragment,a=t.HTMLTemplateElement,c=t.Node,l=t.Element,u=t.NodeFilter,f=t.NamedNodeMap,p=void 0===f?t.NamedNodeMap||t.MozNamedAttrMap:f,h=t.HTMLFormElement,d=t.DOMParser,g=t.trustedTypes,y=l.prototype,v=N(y,\"cloneNode\"),b=N(y,\"nextSibling\"),_=N(y,\"childNodes\"),I=N(y,\"parentNode\");if(\"function\"==typeof a){var M=i.createElement(\"template\");M.content&&M.content.ownerDocument&&(i=M.content.ownerDocument)}var oe=ne(g,r),ie=oe?oe.createHTML(\"\"):\"\",se=i,ae=se.implementation,ce=se.createNodeIterator,le=se.createDocumentFragment,ue=se.getElementsByTagName,fe=r.importNode,pe={};try{pe=L(i).documentMode?i.documentMode:{}}catch(e){}var he={};n.isSupported=\"function\"==typeof I&&ae&&void 0!==ae.createHTMLDocument&&9!==pe;var de,me,ge=J,ye=Z,ve=V,be=K,_e=Q,we=X,xe=Y,Ce=null,Se=F({},[].concat(o(P),o(R),o(D),o($),o(H))),Oe=null,je=F({},[].concat(o(B),o(q),o(W),o(G))),Ae=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ee=null,ke=null,Te=!0,Ie=!0,Me=!1,Fe=!1,Le=!1,Ne=!1,Pe=!1,Re=!1,De=!1,Ue=!1,$e=!0,ze=!0,He=!1,Be={},qe=null,We=F({},[\"annotation-xml\",\"audio\",\"colgroup\",\"desc\",\"foreignobject\",\"head\",\"iframe\",\"math\",\"mi\",\"mn\",\"mo\",\"ms\",\"mtext\",\"noembed\",\"noframes\",\"noscript\",\"plaintext\",\"script\",\"style\",\"svg\",\"template\",\"thead\",\"title\",\"video\",\"xmp\"]),Ge=null,Je=F({},[\"audio\",\"video\",\"img\",\"source\",\"image\",\"track\"]),Ze=null,Ve=F({},[\"alt\",\"class\",\"for\",\"id\",\"label\",\"name\",\"pattern\",\"placeholder\",\"role\",\"summary\",\"title\",\"value\",\"style\",\"xmlns\"]),Ke=\"http://www.w3.org/1998/Math/MathML\",Ye=\"http://www.w3.org/2000/svg\",Qe=\"http://www.w3.org/1999/xhtml\",Xe=Qe,et=!1,tt=[\"application/xhtml+xml\",\"text/html\"],nt=\"text/html\",rt=null,ot=i.createElement(\"form\"),it=function(e){return e instanceof RegExp||e instanceof Function},st=function(t){rt&&rt===t||(t&&\"object\"===e(t)||(t={}),t=L(t),Ce=\"ALLOWED_TAGS\"in t?F({},t.ALLOWED_TAGS):Se,Oe=\"ALLOWED_ATTR\"in t?F({},t.ALLOWED_ATTR):je,Ze=\"ADD_URI_SAFE_ATTR\"in t?F(L(Ve),t.ADD_URI_SAFE_ATTR):Ve,Ge=\"ADD_DATA_URI_TAGS\"in t?F(L(Je),t.ADD_DATA_URI_TAGS):Je,qe=\"FORBID_CONTENTS\"in t?F({},t.FORBID_CONTENTS):We,Ee=\"FORBID_TAGS\"in t?F({},t.FORBID_TAGS):{},ke=\"FORBID_ATTR\"in t?F({},t.FORBID_ATTR):{},Be=\"USE_PROFILES\"in t&&t.USE_PROFILES,Te=!1!==t.ALLOW_ARIA_ATTR,Ie=!1!==t.ALLOW_DATA_ATTR,Me=t.ALLOW_UNKNOWN_PROTOCOLS||!1,Fe=t.SAFE_FOR_TEMPLATES||!1,Le=t.WHOLE_DOCUMENT||!1,Re=t.RETURN_DOM||!1,De=t.RETURN_DOM_FRAGMENT||!1,Ue=t.RETURN_TRUSTED_TYPE||!1,Pe=t.FORCE_BODY||!1,$e=!1!==t.SANITIZE_DOM,ze=!1!==t.KEEP_CONTENT,He=t.IN_PLACE||!1,xe=t.ALLOWED_URI_REGEXP||xe,Xe=t.NAMESPACE||Qe,t.CUSTOM_ELEMENT_HANDLING&&it(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ae.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&it(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ae.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&\"boolean\"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ae.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),de=de=-1===tt.indexOf(t.PARSER_MEDIA_TYPE)?nt:t.PARSER_MEDIA_TYPE,me=\"application/xhtml+xml\"===de?function(e){return e}:S,Fe&&(Ie=!1),De&&(Re=!0),Be&&(Ce=F({},o(H)),Oe=[],!0===Be.html&&(F(Ce,P),F(Oe,B)),!0===Be.svg&&(F(Ce,R),F(Oe,q),F(Oe,G)),!0===Be.svgFilters&&(F(Ce,D),F(Oe,q),F(Oe,G)),!0===Be.mathMl&&(F(Ce,$),F(Oe,W),F(Oe,G))),t.ADD_TAGS&&(Ce===Se&&(Ce=L(Ce)),F(Ce,t.ADD_TAGS)),t.ADD_ATTR&&(Oe===je&&(Oe=L(Oe)),F(Oe,t.ADD_ATTR)),t.ADD_URI_SAFE_ATTR&&F(Ze,t.ADD_URI_SAFE_ATTR),t.FORBID_CONTENTS&&(qe===We&&(qe=L(qe)),F(qe,t.FORBID_CONTENTS)),ze&&(Ce[\"#text\"]=!0),Le&&F(Ce,[\"html\",\"head\",\"body\"]),Ce.table&&(F(Ce,[\"tbody\"]),delete Ee.tbody),m&&m(t),rt=t)},at=F({},[\"mi\",\"mo\",\"mn\",\"ms\",\"mtext\"]),ct=F({},[\"foreignobject\",\"desc\",\"title\",\"annotation-xml\"]),lt=F({},[\"title\",\"style\",\"font\",\"a\",\"script\"]),ut=F({},R);F(ut,D),F(ut,U);var ft=F({},$);F(ft,z);var pt=function(e){var t=I(e);t&&t.tagName||(t={namespaceURI:Qe,tagName:\"template\"});var n=S(e.tagName),r=S(t.tagName);return e.namespaceURI===Ye?t.namespaceURI===Qe?\"svg\"===n:t.namespaceURI===Ke?\"svg\"===n&&(\"annotation-xml\"===r||at[r]):Boolean(ut[n]):e.namespaceURI===Ke?t.namespaceURI===Qe?\"math\"===n:t.namespaceURI===Ye?\"math\"===n&&ct[r]:Boolean(ft[n]):e.namespaceURI===Qe&&!(t.namespaceURI===Ye&&!ct[r])&&!(t.namespaceURI===Ke&&!at[r])&&!ft[n]&&(lt[n]||!ut[n])},ht=function(e){C(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=ie}catch(t){e.remove()}}},dt=function(e,t){try{C(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){C(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),\"is\"===e&&!Oe[e])if(Re||De)try{ht(t)}catch(e){}else try{t.setAttribute(e,\"\")}catch(e){}},mt=function(e){var t,n;if(Pe)e=\"<remove></remove>\"+e;else{var r=O(e,/^[\\r\\n\\t ]+/);n=r&&r[0]}\"application/xhtml+xml\"===de&&(e='<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>'+e+\"</body></html>\");var o=oe?oe.createHTML(e):e;if(Xe===Qe)try{t=(new d).parseFromString(o,de)}catch(e){}if(!t||!t.documentElement){t=ae.createDocument(Xe,\"template\",null);try{t.documentElement.innerHTML=et?\"\":o}catch(e){}}var s=t.body||t.documentElement;return e&&n&&s.insertBefore(i.createTextNode(n),s.childNodes[0]||null),Xe===Qe?ue.call(t,Le?\"html\":\"body\")[0]:Le?t.documentElement:s},gt=function(e){return ce.call(e.ownerDocument||e,e,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT,null,!1)},yt=function(e){return e instanceof h&&(\"string\"!=typeof e.nodeName||\"string\"!=typeof e.textContent||\"function\"!=typeof e.removeChild||!(e.attributes instanceof p)||\"function\"!=typeof e.removeAttribute||\"function\"!=typeof e.setAttribute||\"string\"!=typeof e.namespaceURI||\"function\"!=typeof e.insertBefore)},vt=function(t){return\"object\"===e(c)?t instanceof c:t&&\"object\"===e(t)&&\"number\"==typeof t.nodeType&&\"string\"==typeof t.nodeName},bt=function(e,t,r){he[e]&&w(he[e],(function(e){e.call(n,t,r,rt)}))},_t=function(e){var t;if(bt(\"beforeSanitizeElements\",e,null),yt(e))return ht(e),!0;if(k(/[\\u0080-\\uFFFF]/,e.nodeName))return ht(e),!0;var r=me(e.nodeName);if(bt(\"uponSanitizeElement\",e,{tagName:r,allowedTags:Ce}),e.hasChildNodes()&&!vt(e.firstElementChild)&&(!vt(e.content)||!vt(e.content.firstElementChild))&&k(/<[/\\w]/g,e.innerHTML)&&k(/<[/\\w]/g,e.textContent))return ht(e),!0;if(\"select\"===r&&k(/<template/i,e.innerHTML))return ht(e),!0;if(!Ce[r]||Ee[r]){if(!Ee[r]&&xt(r)){if(Ae.tagNameCheck instanceof RegExp&&k(Ae.tagNameCheck,r))return!1;if(Ae.tagNameCheck instanceof Function&&Ae.tagNameCheck(r))return!1}if(ze&&!qe[r]){var o=I(e)||e.parentNode,i=_(e)||e.childNodes;if(i&&o)for(var s=i.length-1;s>=0;--s)o.insertBefore(v(i[s],!0),b(e))}return ht(e),!0}return e instanceof l&&!pt(e)?(ht(e),!0):\"noscript\"!==r&&\"noembed\"!==r||!k(/<\\/no(script|embed)/i,e.innerHTML)?(Fe&&3===e.nodeType&&(t=e.textContent,t=j(t,ge,\" \"),t=j(t,ye,\" \"),e.textContent!==t&&(C(n.removed,{element:e.cloneNode()}),e.textContent=t)),bt(\"afterSanitizeElements\",e,null),!1):(ht(e),!0)},wt=function(e,t,n){if($e&&(\"id\"===t||\"name\"===t)&&(n in i||n in ot))return!1;if(Ie&&!ke[t]&&k(ve,t));else if(Te&&k(be,t));else if(!Oe[t]||ke[t]){if(!(xt(e)&&(Ae.tagNameCheck instanceof RegExp&&k(Ae.tagNameCheck,e)||Ae.tagNameCheck instanceof Function&&Ae.tagNameCheck(e))&&(Ae.attributeNameCheck instanceof RegExp&&k(Ae.attributeNameCheck,t)||Ae.attributeNameCheck instanceof Function&&Ae.attributeNameCheck(t))||\"is\"===t&&Ae.allowCustomizedBuiltInElements&&(Ae.tagNameCheck instanceof RegExp&&k(Ae.tagNameCheck,n)||Ae.tagNameCheck instanceof Function&&Ae.tagNameCheck(n))))return!1}else if(Ze[t]);else if(k(xe,j(n,we,\"\")));else if(\"src\"!==t&&\"xlink:href\"!==t&&\"href\"!==t||\"script\"===e||0!==A(n,\"data:\")||!Ge[e])if(Me&&!k(_e,j(n,we,\"\")));else if(n)return!1;return!0},xt=function(e){return e.indexOf(\"-\")>0},Ct=function(e){var t,r,o,i;bt(\"beforeSanitizeAttributes\",e,null);var s=e.attributes;if(s){var a={attrName:\"\",attrValue:\"\",keepAttr:!0,allowedAttributes:Oe};for(i=s.length;i--;){var c=t=s[i],l=c.name,u=c.namespaceURI;if(r=\"value\"===l?t.value:E(t.value),o=me(l),a.attrName=o,a.attrValue=r,a.keepAttr=!0,a.forceKeepAttr=void 0,bt(\"uponSanitizeAttribute\",e,a),r=a.attrValue,!a.forceKeepAttr&&(dt(l,e),a.keepAttr))if(k(/\\/>/i,r))dt(l,e);else{Fe&&(r=j(r,ge,\" \"),r=j(r,ye,\" \"));var f=me(e.nodeName);if(wt(f,o,r))try{u?e.setAttributeNS(u,l,r):e.setAttribute(l,r),x(n.removed)}catch(e){}}}bt(\"afterSanitizeAttributes\",e,null)}},St=function e(t){var n,r=gt(t);for(bt(\"beforeSanitizeShadowDOM\",t,null);n=r.nextNode();)bt(\"uponSanitizeShadowNode\",n,null),_t(n)||(n.content instanceof s&&e(n.content),Ct(n));bt(\"afterSanitizeShadowDOM\",t,null)};return n.sanitize=function(o,i){var a,l,u,f,p;if((et=!o)&&(o=\"\\x3c!--\\x3e\"),\"string\"!=typeof o&&!vt(o)){if(\"function\"!=typeof o.toString)throw T(\"toString is not a function\");if(\"string\"!=typeof(o=o.toString()))throw T(\"dirty is not a string, aborting\")}if(!n.isSupported){if(\"object\"===e(t.toStaticHTML)||\"function\"==typeof t.toStaticHTML){if(\"string\"==typeof o)return t.toStaticHTML(o);if(vt(o))return t.toStaticHTML(o.outerHTML)}return o}if(Ne||st(i),n.removed=[],\"string\"==typeof o&&(He=!1),He){if(o.nodeName){var h=me(o.nodeName);if(!Ce[h]||Ee[h])throw T(\"root node is forbidden and cannot be sanitized in-place\")}}else if(o instanceof c)1===(l=(a=mt(\"\\x3c!----\\x3e\")).ownerDocument.importNode(o,!0)).nodeType&&\"BODY\"===l.nodeName||\"HTML\"===l.nodeName?a=l:a.appendChild(l);else{if(!Re&&!Fe&&!Le&&-1===o.indexOf(\"<\"))return oe&&Ue?oe.createHTML(o):o;if(!(a=mt(o)))return Re?null:Ue?ie:\"\"}a&&Pe&&ht(a.firstChild);for(var d=gt(He?o:a);u=d.nextNode();)3===u.nodeType&&u===f||_t(u)||(u.content instanceof s&&St(u.content),Ct(u),f=u);if(f=null,He)return o;if(Re){if(De)for(p=le.call(a.ownerDocument);a.firstChild;)p.appendChild(a.firstChild);else p=a;return Oe.shadowroot&&(p=fe.call(r,p,!0)),p}var m=Le?a.outerHTML:a.innerHTML;return Le&&Ce[\"!doctype\"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&k(ee,a.ownerDocument.doctype.name)&&(m=\"<!DOCTYPE \"+a.ownerDocument.doctype.name+\">\\n\"+m),Fe&&(m=j(m,ge,\" \"),m=j(m,ye,\" \")),oe&&Ue?oe.createHTML(m):m},n.setConfig=function(e){st(e),Ne=!0},n.clearConfig=function(){rt=null,Ne=!1},n.isValidAttribute=function(e,t,n){rt||st({});var r=me(e),o=me(t);return wt(r,o,n)},n.addHook=function(e,t){\"function\"==typeof t&&(he[e]=he[e]||[],C(he[e],t))},n.removeHook=function(e){if(he[e])return x(he[e])},n.removeHooks=function(e){he[e]&&(he[e]=[])},n.removeAllHooks=function(){he={}},n}return re()}()},729:e=>{\"use strict\";var t=Object.prototype.hasOwnProperty,n=\"~\";function r(){}function o(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function i(e,t,r,i,s){if(\"function\"!=typeof r)throw new TypeError(\"The listener must be a function\");var a=new o(r,i||e,s),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],a]:e._events[c].push(a):(e._events[c]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function a(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),a.prototype.eventNames=function(){var e,r,o=[];if(0===this._eventsCount)return o;for(r in e=this._events)t.call(e,r)&&o.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},a.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,s=new Array(i);o<i;o++)s[o]=r[o].fn;return s},a.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},a.prototype.emit=function(e,t,r,o,i,s){var a=n?n+e:e;if(!this._events[a])return!1;var c,l,u=this._events[a],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,r),!0;case 4:return u.fn.call(u.context,t,r,o),!0;case 5:return u.fn.call(u.context,t,r,o,i),!0;case 6:return u.fn.call(u.context,t,r,o,i,s),!0}for(l=1,c=new Array(f-1);l<f;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var p,h=u.length;for(l=0;l<h;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),f){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,r);break;case 4:u[l].fn.call(u[l].context,t,r,o);break;default:if(!c)for(p=1,c=new Array(f-1);p<f;p++)c[p-1]=arguments[p];u[l].fn.apply(u[l].context,c)}}return!0},a.prototype.on=function(e,t,n){return i(this,e,t,n,!1)},a.prototype.once=function(e,t,n){return i(this,e,t,n,!0)},a.prototype.removeListener=function(e,t,r,o){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return s(this,i),this;var a=this._events[i];if(a.fn)a.fn!==t||o&&!a.once||r&&a.context!==r||s(this,i);else{for(var c=0,l=[],u=a.length;c<u;c++)(a[c].fn!==t||o&&!a[c].once||r&&a[c].context!==r)&&l.push(a[c]);l.length?this._events[i]=1===l.length?l[0]:l:s(this,i)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new r,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=n,a.EventEmitter=a,e.exports=a},717:e=>{\"function\"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},824:e=>{var t=1e3,n=60*t,r=60*n,o=24*r,i=7*o,s=365.25*o;function a(e,t,n,r){var o=t>=1.5*n;return Math.round(e/n)+\" \"+r+(o?\"s\":\"\")}e.exports=function(e,c){c=c||{};var l=typeof e;if(\"string\"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var a=/^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!a)return;var c=parseFloat(a[1]);switch((a[2]||\"ms\").toLowerCase()){case\"years\":case\"year\":case\"yrs\":case\"yr\":case\"y\":return c*s;case\"weeks\":case\"week\":case\"w\":return c*i;case\"days\":case\"day\":case\"d\":return c*o;case\"hours\":case\"hour\":case\"hrs\":case\"hr\":case\"h\":return c*r;case\"minutes\":case\"minute\":case\"mins\":case\"min\":case\"m\":return c*n;case\"seconds\":case\"second\":case\"secs\":case\"sec\":case\"s\":return c*t;case\"milliseconds\":case\"millisecond\":case\"msecs\":case\"msec\":case\"ms\":return c;default:return}}(e);if(\"number\"===l&&isFinite(e))return c.long?function(e){var i=Math.abs(e);if(i>=o)return a(e,i,o,\"day\");if(i>=r)return a(e,i,r,\"hour\");if(i>=n)return a(e,i,n,\"minute\");if(i>=t)return a(e,i,t,\"second\");return e+\" ms\"}(e):function(e){var i=Math.abs(e);if(i>=o)return Math.round(e/o)+\"d\";if(i>=r)return Math.round(e/r)+\"h\";if(i>=n)return Math.round(e/n)+\"m\";if(i>=t)return Math.round(e/t)+\"s\";return e+\"ms\"}(e);throw new Error(\"val is not a non-empty string or a valid number. val=\"+JSON.stringify(e))}},520:(e,t,n)=>{\"use strict\";var r=n(155),o=\"win32\"===r.platform,i=n(539);function s(e,t){for(var n=[],r=0;r<e.length;r++){var o=e[r];o&&\".\"!==o&&(\"..\"===o?n.length&&\"..\"!==n[n.length-1]?n.pop():t&&n.push(\"..\"):n.push(o))}return n}function a(e){for(var t=e.length-1,n=0;n<=t&&!e[n];n++);for(var r=t;r>=0&&!e[r];r--);return 0===n&&r===t?e:n>r?[]:e.slice(n,r+1)}var c=/^([a-zA-Z]:|[\\\\\\/]{2}[^\\\\\\/]+[\\\\\\/]+[^\\\\\\/]+)?([\\\\\\/])?([\\s\\S]*?)$/,l=/^([\\s\\S]*?)((?:\\.{1,2}|[^\\\\\\/]+?|)(\\.[^.\\/\\\\]*|))(?:[\\\\\\/]*)$/,u={};function f(e){var t=c.exec(e),n=(t[1]||\"\")+(t[2]||\"\"),r=t[3]||\"\",o=l.exec(r);return[n,o[1],o[2],o[3]]}function p(e){var t=c.exec(e),n=t[1]||\"\",r=!!n&&\":\"!==n[1];return{device:n,isUnc:r,isAbsolute:r||!!t[2],tail:t[3]}}function h(e){return\"\\\\\\\\\"+e.replace(/^[\\\\\\/]+/,\"\").replace(/[\\\\\\/]+/g,\"\\\\\")}u.resolve=function(){for(var e=\"\",t=\"\",n=!1,o=arguments.length-1;o>=-1;o--){var a;if(o>=0?a=arguments[o]:e?(a=r.env[\"=\"+e])&&a.substr(0,3).toLowerCase()===e.toLowerCase()+\"\\\\\"||(a=e+\"\\\\\"):a=r.cwd(),!i.isString(a))throw new TypeError(\"Arguments to path.resolve must be strings\");if(a){var c=p(a),l=c.device,u=c.isUnc,f=c.isAbsolute,d=c.tail;if((!l||!e||l.toLowerCase()===e.toLowerCase())&&(e||(e=l),n||(t=d+\"\\\\\"+t,n=f),e&&n))break}}return u&&(e=h(e)),e+(n?\"\\\\\":\"\")+(t=s(t.split(/[\\\\\\/]+/),!n).join(\"\\\\\"))||\".\"},u.normalize=function(e){var t=p(e),n=t.device,r=t.isUnc,o=t.isAbsolute,i=t.tail,a=/[\\\\\\/]$/.test(i);return(i=s(i.split(/[\\\\\\/]+/),!o).join(\"\\\\\"))||o||(i=\".\"),i&&a&&(i+=\"\\\\\"),r&&(n=h(n)),n+(o?\"\\\\\":\"\")+i},u.isAbsolute=function(e){return p(e).isAbsolute},u.join=function(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(!i.isString(n))throw new TypeError(\"Arguments to path.join must be strings\");n&&e.push(n)}var r=e.join(\"\\\\\");return/^[\\\\\\/]{2}[^\\\\\\/]/.test(e[0])||(r=r.replace(/^[\\\\\\/]{2,}/,\"\\\\\")),u.normalize(r)},u.relative=function(e,t){e=u.resolve(e),t=u.resolve(t);for(var n=e.toLowerCase(),r=t.toLowerCase(),o=a(t.split(\"\\\\\")),i=a(n.split(\"\\\\\")),s=a(r.split(\"\\\\\")),c=Math.min(i.length,s.length),l=c,f=0;f<c;f++)if(i[f]!==s[f]){l=f;break}if(0==l)return t;var p=[];for(f=l;f<i.length;f++)p.push(\"..\");return(p=p.concat(o.slice(l))).join(\"\\\\\")},u._makeLong=function(e){if(!i.isString(e))return e;if(!e)return\"\";var t=u.resolve(e);return/^[a-zA-Z]\\:\\\\/.test(t)?\"\\\\\\\\?\\\\\"+t:/^\\\\\\\\[^?.]/.test(t)?\"\\\\\\\\?\\\\UNC\\\\\"+t.substring(2):e},u.dirname=function(e){var t=f(e),n=t[0],r=t[1];return n||r?(r&&(r=r.substr(0,r.length-1)),n+r):\".\"},u.basename=function(e,t){var n=f(e)[2];return t&&n.substr(-1*t.length)===t&&(n=n.substr(0,n.length-t.length)),n},u.extname=function(e){return f(e)[3]},u.format=function(e){if(!i.isObject(e))throw new TypeError(\"Parameter 'pathObject' must be an object, not \"+typeof e);var t=e.root||\"\";if(!i.isString(t))throw new TypeError(\"'pathObject.root' must be a string or undefined, not \"+typeof e.root);var n=e.dir,r=e.base||\"\";return n?n[n.length-1]===u.sep?n+r:n+u.sep+r:r},u.parse=function(e){if(!i.isString(e))throw new TypeError(\"Parameter 'pathString' must be a string, not \"+typeof e);var t=f(e);if(!t||4!==t.length)throw new TypeError(\"Invalid path '\"+e+\"'\");return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}},u.sep=\"\\\\\",u.delimiter=\";\";var d=/^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))(?:[\\/]*)$/,m={};function g(e){return d.exec(e).slice(1)}m.resolve=function(){for(var e=\"\",t=!1,n=arguments.length-1;n>=-1&&!t;n--){var o=n>=0?arguments[n]:r.cwd();if(!i.isString(o))throw new TypeError(\"Arguments to path.resolve must be strings\");o&&(e=o+\"/\"+e,t=\"/\"===o[0])}return(t?\"/\":\"\")+(e=s(e.split(\"/\"),!t).join(\"/\"))||\".\"},m.normalize=function(e){var t=m.isAbsolute(e),n=e&&\"/\"===e[e.length-1];return(e=s(e.split(\"/\"),!t).join(\"/\"))||t||(e=\".\"),e&&n&&(e+=\"/\"),(t?\"/\":\"\")+e},m.isAbsolute=function(e){return\"/\"===e.charAt(0)},m.join=function(){for(var e=\"\",t=0;t<arguments.length;t++){var n=arguments[t];if(!i.isString(n))throw new TypeError(\"Arguments to path.join must be strings\");n&&(e+=e?\"/\"+n:n)}return m.normalize(e)},m.relative=function(e,t){e=m.resolve(e).substr(1),t=m.resolve(t).substr(1);for(var n=a(e.split(\"/\")),r=a(t.split(\"/\")),o=Math.min(n.length,r.length),i=o,s=0;s<o;s++)if(n[s]!==r[s]){i=s;break}var c=[];for(s=i;s<n.length;s++)c.push(\"..\");return(c=c.concat(r.slice(i))).join(\"/\")},m._makeLong=function(e){return e},m.dirname=function(e){var t=g(e),n=t[0],r=t[1];return n||r?(r&&(r=r.substr(0,r.length-1)),n+r):\".\"},m.basename=function(e,t){var n=g(e)[2];return t&&n.substr(-1*t.length)===t&&(n=n.substr(0,n.length-t.length)),n},m.extname=function(e){return g(e)[3]},m.format=function(e){if(!i.isObject(e))throw new TypeError(\"Parameter 'pathObject' must be an object, not \"+typeof e);var t=e.root||\"\";if(!i.isString(t))throw new TypeError(\"'pathObject.root' must be a string or undefined, not \"+typeof e.root);return(e.dir?e.dir+m.sep:\"\")+(e.base||\"\")},m.parse=function(e){if(!i.isString(e))throw new TypeError(\"Parameter 'pathString' must be a string, not \"+typeof e);var t=g(e);if(!t||4!==t.length)throw new TypeError(\"Invalid path '\"+e+\"'\");return t[1]=t[1]||\"\",t[2]=t[2]||\"\",t[3]=t[3]||\"\",{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}},m.sep=\"/\",m.delimiter=\":\",e.exports=o?u:m,e.exports.posix=m,e.exports.win32=u},155:e=>{var t,n,r=e.exports={};function o(){throw new Error(\"setTimeout has not been defined\")}function i(){throw new Error(\"clearTimeout has not been defined\")}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t=\"function\"==typeof setTimeout?setTimeout:o}catch(e){t=o}try{n=\"function\"==typeof clearTimeout?clearTimeout:i}catch(e){n=i}}();var a,c=[],l=!1,u=-1;function f(){l&&a&&(l=!1,a.length?c=a.concat(c):u=-1,c.length&&p())}function p(){if(!l){var e=s(f);l=!0;for(var t=c.length;t;){for(a=c,c=[];++u<t;)a&&a[u].run();u=-1,t=c.length}a=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===i||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function d(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];c.push(new h(e,t)),1!==c.length||l||s(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},r.title=\"browser\",r.browser=!0,r.env={},r.argv=[],r.version=\"\",r.versions={},r.on=d,r.addListener=d,r.once=d,r.off=d,r.removeListener=d,r.removeAllListeners=d,r.emit=d,r.prependListener=d,r.prependOnceListener=d,r.listeners=function(e){return[]},r.binding=function(e){throw new Error(\"process.binding is not supported\")},r.cwd=function(){return\"/\"},r.chdir=function(e){throw new Error(\"process.chdir is not supported\")},r.umask=function(){return 0}},384:e=>{e.exports=function(e){return e&&\"object\"==typeof e&&\"function\"==typeof e.copy&&\"function\"==typeof e.fill&&\"function\"==typeof e.readUInt8}},539:(e,t,n)=>{var r=n(155),o=/%[sdj%]/g;t.format=function(e){if(!y(e)){for(var t=[],n=0;n<arguments.length;n++)t.push(a(arguments[n]));return t.join(\" \")}n=1;for(var r=arguments,i=r.length,s=String(e).replace(o,(function(e){if(\"%%\"===e)return\"%\";if(n>=i)return e;switch(e){case\"%s\":return String(r[n++]);case\"%d\":return Number(r[n++]);case\"%j\":try{return JSON.stringify(r[n++])}catch(e){return\"[Circular]\"}default:return e}})),c=r[n];n<i;c=r[++n])m(c)||!_(c)?s+=\" \"+c:s+=\" \"+a(c);return s},t.deprecate=function(e,o){if(v(n.g.process))return function(){return t.deprecate(e,o).apply(this,arguments)};if(!0===r.noDeprecation)return e;var i=!1;return function(){if(!i){if(r.throwDeprecation)throw new Error(o);r.traceDeprecation?console.trace(o):console.error(o),i=!0}return e.apply(this,arguments)}};var i,s={};function a(e,n){var r={seen:[],stylize:l};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),d(n)?r.showHidden=n:n&&t._extend(r,n),v(r.showHidden)&&(r.showHidden=!1),v(r.depth)&&(r.depth=2),v(r.colors)&&(r.colors=!1),v(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=c),u(r,e,r.depth)}function c(e,t){var n=a.styles[t];return n?\"\u001b[\"+a.colors[n][0]+\"m\"+e+\"\u001b[\"+a.colors[n][1]+\"m\":e}function l(e,t){return e}function u(e,n,r){if(e.customInspect&&n&&C(n.inspect)&&n.inspect!==t.inspect&&(!n.constructor||n.constructor.prototype!==n)){var o=n.inspect(r,e);return y(o)||(o=u(e,o,r)),o}var i=function(e,t){if(v(t))return e.stylize(\"undefined\",\"undefined\");if(y(t)){var n=\"'\"+JSON.stringify(t).replace(/^\"|\"$/g,\"\").replace(/'/g,\"\\\\'\").replace(/\\\\\"/g,'\"')+\"'\";return e.stylize(n,\"string\")}if(g(t))return e.stylize(\"\"+t,\"number\");if(d(t))return e.stylize(\"\"+t,\"boolean\");if(m(t))return e.stylize(\"null\",\"null\")}(e,n);if(i)return i;var s=Object.keys(n),a=function(e){var t={};return e.forEach((function(e,n){t[e]=!0})),t}(s);if(e.showHidden&&(s=Object.getOwnPropertyNames(n)),x(n)&&(s.indexOf(\"message\")>=0||s.indexOf(\"description\")>=0))return f(n);if(0===s.length){if(C(n)){var c=n.name?\": \"+n.name:\"\";return e.stylize(\"[Function\"+c+\"]\",\"special\")}if(b(n))return e.stylize(RegExp.prototype.toString.call(n),\"regexp\");if(w(n))return e.stylize(Date.prototype.toString.call(n),\"date\");if(x(n))return f(n)}var l,_=\"\",S=!1,O=[\"{\",\"}\"];(h(n)&&(S=!0,O=[\"[\",\"]\"]),C(n))&&(_=\" [Function\"+(n.name?\": \"+n.name:\"\")+\"]\");return b(n)&&(_=\" \"+RegExp.prototype.toString.call(n)),w(n)&&(_=\" \"+Date.prototype.toUTCString.call(n)),x(n)&&(_=\" \"+f(n)),0!==s.length||S&&0!=n.length?r<0?b(n)?e.stylize(RegExp.prototype.toString.call(n),\"regexp\"):e.stylize(\"[Object]\",\"special\"):(e.seen.push(n),l=S?function(e,t,n,r,o){for(var i=[],s=0,a=t.length;s<a;++s)E(t,String(s))?i.push(p(e,t,n,r,String(s),!0)):i.push(\"\");return o.forEach((function(o){o.match(/^\\d+$/)||i.push(p(e,t,n,r,o,!0))})),i}(e,n,r,a,s):s.map((function(t){return p(e,n,r,a,t,S)})),e.seen.pop(),function(e,t,n){if(e.reduce((function(e,t){return t.indexOf(\"\\n\")>=0&&0,e+t.replace(/\\u001b\\[\\d\\d?m/g,\"\").length+1}),0)>60)return n[0]+(\"\"===t?\"\":t+\"\\n \")+\" \"+e.join(\",\\n  \")+\" \"+n[1];return n[0]+t+\" \"+e.join(\", \")+\" \"+n[1]}(l,_,O)):O[0]+_+O[1]}function f(e){return\"[\"+Error.prototype.toString.call(e)+\"]\"}function p(e,t,n,r,o,i){var s,a,c;if((c=Object.getOwnPropertyDescriptor(t,o)||{value:t[o]}).get?a=c.set?e.stylize(\"[Getter/Setter]\",\"special\"):e.stylize(\"[Getter]\",\"special\"):c.set&&(a=e.stylize(\"[Setter]\",\"special\")),E(r,o)||(s=\"[\"+o+\"]\"),a||(e.seen.indexOf(c.value)<0?(a=m(n)?u(e,c.value,null):u(e,c.value,n-1)).indexOf(\"\\n\")>-1&&(a=i?a.split(\"\\n\").map((function(e){return\"  \"+e})).join(\"\\n\").substr(2):\"\\n\"+a.split(\"\\n\").map((function(e){return\"   \"+e})).join(\"\\n\")):a=e.stylize(\"[Circular]\",\"special\")),v(s)){if(i&&o.match(/^\\d+$/))return a;(s=JSON.stringify(\"\"+o)).match(/^\"([a-zA-Z_][a-zA-Z_0-9]*)\"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,\"name\")):(s=s.replace(/'/g,\"\\\\'\").replace(/\\\\\"/g,'\"').replace(/(^\"|\"$)/g,\"'\"),s=e.stylize(s,\"string\"))}return s+\": \"+a}function h(e){return Array.isArray(e)}function d(e){return\"boolean\"==typeof e}function m(e){return null===e}function g(e){return\"number\"==typeof e}function y(e){return\"string\"==typeof e}function v(e){return void 0===e}function b(e){return _(e)&&\"[object RegExp]\"===S(e)}function _(e){return\"object\"==typeof e&&null!==e}function w(e){return _(e)&&\"[object Date]\"===S(e)}function x(e){return _(e)&&(\"[object Error]\"===S(e)||e instanceof Error)}function C(e){return\"function\"==typeof e}function S(e){return Object.prototype.toString.call(e)}function O(e){return e<10?\"0\"+e.toString(10):e.toString(10)}t.debuglog=function(e){if(v(i)&&(i=r.env.NODE_DEBUG||\"\"),e=e.toUpperCase(),!s[e])if(new RegExp(\"\\\\b\"+e+\"\\\\b\",\"i\").test(i)){var n=r.pid;s[e]=function(){var r=t.format.apply(t,arguments);console.error(\"%s %d: %s\",e,n,r)}}else s[e]=function(){};return s[e]},t.inspect=a,a.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},a.styles={special:\"cyan\",number:\"yellow\",boolean:\"yellow\",undefined:\"grey\",null:\"bold\",string:\"green\",date:\"magenta\",regexp:\"red\"},t.isArray=h,t.isBoolean=d,t.isNull=m,t.isNullOrUndefined=function(e){return null==e},t.isNumber=g,t.isString=y,t.isSymbol=function(e){return\"symbol\"==typeof e},t.isUndefined=v,t.isRegExp=b,t.isObject=_,t.isDate=w,t.isError=x,t.isFunction=C,t.isPrimitive=function(e){return null===e||\"boolean\"==typeof e||\"number\"==typeof e||\"string\"==typeof e||\"symbol\"==typeof e||void 0===e},t.isBuffer=n(384);var j=[\"Jan\",\"Feb\",\"Mar\",\"Apr\",\"May\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Oct\",\"Nov\",\"Dec\"];function A(){var e=new Date,t=[O(e.getHours()),O(e.getMinutes()),O(e.getSeconds())].join(\":\");return[e.getDate(),j[e.getMonth()],t].join(\" \")}function E(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.log=function(){console.log(\"%s - %s\",A(),t.format.apply(t,arguments))},t.inherits=n(717),t._extend=function(e,t){if(!t||!_(t))return e;for(var n=Object.keys(t),r=n.length;r--;)e[n[r]]=t[n[r]];return e}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if(\"object\"==typeof globalThis)return globalThis;try{return this||new Function(\"return this\")()}catch(e){if(\"object\"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})};var r={};return(()=>{\"use strict\";n.r(r),n.d(r,{LSPluginUser:()=>Nr,setupPluginUserInstance:()=>Pr});var e=n(520),t=(n(856),n(996)),o=n.n(t);var i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};Object.create;Object.create;function s(e){return e.toLowerCase()}var a=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],c=/[^A-Z0-9]+/gi;function l(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function u(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,r=void 0===n?a:n,o=t.stripRegexp,i=void 0===o?c:o,u=t.transform,f=void 0===u?s:u,p=t.delimiter,h=void 0===p?\" \":p,d=l(l(e,r,\"$1\\0$2\"),i,\"\\0\"),m=0,g=d.length;\"\\0\"===d.charAt(m);)m++;for(;\"\\0\"===d.charAt(g-1);)g--;return d.slice(m,g).split(\"\\0\").map(f).join(h)}(e,i({delimiter:\".\"},t))}var f=n(729),p=n.n(f);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const d=\"win32\"===navigator.platform.toLowerCase()?e.win32:e.posix;const m=function(e,t){return void 0===t&&(t={}),u(e,i({delimiter:\"_\"},t))};class g extends(p()){constructor(e,t){super(),h(this,\"_tag\",void 0),h(this,\"_opts\",void 0),h(this,\"_logs\",[]),this._tag=e,this._opts=t}write(e,t,n){var r;null!=t&&t.length&&!0===t[t.length-1]&&(n=!0,t.pop());const o=t.reduce(((e,t)=>(t&&t instanceof Error?e+=`${t.message} ${t.stack}`:e+=t.toString(),e)),`[${this._tag}][${(new Date).toLocaleTimeString()}] `);var i;(this._logs.push([e,o]),n||null!==(r=this._opts)&&void 0!==r&&r.console)&&(null===(i=console)||void 0===i||i[\"ERROR\"===e?\"error\":\"debug\"](`${e}: ${o}`));this.emit(\"change\")}clear(){this._logs=[],this.emit(\"change\")}info(...e){this.write(\"INFO\",e)}error(...e){this.write(\"ERROR\",e)}warn(...e){this.write(\"WARN\",e)}setTag(e){this._tag=e}toJSON(){return this._logs}}function y(e,...t){try{const n=new URL(e);if(!n.origin)throw new Error(null);const r=d.join(e.substr(n.origin.length),...t);return n.origin+r}catch(n){return d.join(e,...t)}}function v(e,t){let n,r,o=!1;const i=t=>n=>{e&&clearTimeout(e),t(n),o=!0},s=new Promise(((o,s)=>{n=i(o),r=i(s),e&&(e=setTimeout((()=>r(new Error(`[deferred timeout] ${t}`))),e))}));return{created:Date.now(),setTag:e=>t=e,resolve:n,reject:r,promise:s,get settled(){return o}}}const b=new Map;window.__injectedUIEffects=b;var _=n(227),w=n.n(_);function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const C=\"application/x-postmate-v1+json\";let S=0;const O={handshake:1,\"handshake-reply\":1,call:1,emit:1,reply:1,request:1},j=(e,t)=>(\"string\"!=typeof t||e.origin===t)&&(!!e.data&&((\"object\"!=typeof e.data||\"postmate\"in e.data)&&(e.data.type===C&&!!O[e.data.postmate])));class A{constructor(e){x(this,\"parent\",void 0),x(this,\"frame\",void 0),x(this,\"child\",void 0),x(this,\"events\",{}),x(this,\"childOrigin\",void 0),x(this,\"listener\",void 0),this.parent=e.parent,this.frame=e.frame,this.child=e.child,this.childOrigin=e.childOrigin,this.listener=e=>{if(!j(e,this.childOrigin))return!1;const{data:t,name:n}=((e||{}).data||{}).value||{};\"emit\"===e.data.postmate&&n in this.events&&this.events[n].forEach((e=>{e.call(this,t)}))},this.parent.addEventListener(\"message\",this.listener,!1)}get(e,...t){return new Promise(((n,r)=>{const o=++S,i=e=>{e.data.uid===o&&\"reply\"===e.data.postmate&&(this.parent.removeEventListener(\"message\",i,!1),e.data.error?r(e.data.error):n(e.data.value))};this.parent.addEventListener(\"message\",i,!1),this.child.postMessage({postmate:\"request\",type:C,property:e,args:t,uid:o},this.childOrigin)}))}call(e,t){this.child.postMessage({postmate:\"call\",type:C,property:e,data:t},this.childOrigin)}on(e,t){this.events[e]||(this.events[e]=[]),this.events[e].push(t)}destroy(){window.removeEventListener(\"message\",this.listener,!1),this.frame.parentNode.removeChild(this.frame)}}class E{constructor(e){x(this,\"model\",void 0),x(this,\"parent\",void 0),x(this,\"parentOrigin\",void 0),x(this,\"child\",void 0),this.model=e.model,this.parent=e.parent,this.parentOrigin=e.parentOrigin,this.child=e.child,this.child.addEventListener(\"message\",(e=>{if(!j(e,this.parentOrigin))return;const{property:t,uid:n,data:r,args:o}=e.data;\"call\"!==e.data.postmate?((e,t,n)=>{const r=\"function\"==typeof e[t]?e[t].apply(null,n):e[t];return Promise.resolve(r)})(this.model,t,o).then((r=>{e.source.postMessage({property:t,postmate:\"reply\",type:C,uid:n,value:r},e.origin)})).catch((r=>{e.source.postMessage({property:t,postmate:\"reply\",type:C,uid:n,error:r},e.origin)})):t in this.model&&\"function\"==typeof this.model[t]&&this.model[t](r)}))}emit(e,t){this.parent.postMessage({postmate:\"emit\",type:C,value:{name:e,data:t}},this.parentOrigin)}}class k{constructor(e){x(this,\"container\",void 0),x(this,\"parent\",void 0),x(this,\"frame\",void 0),x(this,\"child\",void 0),x(this,\"childOrigin\",void 0),x(this,\"url\",void 0),x(this,\"model\",void 0),this.container=e.container,this.url=e.url,this.parent=window,this.frame=document.createElement(\"iframe\"),e.id&&(this.frame.id=e.id),e.name&&(this.frame.name=e.name),this.frame.classList.add.apply(this.frame.classList,e.classListArray||[]),this.container.appendChild(this.frame),this.child=this.frame.contentWindow,this.model=e.model||{}}sendHandshake(e){const t=(e=>{const t=document.createElement(\"a\");t.href=e;const n=t.protocol.length>4?t.protocol:window.location.protocol,r=t.host.length?\"80\"===t.port||\"443\"===t.port?t.hostname:t.host:window.location.host;return t.origin||`${n}//${r}`})(e=e||this.url);let n,r=0;return new Promise(((o,i)=>{const s=e=>!!j(e,t)&&(\"handshake-reply\"===e.data.postmate?(clearInterval(n),this.parent.removeEventListener(\"message\",s,!1),this.childOrigin=e.origin,o(new A(this))):i(\"Failed handshake\"));this.parent.addEventListener(\"message\",s,!1);const a=()=>{r++,this.child.postMessage({postmate:\"handshake\",type:C,model:this.model},t),5===r&&clearInterval(n)};this.frame.addEventListener(\"load\",(()=>{a(),n=setInterval(a,500)})),this.frame.src=e}))}destroy(){this.frame.parentNode.removeChild(this.frame)}}x(k,\"debug\",!1),x(k,\"Model\",void 0);class T{constructor(e){x(this,\"child\",void 0),x(this,\"model\",void 0),x(this,\"parent\",void 0),x(this,\"parentOrigin\",void 0),this.child=window,this.model=e,this.parent=this.child.parent}sendHandshakeReply(){return new Promise(((e,t)=>{const n=r=>{if(r.data.postmate){if(\"handshake\"===r.data.postmate){0,this.child.removeEventListener(\"message\",n,!1),r.source.postMessage({postmate:\"handshake-reply\",type:C},r.origin),this.parentOrigin=r.origin;const t=r.data.model;return t&&Object.keys(t).forEach((e=>{this.model[e]=t[e]})),e(new E(this))}return t(\"Handshake Reply Failed\")}};this.child.addEventListener(\"message\",n,!1)}))}}function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const{importHTML:M,createSandboxContainer:F}=window.QSandbox||{};function L(e,t){return e.startsWith(\"http\")?fetch(e,t):(e=e.replace(\"file://\",\"\"),new Promise((async(t,n)=>{try{const n=await window.apis.doAction([\"readFile\",e]);t({text:()=>n})}catch(e){console.error(e),n(e)}})))}class N extends(p()){constructor(e){super(),I(this,\"_pluginLocal\",void 0),I(this,\"_frame\",void 0),I(this,\"_root\",void 0),I(this,\"_loaded\",!1),I(this,\"_unmountFns\",[]),this._pluginLocal=e,e._dispose((()=>{this._unmount()}))}async load(){const{name:e,entry:t}=this._pluginLocal.options;if(this.loaded||!t)return;const{template:n,execScripts:r}=await M(t,{fetch:L});this._mount(n,document.body);const o=F(e,{elementGetter:()=>{var e;return null===(e=this._root)||void 0===e?void 0:e.firstChild}}).instance.proxy;o.__shadow_mode__=!0,o.LSPluginLocal=this._pluginLocal,o.LSPluginShadow=this,o.LSPluginUser=o.logseq=new Nr(this._pluginLocal.toJSON(),this._pluginLocal.caller);const i=await r(o,!0);this._unmountFns.push(i.unmount),this._loaded=!0}_mount(e,t){const n=this._frame=document.createElement(\"div\");n.classList.add(\"lsp-shadow-sandbox\"),n.id=this._pluginLocal.id,this._root=n.attachShadow({mode:\"open\"}),this._root.innerHTML=`<div>${e}</div>`,t.appendChild(n),this.emit(\"mounted\")}_unmount(){for(const e of this._unmountFns)e&&e.call(null)}destroy(){var e,t;null===(e=this.frame)||void 0===e||null===(t=e.parentNode)||void 0===t||t.removeChild(this.frame)}get loaded(){return this._loaded}get document(){var e;return null===(e=this._root)||void 0===e?void 0:e.firstChild}get frame(){return this._frame}}function P(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const R=w()(\"LSPlugin:caller\"),D=\"#await#response#\",U=\"#lspmsg#\",$=\"#lspmsg#error#\",z=e=>`#lspmsg#${e}`;class H extends(p()){constructor(e){super(),P(this,\"_pluginLocal\",void 0),P(this,\"_connected\",!1),P(this,\"_parent\",void 0),P(this,\"_child\",void 0),P(this,\"_shadow\",void 0),P(this,\"_status\",void 0),P(this,\"_userModel\",{}),P(this,\"_call\",void 0),P(this,\"_callUserModel\",void 0),P(this,\"_debugTag\",\"\"),this._pluginLocal=e,e&&(this._debugTag=e.debugTag)}async connectToChild(){if(this._connected)return;const{shadow:e}=this._pluginLocal;e?await this._setupShadowSandbox():await this._setupIframeSandbox()}async connectToParent(e={}){if(this._connected)return;const t=this,n=null!=this._pluginLocal;let r=0,o=0;const i=new Map,s=v(6e4),a=this._extendUserModel({\"#lspmsg#ready#\":async e=>{a[z(null==e?void 0:e.pid)]=({type:e,payload:n})=>{R(`[host (_call) -> *user] ${this._debugTag}`,e,n),t.emit(e,n)},await s.resolve()},\"#lspmsg#beforeunload#\":async e=>{const n=v(1e4);t.emit(\"beforeunload\",Object.assign({actor:n},e)),await n.promise},\"#lspmsg#settings#\":async({type:e,payload:n})=>{t.emit(\"settings:changed\",n)},[U]:async({ns:e,type:n,payload:r})=>{R(`[host (async) -> *user] ${this._debugTag} ns=${e} type=${n}`,r),e&&e.startsWith(\"hook\")?t.emit(`${e}:${n}`,r):t.emit(n,r)},\"#lspmsg#reply#\":({_sync:e,result:t})=>{if(R(`[sync host -> *user] #${e}`,t),i.has(e)){const n=i.get(e);n&&(null!=t&&t.hasOwnProperty($)?n.reject(t[$]):n.resolve(t),i.delete(e))}},...e});var c;if(n)return await s.promise,JSON.parse(JSON.stringify(null===(c=this._pluginLocal)||void 0===c?void 0:c.toJSON()));const l=new T(a).sendHandshakeReply();return this._status=\"pending\",await l.then((e=>{this._child=e,this._connected=!0,this._call=async(t,n={},r)=>{if(r){const e=++o;i.set(e,r),n._sync=e,r.setTag(`async call #${e}`),R(`async call #${e}`)}return e.emit(z(a.baseInfo.id),{type:t,payload:n}),null==r?void 0:r.promise},this._callUserModel=async(e,t)=>{try{a[e](t)}catch(t){R(`[model method] #${e} not existed`)}},r=setInterval((()=>{if(i.size>100)for(const[e,t]of i)t.settled&&i.delete(e)}),18e5)})).finally((()=>{this._status=void 0})),await s.promise,a.baseInfo}async call(e,t={}){var n;return null===(n=this._call)||void 0===n?void 0:n.call(this,e,t)}async callAsync(e,t={}){var n;const r=v(1e4);return null===(n=this._call)||void 0===n?void 0:n.call(this,e,t,r)}async callUserModel(e,...t){var n;return null===(n=this._callUserModel)||void 0===n?void 0:n.apply(this,[e,...t])}async callUserModelAsync(e,...t){var n;return e=`${D}${e}`,null===(n=this._callUserModel)||void 0===n?void 0:n.apply(this,[e,...t])}async _setupIframeSandbox(){const e=this._pluginLocal,t=e.id,n=`${t}_lsp_main`,r=new URL(e.options.entry);r.searchParams.set(\"__v__\",e.options.version);const o=document.querySelector(`#${n}`);o&&o.parentElement.removeChild(o);const i=document.createElement(\"div\");i.classList.add(\"lsp-iframe-sandbox-container\"),i.id=n,i.dataset.pid=t;try{var s;const e=null===(s=await this._pluginLocal._loadLayoutsData())||void 0===s?void 0:s.$$0;if(e){i.dataset.inited_layout=\"true\";let{width:t,height:n,left:r,top:o,vw:s,vh:a}=e;r=Math.max(r,0),r=\"number\"==typeof s?`${Math.min(100*r/s,99)}%`:`${r}px`,o=Math.max(o,45),o=\"number\"==typeof a?`${Math.min(100*o/a,99)}%`:`${o}px`,Object.assign(i.style,{width:t+\"px\",height:n+\"px\",left:r,top:o})}}catch(e){console.error(\"[Restore Layout Error]\",e)}document.body.appendChild(i);const a=new k({id:t+\"_iframe\",container:i,url:r.href,classListArray:[\"lsp-iframe-sandbox\"],model:{baseInfo:JSON.parse(JSON.stringify(e.toJSON()))}});let c,l=a.sendHandshake();return this._status=\"pending\",new Promise(((t,n)=>{c=setTimeout((()=>{n(new Error(\"handshake Timeout\")),a.destroy()}),4e3),l.then((n=>{this._parent=n,this._connected=!0,this.emit(\"connected\"),n.on(z(e.id),(({type:e,payload:t})=>{var n,r;R(\"[user -> *host] \",e,t),null===(n=this._pluginLocal)||void 0===n||n.emit(e,t||{}),null===(r=this._pluginLocal)||void 0===r||r.caller.emit(e,t||{})})),this._call=async(...t)=>{await n.call(z(e.id),{type:t[0],payload:Object.assign(t[1]||{},{$$pid:e.id})})},this._callUserModel=async(e,...t)=>{if(e.startsWith(D))return await n.get(e.replace(D,\"\"),...t);n.call(e,null==t?void 0:t[0])},t(null)})).catch((e=>{n(e)})).finally((()=>{clearTimeout(c)}))})).catch((e=>{throw R(\"[iframe sandbox] error\",e),e})).finally((()=>{this._status=void 0}))}async _setupShadowSandbox(){const e=this._pluginLocal,t=this._shadow=new N(e);try{this._status=\"pending\",await t.load(),this._connected=!0,this.emit(\"connected\"),this._call=async(t,n={},r)=>{var o;return r&&(n.actor=r),null===(o=this._pluginLocal)||void 0===o||o.emit(t,Object.assign(n,{$$pid:e.id})),null==r?void 0:r.promise},this._callUserModel=async(...e)=>{var t;let n=e[0];null!==(t=n)&&void 0!==t&&t.startsWith(D)&&(n=n.replace(D,\"\"));const r=e[1]||{},o=this._userModel[n];\"function\"==typeof o&&await o.call(null,r)}}catch(e){throw R(\"[shadow sandbox] error\",e),e}finally{this._status=void 0}}_extendUserModel(e){return Object.assign(this._userModel,e)}_getSandboxIframeContainer(){var e;return null===(e=this._parent)||void 0===e?void 0:e.frame.parentNode}_getSandboxShadowContainer(){var e;return null===(e=this._shadow)||void 0===e?void 0:e.frame.parentNode}_getSandboxIframeRoot(){var e;return null===(e=this._parent)||void 0===e?void 0:e.frame}_getSandboxShadowRoot(){var e;return null===(e=this._shadow)||void 0===e?void 0:e.frame}set debugTag(e){this._debugTag=e}async destroy(){var e;let t=null;this._parent&&(t=this._getSandboxIframeContainer(),await this._parent.destroy()),this._shadow&&(t=this._getSandboxShadowContainer(),this._shadow.destroy()),null===(e=t)||void 0===e||e.parentNode.removeChild(t)}}function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class q{constructor(e,t){B(this,\"ctx\",void 0),B(this,\"opts\",void 0),this.ctx=e,this.opts=t}get ctxId(){return this.ctx.baseInfo.id}setItem(e,t){var n;return this.ctx.caller.callAsync(\"api:call\",{method:\"write-plugin-storage-file\",args:[this.ctxId,e,t,null===(n=this.opts)||void 0===n?void 0:n.assets]})}getItem(e){var t;return this.ctx.caller.callAsync(\"api:call\",{method:\"read-plugin-storage-file\",args:[this.ctxId,e,null===(t=this.opts)||void 0===t?void 0:t.assets]})}removeItem(e){var t;return this.ctx.caller.call(\"api:call\",{method:\"unlink-plugin-storage-file\",args:[this.ctxId,e,null===(t=this.opts)||void 0===t?void 0:t.assets]})}allKeys(){var e;return this.ctx.caller.callAsync(\"api:call\",{method:\"list-plugin-storage-files\",args:[this.ctxId,null===(e=this.opts)||void 0===e?void 0:e.assets]})}clear(){var e;return this.ctx.caller.call(\"api:call\",{method:\"clear-plugin-storage-files\",args:[this.ctxId,null===(e=this.opts)||void 0===e?void 0:e.assets]})}hasItem(e){var t;return this.ctx.caller.callAsync(\"api:call\",{method:\"exist-plugin-storage-file\",args:[this.ctxId,e,null===(t=this.opts)||void 0===t?void 0:t.assets]})}}class W{constructor(e){var t,n,r;r=void 0,(n=\"ctx\")in(t=this)?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,this.ctx=e}get React(){return this.ensureHostScope().React}get ReactDOM(){return this.ensureHostScope().ReactDOM}get pluginLocal(){return this.ensureHostScope().LSPluginCore.ensurePlugin(this.ctx.baseInfo.id)}invokeExperMethod(e,...t){var n,r;const o=this.ensureHostScope();return e=null===(n=m(e))||void 0===n?void 0:n.toLowerCase(),null===(r=o.logseq.api[\"exper_\"+e])||void 0===r?void 0:r.apply(o,t)}async loadScripts(...e){(e=e.map((e=>null!=e&&e.startsWith(\"http\")?e:this.ctx.resolveResourceFullUrl(e)))).unshift(this.ctx.baseInfo.id),await this.invokeExperMethod(\"loadScripts\",...e)}registerFencedCodeRenderer(e,t){return this.ensureHostScope().logseq.api.exper_register_fenced_code_renderer(this.ctx.baseInfo.id,e,t)}registerExtensionsEnhancer(e,t){const n=this.ensureHostScope();if(\"katex\"===e)n.katex&&t(n.katex).catch(console.error);return n.logseq.api.exper_register_extensions_enhancer(this.ctx.baseInfo.id,e,t)}ensureHostScope(){if(window===top)throw new Error(\"Can not access host scope!\");return top}}function G(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const J=e=>`task_callback_${e}`;class Z{constructor(e,t,n={}){G(this,\"_client\",void 0),G(this,\"_requestId\",void 0),G(this,\"_requestOptions\",void 0),G(this,\"_promise\",void 0),G(this,\"_aborted\",!1),this._client=e,this._requestId=t,this._requestOptions=n,this._promise=new Promise(((e,t)=>{if(!this._requestId)return t(null);this._client.once(J(this._requestId),(n=>{n&&n instanceof Error?t(n):e(n)}))}));const{success:r,fail:o,final:i}=this._requestOptions;this._promise.then((e=>{null==r||r(e)})).catch((e=>{null==o||o(e)})).finally((()=>{null==i||i()}))}abort(){this._requestOptions.abortable&&!this._aborted&&(this._client.ctx._execCallableAPI(\"http_request_abort\",this._requestId),this._aborted=!0)}get promise(){return this._promise}get client(){return this._client}get requestId(){return this._requestId}}class V extends f.EventEmitter{constructor(e){super(),G(this,\"_ctx\",void 0),this._ctx=e,this.ctx.caller.on(\"#lsp#request#callback\",(e=>{const t=null==e?void 0:e.requestId;t&&this.emit(J(t),null==e?void 0:e.payload)}))}static createRequestTask(e,t,n){return new Z(e,t,n)}async _request(e){const t=this.ctx.baseInfo.id,{success:n,fail:r,final:o,...i}=e,s=this.ctx.Experiments.invokeExperMethod(\"request\",t,i),a=V.createRequestTask(this.ctx.Request,s,e);return i.abortable?a:a.promise}get ctx(){return this._ctx}}const K=Array.isArray;const Y=\"object\"==typeof global&&global&&global.Object===Object&&global;var Q=\"object\"==typeof self&&self&&self.Object===Object&&self;const X=Y||Q||Function(\"return this\")();const ee=X.Symbol;var te=Object.prototype,ne=te.hasOwnProperty,re=te.toString,oe=ee?ee.toStringTag:void 0;const ie=function(e){var t=ne.call(e,oe),n=e[oe];try{e[oe]=void 0;var r=!0}catch(e){}var o=re.call(e);return r&&(t?e[oe]=n:delete e[oe]),o};var se=Object.prototype.toString;const ae=function(e){return se.call(e)};var ce=ee?ee.toStringTag:void 0;const le=function(e){return null==e?void 0===e?\"[object Undefined]\":\"[object Null]\":ce&&ce in Object(e)?ie(e):ae(e)};const ue=function(e){var t=typeof e;return null!=e&&(\"object\"==t||\"function\"==t)};const fe=function(e){if(!ue(e))return!1;var t=le(e);return\"[object Function]\"==t||\"[object GeneratorFunction]\"==t||\"[object AsyncFunction]\"==t||\"[object Proxy]\"==t};const pe=X[\"__core-js_shared__\"];var he,de=(he=/[^.]+$/.exec(pe&&pe.keys&&pe.keys.IE_PROTO||\"\"))?\"Symbol(src)_1.\"+he:\"\";const me=function(e){return!!de&&de in e};var ge=Function.prototype.toString;const ye=function(e){if(null!=e){try{return ge.call(e)}catch(e){}try{return e+\"\"}catch(e){}}return\"\"};var ve=/^\\[object .+?Constructor\\]$/,be=Function.prototype,_e=Object.prototype,we=be.toString,xe=_e.hasOwnProperty,Ce=RegExp(\"^\"+we.call(xe).replace(/[\\\\^$.*+?()[\\]{}|]/g,\"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g,\"$1.*?\")+\"$\");const Se=function(e){return!(!ue(e)||me(e))&&(fe(e)?Ce:ve).test(ye(e))};const Oe=function(e,t){return null==e?void 0:e[t]};const je=function(e,t){var n=Oe(e,t);return Se(n)?n:void 0};const Ae=function(){try{var e=je(Object,\"defineProperty\");return e({},\"\",{}),e}catch(e){}}();const Ee=function(e,t,n){\"__proto__\"==t&&Ae?Ae(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n};const ke=function(e){return function(t,n,r){for(var o=-1,i=Object(t),s=r(t),a=s.length;a--;){var c=s[e?a:++o];if(!1===n(i[c],c,i))break}return t}}();const Te=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r};const Ie=function(e){return null!=e&&\"object\"==typeof e};const Me=function(e){return Ie(e)&&\"[object Arguments]\"==le(e)};var Fe=Object.prototype,Le=Fe.hasOwnProperty,Ne=Fe.propertyIsEnumerable;const Pe=Me(function(){return arguments}())?Me:function(e){return Ie(e)&&Le.call(e,\"callee\")&&!Ne.call(e,\"callee\")};const Re=function(){return!1};var De=\"object\"==typeof exports&&exports&&!exports.nodeType&&exports,Ue=De&&\"object\"==typeof module&&module&&!module.nodeType&&module,$e=Ue&&Ue.exports===De?X.Buffer:void 0;const ze=($e?$e.isBuffer:void 0)||Re;var He=/^(?:0|[1-9]\\d*)$/;const Be=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&(\"number\"==n||\"symbol\"!=n&&He.test(e))&&e>-1&&e%1==0&&e<t};const qe=function(e){return\"number\"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var We={};We[\"[object Float32Array]\"]=We[\"[object Float64Array]\"]=We[\"[object Int8Array]\"]=We[\"[object Int16Array]\"]=We[\"[object Int32Array]\"]=We[\"[object Uint8Array]\"]=We[\"[object Uint8ClampedArray]\"]=We[\"[object Uint16Array]\"]=We[\"[object Uint32Array]\"]=!0,We[\"[object Arguments]\"]=We[\"[object Array]\"]=We[\"[object ArrayBuffer]\"]=We[\"[object Boolean]\"]=We[\"[object DataView]\"]=We[\"[object Date]\"]=We[\"[object Error]\"]=We[\"[object Function]\"]=We[\"[object Map]\"]=We[\"[object Number]\"]=We[\"[object Object]\"]=We[\"[object RegExp]\"]=We[\"[object Set]\"]=We[\"[object String]\"]=We[\"[object WeakMap]\"]=!1;const Ge=function(e){return Ie(e)&&qe(e.length)&&!!We[le(e)]};const Je=function(e){return function(t){return e(t)}};var Ze=\"object\"==typeof exports&&exports&&!exports.nodeType&&exports,Ve=Ze&&\"object\"==typeof module&&module&&!module.nodeType&&module,Ke=Ve&&Ve.exports===Ze&&Y.process,Ye=function(){try{var e=Ve&&Ve.require&&Ve.require(\"util\").types;return e||Ke&&Ke.binding&&Ke.binding(\"util\")}catch(e){}}();var Qe=Ye&&Ye.isTypedArray;const Xe=Qe?Je(Qe):Ge;var et=Object.prototype.hasOwnProperty;const tt=function(e,t){var n=K(e),r=!n&&Pe(e),o=!n&&!r&&ze(e),i=!n&&!r&&!o&&Xe(e),s=n||r||o||i,a=s?Te(e.length,String):[],c=a.length;for(var l in e)!t&&!et.call(e,l)||s&&(\"length\"==l||o&&(\"offset\"==l||\"parent\"==l)||i&&(\"buffer\"==l||\"byteLength\"==l||\"byteOffset\"==l)||Be(l,c))||a.push(l);return a};var nt=Object.prototype;const rt=function(e){var t=e&&e.constructor;return e===(\"function\"==typeof t&&t.prototype||nt)};const ot=function(e,t){return function(n){return e(t(n))}}(Object.keys,Object);var it=Object.prototype.hasOwnProperty;const st=function(e){if(!rt(e))return ot(e);var t=[];for(var n in Object(e))it.call(e,n)&&\"constructor\"!=n&&t.push(n);return t};const at=function(e){return null!=e&&qe(e.length)&&!fe(e)};const ct=function(e){return at(e)?tt(e):st(e)};const lt=function(e,t){return e&&ke(e,t,ct)};const ut=function(){this.__data__=[],this.size=0};const ft=function(e,t){return e===t||e!=e&&t!=t};const pt=function(e,t){for(var n=e.length;n--;)if(ft(e[n][0],t))return n;return-1};var ht=Array.prototype.splice;const dt=function(e){var t=this.__data__,n=pt(t,e);return!(n<0)&&(n==t.length-1?t.pop():ht.call(t,n,1),--this.size,!0)};const mt=function(e){var t=this.__data__,n=pt(t,e);return n<0?void 0:t[n][1]};const gt=function(e){return pt(this.__data__,e)>-1};const yt=function(e,t){var n=this.__data__,r=pt(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function vt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}vt.prototype.clear=ut,vt.prototype.delete=dt,vt.prototype.get=mt,vt.prototype.has=gt,vt.prototype.set=yt;const bt=vt;const _t=function(){this.__data__=new bt,this.size=0};const wt=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n};const xt=function(e){return this.__data__.get(e)};const Ct=function(e){return this.__data__.has(e)};const St=je(X,\"Map\");const Ot=je(Object,\"create\");const jt=function(){this.__data__=Ot?Ot(null):{},this.size=0};const At=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t};var Et=Object.prototype.hasOwnProperty;const kt=function(e){var t=this.__data__;if(Ot){var n=t[e];return\"__lodash_hash_undefined__\"===n?void 0:n}return Et.call(t,e)?t[e]:void 0};var Tt=Object.prototype.hasOwnProperty;const It=function(e){var t=this.__data__;return Ot?void 0!==t[e]:Tt.call(t,e)};const Mt=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Ot&&void 0===t?\"__lodash_hash_undefined__\":t,this};function Ft(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Ft.prototype.clear=jt,Ft.prototype.delete=At,Ft.prototype.get=kt,Ft.prototype.has=It,Ft.prototype.set=Mt;const Lt=Ft;const Nt=function(){this.size=0,this.__data__={hash:new Lt,map:new(St||bt),string:new Lt}};const Pt=function(e){var t=typeof e;return\"string\"==t||\"number\"==t||\"symbol\"==t||\"boolean\"==t?\"__proto__\"!==e:null===e};const Rt=function(e,t){var n=e.__data__;return Pt(t)?n[\"string\"==typeof t?\"string\":\"hash\"]:n.map};const Dt=function(e){var t=Rt(this,e).delete(e);return this.size-=t?1:0,t};const Ut=function(e){return Rt(this,e).get(e)};const $t=function(e){return Rt(this,e).has(e)};const zt=function(e,t){var n=Rt(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function Ht(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Ht.prototype.clear=Nt,Ht.prototype.delete=Dt,Ht.prototype.get=Ut,Ht.prototype.has=$t,Ht.prototype.set=zt;const Bt=Ht;const qt=function(e,t){var n=this.__data__;if(n instanceof bt){var r=n.__data__;if(!St||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Bt(r)}return n.set(e,t),this.size=n.size,this};function Wt(e){var t=this.__data__=new bt(e);this.size=t.size}Wt.prototype.clear=_t,Wt.prototype.delete=wt,Wt.prototype.get=xt,Wt.prototype.has=Ct,Wt.prototype.set=qt;const Gt=Wt;const Jt=function(e){return this.__data__.set(e,\"__lodash_hash_undefined__\"),this};const Zt=function(e){return this.__data__.has(e)};function Vt(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Bt;++t<n;)this.add(e[t])}Vt.prototype.add=Vt.prototype.push=Jt,Vt.prototype.has=Zt;const Kt=Vt;const Yt=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1};const Qt=function(e,t){return e.has(t)};const Xt=function(e,t,n,r,o,i){var s=1&n,a=e.length,c=t.length;if(a!=c&&!(s&&c>a))return!1;var l=i.get(e),u=i.get(t);if(l&&u)return l==t&&u==e;var f=-1,p=!0,h=2&n?new Kt:void 0;for(i.set(e,t),i.set(t,e);++f<a;){var d=e[f],m=t[f];if(r)var g=s?r(m,d,f,t,e,i):r(d,m,f,e,t,i);if(void 0!==g){if(g)continue;p=!1;break}if(h){if(!Yt(t,(function(e,t){if(!Qt(h,t)&&(d===e||o(d,e,n,r,i)))return h.push(t)}))){p=!1;break}}else if(d!==m&&!o(d,m,n,r,i)){p=!1;break}}return i.delete(e),i.delete(t),p};const en=X.Uint8Array;const tn=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n};const nn=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n};var rn=ee?ee.prototype:void 0,on=rn?rn.valueOf:void 0;const sn=function(e,t,n,r,o,i,s){switch(n){case\"[object DataView]\":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case\"[object ArrayBuffer]\":return!(e.byteLength!=t.byteLength||!i(new en(e),new en(t)));case\"[object Boolean]\":case\"[object Date]\":case\"[object Number]\":return ft(+e,+t);case\"[object Error]\":return e.name==t.name&&e.message==t.message;case\"[object RegExp]\":case\"[object String]\":return e==t+\"\";case\"[object Map]\":var a=tn;case\"[object Set]\":var c=1&r;if(a||(a=nn),e.size!=t.size&&!c)return!1;var l=s.get(e);if(l)return l==t;r|=2,s.set(e,t);var u=Xt(a(e),a(t),r,o,i,s);return s.delete(e),u;case\"[object Symbol]\":if(on)return on.call(e)==on.call(t)}return!1};const an=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e};const cn=function(e,t,n){var r=t(e);return K(e)?r:an(r,n(e))};const ln=function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,i=[];++n<r;){var s=e[n];t(s,n,e)&&(i[o++]=s)}return i};const un=function(){return[]};var fn=Object.prototype.propertyIsEnumerable,pn=Object.getOwnPropertySymbols;const hn=pn?function(e){return null==e?[]:(e=Object(e),ln(pn(e),(function(t){return fn.call(e,t)})))}:un;const dn=function(e){return cn(e,ct,hn)};var mn=Object.prototype.hasOwnProperty;const gn=function(e,t,n,r,o,i){var s=1&n,a=dn(e),c=a.length;if(c!=dn(t).length&&!s)return!1;for(var l=c;l--;){var u=a[l];if(!(s?u in t:mn.call(t,u)))return!1}var f=i.get(e),p=i.get(t);if(f&&p)return f==t&&p==e;var h=!0;i.set(e,t),i.set(t,e);for(var d=s;++l<c;){var m=e[u=a[l]],g=t[u];if(r)var y=s?r(g,m,u,t,e,i):r(m,g,u,e,t,i);if(!(void 0===y?m===g||o(m,g,n,r,i):y)){h=!1;break}d||(d=\"constructor\"==u)}if(h&&!d){var v=e.constructor,b=t.constructor;v==b||!(\"constructor\"in e)||!(\"constructor\"in t)||\"function\"==typeof v&&v instanceof v&&\"function\"==typeof b&&b instanceof b||(h=!1)}return i.delete(e),i.delete(t),h};const yn=je(X,\"DataView\");const vn=je(X,\"Promise\");const bn=je(X,\"Set\");const _n=je(X,\"WeakMap\");var wn=\"[object Map]\",xn=\"[object Promise]\",Cn=\"[object Set]\",Sn=\"[object WeakMap]\",On=\"[object DataView]\",jn=ye(yn),An=ye(St),En=ye(vn),kn=ye(bn),Tn=ye(_n),In=le;(yn&&In(new yn(new ArrayBuffer(1)))!=On||St&&In(new St)!=wn||vn&&In(vn.resolve())!=xn||bn&&In(new bn)!=Cn||_n&&In(new _n)!=Sn)&&(In=function(e){var t=le(e),n=\"[object Object]\"==t?e.constructor:void 0,r=n?ye(n):\"\";if(r)switch(r){case jn:return On;case An:return wn;case En:return xn;case kn:return Cn;case Tn:return Sn}return t});const Mn=In;var Fn=\"[object Arguments]\",Ln=\"[object Array]\",Nn=\"[object Object]\",Pn=Object.prototype.hasOwnProperty;const Rn=function(e,t,n,r,o,i){var s=K(e),a=K(t),c=s?Ln:Mn(e),l=a?Ln:Mn(t),u=(c=c==Fn?Nn:c)==Nn,f=(l=l==Fn?Nn:l)==Nn,p=c==l;if(p&&ze(e)){if(!ze(t))return!1;s=!0,u=!1}if(p&&!u)return i||(i=new Gt),s||Xe(e)?Xt(e,t,n,r,o,i):sn(e,t,c,n,r,o,i);if(!(1&n)){var h=u&&Pn.call(e,\"__wrapped__\"),d=f&&Pn.call(t,\"__wrapped__\");if(h||d){var m=h?e.value():e,g=d?t.value():t;return i||(i=new Gt),o(m,g,n,r,i)}}return!!p&&(i||(i=new Gt),gn(e,t,n,r,o,i))};const Dn=function e(t,n,r,o,i){return t===n||(null==t||null==n||!Ie(t)&&!Ie(n)?t!=t&&n!=n:Rn(t,n,r,o,e,i))};const Un=function(e,t,n,r){var o=n.length,i=o,s=!r;if(null==e)return!i;for(e=Object(e);o--;){var a=n[o];if(s&&a[2]?a[1]!==e[a[0]]:!(a[0]in e))return!1}for(;++o<i;){var c=(a=n[o])[0],l=e[c],u=a[1];if(s&&a[2]){if(void 0===l&&!(c in e))return!1}else{var f=new Gt;if(r)var p=r(l,u,c,e,t,f);if(!(void 0===p?Dn(u,l,3,r,f):p))return!1}}return!0};const $n=function(e){return e==e&&!ue(e)};const zn=function(e){for(var t=ct(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,$n(o)]}return t};const Hn=function(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}};const Bn=function(e){var t=zn(e);return 1==t.length&&t[0][2]?Hn(t[0][0],t[0][1]):function(n){return n===e||Un(n,e,t)}};const qn=function(e){return\"symbol\"==typeof e||Ie(e)&&\"[object Symbol]\"==le(e)};var Wn=/\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,Gn=/^\\w*$/;const Jn=function(e,t){if(K(e))return!1;var n=typeof e;return!(\"number\"!=n&&\"symbol\"!=n&&\"boolean\"!=n&&null!=e&&!qn(e))||(Gn.test(e)||!Wn.test(e)||null!=t&&e in Object(t))};function Zn(e,t){if(\"function\"!=typeof e||null!=t&&\"function\"!=typeof t)throw new TypeError(\"Expected a function\");var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var s=e.apply(this,r);return n.cache=i.set(o,s)||i,s};return n.cache=new(Zn.Cache||Bt),n}Zn.Cache=Bt;const Vn=Zn;var Kn=/[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g,Yn=/\\\\(\\\\)?/g;const Qn=function(e){var t=Vn(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(\"\"),e.replace(Kn,(function(e,n,r,o){t.push(r?o.replace(Yn,\"$1\"):n||e)})),t}));const Xn=function(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o};var er=ee?ee.prototype:void 0,tr=er?er.toString:void 0;const nr=function e(t){if(\"string\"==typeof t)return t;if(K(t))return Xn(t,e)+\"\";if(qn(t))return tr?tr.call(t):\"\";var n=t+\"\";return\"0\"==n&&1/t==-Infinity?\"-0\":n};const rr=function(e){return null==e?\"\":nr(e)};const or=function(e,t){return K(e)?e:Jn(e,t)?[e]:Qn(rr(e))};const ir=function(e){if(\"string\"==typeof e||qn(e))return e;var t=e+\"\";return\"0\"==t&&1/e==-Infinity?\"-0\":t};const sr=function(e,t){for(var n=0,r=(t=or(t,e)).length;null!=e&&n<r;)e=e[ir(t[n++])];return n&&n==r?e:void 0};const ar=function(e,t,n){var r=null==e?void 0:sr(e,t);return void 0===r?n:r};const cr=function(e,t){return null!=e&&t in Object(e)};const lr=function(e,t,n){for(var r=-1,o=(t=or(t,e)).length,i=!1;++r<o;){var s=ir(t[r]);if(!(i=null!=e&&n(e,s)))break;e=e[s]}return i||++r!=o?i:!!(o=null==e?0:e.length)&&qe(o)&&Be(s,o)&&(K(e)||Pe(e))};const ur=function(e,t){return null!=e&&lr(e,t,cr)};const fr=function(e,t){return Jn(e)&&$n(t)?Hn(ir(e),t):function(n){var r=ar(n,e);return void 0===r&&r===t?ur(n,e):Dn(t,r,3)}};const pr=function(e){return e};const hr=function(e){return function(t){return null==t?void 0:t[e]}};const dr=function(e){return function(t){return sr(t,e)}};const mr=function(e){return Jn(e)?hr(ir(e)):dr(e)};const gr=function(e){return\"function\"==typeof e?e:null==e?pr:\"object\"==typeof e?K(e)?fr(e[0],e[1]):Bn(e):mr(e)};const yr=function(e,t){var n={};return t=gr(t,3),lt(e,(function(e,r,o){Ee(n,t(e,r,o),e)})),n};function vr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class br{constructor(e,t){vr(this,\"ctx\",void 0),vr(this,\"serviceHooks\",void 0),this.ctx=e,this.serviceHooks=t,e._execCallableAPI(\"register-search-service\",e.baseInfo.id,t.name,t.options);Object.entries({query:{f:\"onQuery\",args:[\"graph\",\"q\",!0],reply:!0,transformOutput:e=>(K(null==e?void 0:e.blocks)&&(e.blocks=e.blocks.map((e=>e&&yr(e,((e,t)=>`block/${t}`))))),e)},rebuildBlocksIndice:{f:\"onIndiceInit\",args:[\"graph\",\"blocks\"]},transactBlocks:{f:\"onBlocksChanged\",args:[\"graph\",\"data\"]},truncateBlocks:{f:\"onIndiceReset\",args:[\"graph\"]},removeDb:{f:\"onGraph\",args:[\"graph\"]}}).forEach((([n,r])=>{const o=(e=>`service:search:${e}:${t.name}`)(n);e.caller.on(o,(async n=>{if(fe(null==t?void 0:t[r.f])){let i=null;try{i=await t[r.f].apply(t,(r.args||[]).map((e=>{if(n){if(!0===e)return n;if(n.hasOwnProperty(e)){const t=n[e];return delete n[e],t}}}))),r.transformOutput&&(i=r.transformOutput(i))}catch(e){console.error(\"[SearchService] \",e),i=e}finally{r.reply&&e.caller.call(`${o}:reply`,i)}}}))}))}}function _r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const wr=Symbol.for(\"proxy-continue\"),xr=w()(\"LSPlugin:user\"),Cr=new g(\"\",{console:!0});function Sr(e,t,n){var r;const{key:o,label:i,desc:s,palette:a,keybinding:c,extras:l}=t;if(\"function\"!=typeof n)return this.logger.error(`${o||i}: command action should be function.`),!1;const u=function(e){if(\"string\"==typeof e)return e.trim().replace(/\\s/g,\"_\").toLowerCase()}(o);if(!u)return this.logger.error(`${i}: command key is required.`),!1;const f=`SimpleCommandHook${u}${++kr}`;this.Editor[\"on\"+f](n),null===(r=this.caller)||void 0===r||r.call(\"api:call\",{method:\"register-plugin-simple-command\",args:[this.baseInfo.id,[{key:u,label:i,type:e,desc:s,keybinding:c,extras:l},[\"editor/hook\",f]],a]})}function Or(e){return!(\"string\"!=typeof(t=e)||36!==t.length||!/^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/gi.test(t))||(Cr.error(`#${e} is not a valid UUID string.`),!1);var t}let jr=null,Ar=new Map;const Er={async getInfo(e){return jr||(jr=await this._execCallableAPIAsync(\"get-app-info\")),\"string\"==typeof e?jr[e]:jr},registerCommand:Sr,registerSearchService(e){if(Ar.has(e.name))throw new Error(`SearchService: #${e.name} has registered!`);Ar.set(e.name,new br(this,e))},registerCommandPalette(e,t){const{key:n,label:r,keybinding:o}=e;return Sr.call(this,\"$palette$\",{key:n,label:r,palette:!0,keybinding:o},t)},registerCommandShortcut(e,t,n={}){\"string\"==typeof e&&(e={mode:\"global\",binding:e});const{binding:r}=e,o=\"$shortcut$\",i=n.key||o+m(null==r?void 0:r.toString());return Sr.call(this,o,{...n,key:i,palette:!1,keybinding:e},t)},registerUIItem(e,t){var n;const r=this.baseInfo.id;null===(n=this.caller)||void 0===n||n.call(\"api:call\",{method:\"register-plugin-ui-item\",args:[r,e,t]})},registerPageMenuItem(e,t){if(\"function\"!=typeof t)return!1;const n=e+\"_\"+this.baseInfo.id,r=e;Sr.call(this,\"page-menu-item\",{key:n,label:r},t)},onBlockRendererSlotted(e,t){if(!Or(e))return;const n=this.baseInfo.id,r=`hook:editor:${m(`slot:${e}`)}`;return this.caller.on(r,t),this.App._installPluginHook(n,r),()=>{this.caller.off(r,t),this.App._uninstallPluginHook(n,r)}},invokeExternalPlugin(e,...t){var n;if(!(e=null===(n=e)||void 0===n?void 0:n.trim()))return;let[r,o]=e.split(\".\");if(![\"models\",\"commands\"].includes(null==o?void 0:o.toLowerCase()))throw new Error(\"Type only support '.models' or '.commands' currently.\");const i=e.replace(`${r}.${o}.`,\"\");if(!r||!o||!i)throw new Error(`Illegal type of #${e} to invoke external plugin.`);return this._execCallableAPIAsync(\"invoke_external_plugin_cmd\",r,o.toLowerCase(),i,t)},setFullScreen(e){const t=(...e)=>this._callWin(\"setFullScreen\",...e);\"toggle\"===e?this._callWin(\"isFullScreen\").then((e=>{e?t():t(!0)})):e?t(!0):t()}};let kr=0;const Tr={newBlockUUID(){return this._execCallableAPIAsync(\"new_block_uuid\")},registerSlashCommand(e,t){var n;xr(\"Register slash command #\",this.baseInfo.id,e,t),\"function\"==typeof t&&(t=[[\"editor/clear-current-slash\",!1],[\"editor/restore-saved-cursor\"],[\"editor/hook\",t]]),t=t.map((e=>{const[t,...n]=e;if(\"editor/hook\"===t){let r=n[0],o=()=>{var e;null===(e=this.caller)||void 0===e||e.callUserModel(r)};\"function\"==typeof r&&(o=r);const i=`SlashCommandHook${t}${++kr}`;e[1]=i,this.Editor[\"on\"+i](o)}return e})),null===(n=this.caller)||void 0===n||n.call(\"api:call\",{method:\"register-plugin-slash-command\",args:[this.baseInfo.id,[e,t]]})},registerBlockContextMenuItem(e,t){if(\"function\"!=typeof t)return!1;const n=e+\"_\"+this.baseInfo.id;Sr.call(this,\"block-context-menu-item\",{key:n,label:e},t)},registerHighlightContextMenuItem(e,t,n){if(\"function\"!=typeof t)return!1;const r=e+\"_\"+this.baseInfo.id;Sr.call(this,\"highlight-context-menu-item\",{key:r,label:e,extras:n},t)},scrollToBlockInPage(e,t,n){const r=\"block-content-\"+t;null!=n&&n.replaceState?this.App.replaceState(\"page\",{name:e},{anchor:r}):this.App.pushState(\"page\",{name:e},{anchor:r})}},Ir={onBlockChanged(e,t){if(!Or(e))return;const n=this.baseInfo.id,r=`hook:db:${m(`block:${e}`)}`,o=({block:n,txData:r,txMeta:o})=>{n.uuid===e&&t(n,r,o)};return this.caller.on(r,o),this.App._installPluginHook(n,r),()=>{this.caller.off(r,o),this.App._uninstallPluginHook(n,r)}},datascriptQuery(e,...t){if(t.pop(),null!=t&&t.some((e=>\"function\"==typeof e))){return this.Experiments.ensureHostScope().logseq.api.datascript_query(e,...t)}return this._execCallableAPIAsync(\"datascript_query\",e,...t)}},Mr={},Fr={},Lr={makeSandboxStorage(){return new q(this,{assets:!0})}};class Nr extends(p()){constructor(e,t){super(),_r(this,\"_baseInfo\",void 0),_r(this,\"_caller\",void 0),_r(this,\"_version\",\"0.0.17\"),_r(this,\"_debugTag\",\"\"),_r(this,\"_settingsSchema\",void 0),_r(this,\"_connected\",!1),_r(this,\"_ui\",new Map),_r(this,\"_mFileStorage\",void 0),_r(this,\"_mRequest\",void 0),_r(this,\"_mExperiments\",void 0),_r(this,\"_beforeunloadCallback\",void 0),this._baseInfo=e,this._caller=t,t.on(\"sys:ui:visible\",(e=>{null!=e&&e.toggle&&this.toggleMainUI()})),t.on(\"settings:changed\",(e=>{const t=Object.assign({},this.settings),n=Object.assign(this._baseInfo.settings,e);this.emit(\"settings:changed\",{...n},t)})),t.on(\"beforeunload\",(async e=>{const{actor:t,...n}=e,r=this._beforeunloadCallback;try{r&&await r(n),null==t||t.resolve(null)}catch(e){this.logger.error(\"[beforeunload] \",e),null==t||t.reject(e)}}))}async ready(e,t){var n,r;if(!this._connected)try{var i;\"function\"==typeof e&&(t=e,e={});let s=await this._caller.connectToParent(e);this._connected=!0,n=this._baseInfo,r=s,s=o()(n,r,{arrayMerge:(e,t)=>t}),this._baseInfo=s,null!==(i=s)&&void 0!==i&&i.id&&(this._debugTag=this._caller.debugTag=`#${s.id} [${s.name}]`,this.logger.setTag(this._debugTag)),this._settingsSchema&&(s.settings=function(e,t){const n=(t||[]).reduce(((e,t)=>(\"default\"in t&&(e[t.key]=t.default),e)),{});return Object.assign(n,e)}(s.settings,this._settingsSchema),await this.useSettingsSchema(this._settingsSchema));try{await this._execCallableAPIAsync(\"setSDKMetadata\",{version:this._version})}catch(e){console.warn(e)}t&&t.call(this,s)}catch(e){console.error(`${this._debugTag} [Ready Error]`,e)}}ensureConnected(){if(!this._connected)throw new Error(\"not connected\")}beforeunload(e){\"function\"==typeof e&&(this._beforeunloadCallback=e)}provideModel(e){return this.caller._extendUserModel(e),this}provideTheme(e){return this.caller.call(\"provider:theme\",e),this}provideStyle(e){return this.caller.call(\"provider:style\",e),this}provideUI(e){return this.caller.call(\"provider:ui\",e),this}useSettingsSchema(e){return this.connected&&this.caller.call(\"settings:schema\",{schema:e,isSync:!0}),this._settingsSchema=e,this}updateSettings(e){this.caller.call(\"settings:update\",e)}onSettingsChanged(e){const t=\"settings:changed\";return this.on(t,e),()=>this.off(t,e)}showSettingsUI(){this.caller.call(\"settings:visible:changed\",{visible:!0})}hideSettingsUI(){this.caller.call(\"settings:visible:changed\",{visible:!1})}setMainUIAttrs(e){this.caller.call(\"main-ui:attrs\",e)}setMainUIInlineStyle(e){this.caller.call(\"main-ui:style\",e)}hideMainUI(e){const t={key:0,visible:!1,cursor:null==e?void 0:e.restoreEditingCursor};this.caller.call(\"main-ui:visible\",t),this.emit(\"ui:visible:changed\",t),this._ui.set(t.key,t)}showMainUI(e){const t={key:0,visible:!0,autoFocus:null==e?void 0:e.autoFocus};this.caller.call(\"main-ui:visible\",t),this.emit(\"ui:visible:changed\",t),this._ui.set(t.key,t)}toggleMainUI(){const e=0,t=this._ui.get(e);t&&t.visible?this.hideMainUI():this.showMainUI()}get version(){return this._version}get isMainUIVisible(){const e=this._ui.get(0);return Boolean(e&&e.visible)}get connected(){return this._connected}get baseInfo(){return this._baseInfo}get effect(){return(e=this)&&((null===(t=e.baseInfo)||void 0===t?void 0:t.effect)||!(null!==(n=e.baseInfo)&&void 0!==n&&n.iir));var e,t,n}get logger(){return Cr}get settings(){var e;return null===(e=this.baseInfo)||void 0===e?void 0:e.settings}get caller(){return this._caller}resolveResourceFullUrl(e){if(this.ensureConnected(),e)return e=e.replace(/^[.\\\\/]+/,\"\"),y(this._baseInfo.lsr,e)}_makeUserProxy(e,t){const n=this,r=this.caller;return new Proxy(e,{get(e,o,i){const s=e[o];return function(...e){if(s){const r=s.apply(n,e.concat(t));if(r!==wr)return r}if(t){const i=o.toString().match(/^(once|off|on)/i);if(null!=i){const o=i[0].toLowerCase(),s=i.input,a=\"off\"===o,c=n.baseInfo.id;let l=s.slice(o.length),u=e[0],f=e[1];\"string\"==typeof u&&\"function\"==typeof f&&(u=u.replace(/^logseq./,\":\"),l=`${l}${u}`,u=f,f=e[2]),l=`hook:${t}:${m(l)}`,r[o](l,u);const p=()=>{r.off(l,u),r.listenerCount(l)||n.App._uninstallPluginHook(c,l)};return a?void p():(n.App._installPluginHook(c,l,f),p)}}let i=o;return[\"git\",\"ui\",\"assets\"].includes(t)&&(i=t+\"_\"+i),r.callAsync(\"api:call\",{tag:t,method:i,args:e})}}})}_execCallableAPIAsync(e,...t){return this._caller.callAsync(\"api:call\",{method:e,args:t})}_execCallableAPI(e,...t){this._caller.call(\"api:call\",{method:e,args:t})}_callWin(...e){return this._execCallableAPIAsync(\"_callMainWin\",...e)}get App(){return this._makeUserProxy(Er,\"app\")}get Editor(){return this._makeUserProxy(Tr,\"editor\")}get DB(){return this._makeUserProxy(Ir,\"db\")}get Git(){return this._makeUserProxy(Mr,\"git\")}get UI(){return this._makeUserProxy(Fr,\"ui\")}get Assets(){return this._makeUserProxy(Lr,\"assets\")}get FileStorage(){let e=this._mFileStorage;return e||(e=this._mFileStorage=new q(this)),e}get Request(){let e=this._mRequest;return e||(e=this._mRequest=new V(this)),e}get Experiments(){let e=this._mExperiments;return e||(e=this._mExperiments=new W(this)),e}}function Pr(e,t){return new Nr(e,t)}if(null==window.__LSP__HOST__){const e=new H(null);window.logseq=Pr({},e)}})(),r})()));"], "mappings": ";;;;;AAAA;AAAA;AACA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,gBAAc,EAAE,IAAE,EAAE,gBAAc,EAAE;AAAA,IAAC,EAAE,MAAM,OAAK,MAAI;AAAC,UAAI,IAAE,EAAC,KAAI,CAACA,IAAEC,IAAEC,OAAI;AAAC,YAAIC,KAAED,GAAE,GAAG;AAAE,QAAAD,GAAE,aAAW,SAASA,IAAE;AAAC,cAAGA,GAAE,CAAC,KAAG,KAAK,YAAU,OAAK,MAAI,KAAK,aAAW,KAAK,YAAU,QAAM,OAAKA,GAAE,CAAC,KAAG,KAAK,YAAU,QAAM,OAAK,MAAID,GAAE,QAAQ,SAAS,KAAK,IAAI,GAAE,CAAC,KAAK;AAAU;AAAO,gBAAME,KAAE,YAAU,KAAK;AAAM,UAAAD,GAAE,OAAO,GAAE,GAAEC,IAAE,gBAAgB;AAAE,cAAIC,KAAE,GAAEC,KAAE;AAAE,UAAAH,GAAE,CAAC,EAAE,QAAQ,eAAe,CAAAD,OAAG;AAAC,qBAAOA,OAAIG,MAAI,SAAOH,OAAII,KAAED;AAAA,UAAG,CAAE,GAAEF,GAAE,OAAOG,IAAE,GAAEF,EAAC;AAAA,QAAC,GAAED,GAAE,OAAK,SAASD,IAAE;AAAC,cAAG;AAAC,YAAAA,KAAEC,GAAE,QAAQ,QAAQ,SAAQD,EAAC,IAAEC,GAAE,QAAQ,WAAW,OAAO;AAAA,UAAC,SAAOD,IAAE;AAAA,UAAC;AAAA,QAAC,GAAEC,GAAE,OAAK,WAAU;AAAC,cAAID;AAAE,cAAG;AAAC,YAAAA,KAAEC,GAAE,QAAQ,QAAQ,OAAO;AAAA,UAAC,SAAOD,IAAE;AAAA,UAAC;AAAC,WAACA,MAAG,WAASG,MAAG,SAAQA,OAAIH,KAAEG,GAAE,IAAI;AAAO,iBAAOH;AAAA,QAAC,GAAEC,GAAE,YAAU,WAAU;AAAC,cAAG,eAAa,OAAO,UAAQ,OAAO,YAAU,eAAa,OAAO,QAAQ,QAAM,OAAO,QAAQ;AAAQ,mBAAM;AAAG,cAAG,eAAa,OAAO,aAAW,UAAU,aAAW,UAAU,UAAU,YAAY,EAAE,MAAM,uBAAuB;AAAE,mBAAM;AAAG,iBAAM,eAAa,OAAO,YAAU,SAAS,mBAAiB,SAAS,gBAAgB,SAAO,SAAS,gBAAgB,MAAM,oBAAkB,eAAa,OAAO,UAAQ,OAAO,YAAU,OAAO,QAAQ,WAAS,OAAO,QAAQ,aAAW,OAAO,QAAQ,UAAQ,eAAa,OAAO,aAAW,UAAU,aAAW,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,KAAG,SAAS,OAAO,IAAG,EAAE,KAAG,MAAI,eAAa,OAAO,aAAW,UAAU,aAAW,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,QAAC,GAAEA,GAAE,UAAQ,WAAU;AAAC,cAAG;AAAC,mBAAO;AAAA,UAAY,SAAOD,IAAE;AAAA,UAAC;AAAA,QAAC,EAAE,GAAEC,GAAE,WAAS,MAAI;AAAC,cAAID,KAAE;AAAG,iBAAM,MAAI;AAAC,YAAAA,OAAIA,KAAE,MAAG,QAAQ,KAAK,uIAAuI;AAAA,UAAE;AAAA,QAAC,GAAG,GAAEC,GAAE,SAAO,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAEA,GAAE,MAAI,QAAQ,SAAO,QAAQ,QAAM,MAAI;AAAA,QAAC,IAAGD,GAAE,UAAQE,GAAE,GAAG,EAAED,EAAC;AAAE,cAAK,EAAC,YAAW,EAAC,IAAED,GAAE;AAAQ,UAAE,IAAE,SAASA,IAAE;AAAC,cAAG;AAAC,mBAAO,KAAK,UAAUA,EAAC;AAAA,UAAC,SAAOA,IAAE;AAAC,mBAAM,iCAA+BA,GAAE;AAAA,UAAO;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAACA,IAAEC,IAAEC,OAAI;AAAC,QAAAF,GAAE,UAAQ,SAASA,IAAE;AAAC,mBAASC,GAAED,IAAE;AAAC,gBAAIE,IAAEE,IAAE,GAAE,IAAE;AAAK,qBAAS,KAAKJ,IAAE;AAAC,kBAAG,CAAC,EAAE;AAAQ;AAAO,oBAAMG,KAAE,GAAEC,KAAE,OAAO,oBAAI,MAAI,GAAEC,KAAED,MAAGF,MAAGE;AAAG,cAAAD,GAAE,OAAKE,IAAEF,GAAE,OAAKD,IAAEC,GAAE,OAAKC,IAAEF,KAAEE,IAAEJ,GAAE,CAAC,IAAEC,GAAE,OAAOD,GAAE,CAAC,CAAC,GAAE,YAAU,OAAOA,GAAE,CAAC,KAAGA,GAAE,QAAQ,IAAI;AAAE,kBAAIM,KAAE;AAAE,cAAAN,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,QAAQ,iBAAiB,CAACE,IAAEE,OAAI;AAAC,oBAAG,SAAOF;AAAE,yBAAM;AAAI,gBAAAI;AAAI,sBAAMD,KAAEJ,GAAE,WAAWG,EAAC;AAAE,oBAAG,cAAY,OAAOC,IAAE;AAAC,wBAAMJ,KAAED,GAAEM,EAAC;AAAE,kBAAAJ,KAAEG,GAAE,KAAKF,IAAEF,EAAC,GAAED,GAAE,OAAOM,IAAE,CAAC,GAAEA;AAAA,gBAAG;AAAC,uBAAOJ;AAAA,cAAC,CAAE,GAAED,GAAE,WAAW,KAAKE,IAAEH,EAAC;AAAE,eAACG,GAAE,OAAKF,GAAE,KAAK,MAAME,IAAEH,EAAC;AAAA,YAAC;AAAC,mBAAO,EAAE,YAAUA,IAAE,EAAE,YAAUC,GAAE,UAAU,GAAE,EAAE,QAAMA,GAAE,YAAYD,EAAC,GAAE,EAAE,SAAOG,IAAE,EAAE,UAAQF,GAAE,SAAQ,OAAO,eAAe,GAAE,WAAU,EAAC,YAAW,MAAG,cAAa,OAAG,KAAI,MAAI,SAAO,IAAE,KAAGG,OAAIH,GAAE,eAAaG,KAAEH,GAAE,YAAW,IAAEA,GAAE,QAAQD,EAAC,IAAG,IAAG,KAAI,CAAAA,OAAG;AAAC,kBAAEA;AAAA,YAAC,EAAC,CAAC,GAAE,cAAY,OAAOC,GAAE,QAAMA,GAAE,KAAK,CAAC,GAAE;AAAA,UAAC;AAAC,mBAASE,GAAEH,IAAEE,IAAE;AAAC,kBAAMC,KAAEF,GAAE,KAAK,aAAW,WAASC,KAAE,MAAIA,MAAGF,EAAC;AAAE,mBAAOG,GAAE,MAAI,KAAK,KAAIA;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAE;AAAC,mBAAOA,GAAE,SAAS,EAAE,UAAU,GAAEA,GAAE,SAAS,EAAE,SAAO,CAAC,EAAE,QAAQ,WAAU,GAAG;AAAA,UAAC;AAAC,iBAAOC,GAAE,QAAMA,IAAEA,GAAE,UAAQA,IAAEA,GAAE,SAAO,SAASD,IAAE;AAAC,gBAAGA,cAAa;AAAM,qBAAOA,GAAE,SAAOA,GAAE;AAAQ,mBAAOA;AAAA,UAAC,GAAEC,GAAE,UAAQ,WAAU;AAAC,kBAAMD,KAAE,CAAC,GAAGC,GAAE,MAAM,IAAI,CAAC,GAAE,GAAGA,GAAE,MAAM,IAAI,CAAC,EAAE,IAAK,CAAAD,OAAG,MAAIA,EAAE,CAAC,EAAE,KAAK,GAAG;AAAE,mBAAOC,GAAE,OAAO,EAAE,GAAED;AAAA,UAAC,GAAEC,GAAE,SAAO,SAASD,IAAE;AAAC,gBAAIE;AAAE,YAAAD,GAAE,KAAKD,EAAC,GAAEC,GAAE,aAAWD,IAAEC,GAAE,QAAM,CAAC,GAAEA,GAAE,QAAM,CAAC;AAAE,kBAAME,MAAG,YAAU,OAAOH,KAAEA,KAAE,IAAI,MAAM,QAAQ,GAAEI,KAAED,GAAE;AAAO,iBAAID,KAAE,GAAEA,KAAEE,IAAEF;AAAI,cAAAC,GAAED,EAAC,MAAI,SAAOF,KAAEG,GAAED,EAAC,EAAE,QAAQ,OAAM,KAAK,GAAG,CAAC,IAAED,GAAE,MAAM,KAAK,IAAI,OAAO,MAAID,GAAE,MAAM,CAAC,IAAE,GAAG,CAAC,IAAEC,GAAE,MAAM,KAAK,IAAI,OAAO,MAAID,KAAE,GAAG,CAAC;AAAA,UAAE,GAAEC,GAAE,UAAQ,SAASD,IAAE;AAAC,gBAAG,QAAMA,GAAEA,GAAE,SAAO,CAAC;AAAE,qBAAM;AAAG,gBAAIE,IAAEC;AAAE,iBAAID,KAAE,GAAEC,KAAEF,GAAE,MAAM,QAAOC,KAAEC,IAAED;AAAI,kBAAGD,GAAE,MAAMC,EAAC,EAAE,KAAKF,EAAC;AAAE,uBAAM;AAAG,iBAAIE,KAAE,GAAEC,KAAEF,GAAE,MAAM,QAAOC,KAAEC,IAAED;AAAI,kBAAGD,GAAE,MAAMC,EAAC,EAAE,KAAKF,EAAC;AAAE,uBAAM;AAAG,mBAAM;AAAA,UAAE,GAAEC,GAAE,WAASC,GAAE,GAAG,GAAED,GAAE,UAAQ,WAAU;AAAC,oBAAQ,KAAK,uIAAuI;AAAA,UAAC,GAAE,OAAO,KAAKD,EAAC,EAAE,QAAS,CAAAE,OAAG;AAAC,YAAAD,GAAEC,EAAC,IAAEF,GAAEE,EAAC;AAAA,UAAC,CAAE,GAAED,GAAE,QAAM,CAAC,GAAEA,GAAE,QAAM,CAAC,GAAEA,GAAE,aAAW,CAAC,GAAEA,GAAE,cAAY,SAASD,IAAE;AAAC,gBAAIE,KAAE;AAAE,qBAAQD,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,cAAAC,MAAGA,MAAG,KAAGA,KAAEF,GAAE,WAAWC,EAAC,GAAEC,MAAG;AAAE,mBAAOD,GAAE,OAAO,KAAK,IAAIC,EAAC,IAAED,GAAE,OAAO,MAAM;AAAA,UAAC,GAAEA,GAAE,OAAOA,GAAE,KAAK,CAAC,GAAEA;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAD,OAAG;AAAC;AAAa,YAAIC,KAAE,SAASD,IAAE;AAAC,iBAAO,SAASA,IAAE;AAAC,mBAAM,CAAC,CAACA,MAAG,YAAU,OAAOA;AAAA,UAAC,EAAEA,EAAC,KAAG,CAAC,SAASA,IAAE;AAAC,gBAAIC,KAAE,OAAO,UAAU,SAAS,KAAKD,EAAC;AAAE,mBAAM,sBAAoBC,MAAG,oBAAkBA,MAAG,SAASD,IAAE;AAAC,qBAAOA,GAAE,aAAWE;AAAA,YAAC,EAAEF,EAAC;AAAA,UAAC,EAAEA,EAAC;AAAA,QAAC;AAAE,YAAIE,KAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,IAAI,eAAe,IAAE;AAAM,iBAASC,GAAEH,IAAEC,IAAE;AAAC,iBAAM,UAAKA,GAAE,SAAOA,GAAE,kBAAkBD,EAAC,IAAE,GAAGE,KAAEF,IAAE,MAAM,QAAQE,EAAC,IAAE,CAAC,IAAE,CAAC,IAAGF,IAAEC,EAAC,IAAED;AAAE,cAAIE;AAAA,QAAC;AAAC,iBAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,iBAAOF,GAAE,OAAOC,EAAC,EAAE,IAAK,SAASD,IAAE;AAAC,mBAAOG,GAAEH,IAAEE,EAAC;AAAA,UAAC,CAAE;AAAA,QAAC;AAAC,iBAAS,EAAEF,IAAE;AAAC,iBAAO,OAAO,KAAKA,EAAC,EAAE,OAAO,SAASA,IAAE;AAAC,mBAAO,OAAO,wBAAsB,OAAO,sBAAsBA,EAAC,EAAE,OAAQ,SAASC,IAAE;AAAC,qBAAO,OAAO,qBAAqB,KAAKD,IAAEC,EAAC;AAAA,YAAC,CAAE,IAAE,CAAC;AAAA,UAAC,EAAED,EAAC,CAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAE;AAAC,cAAG;AAAC,mBAAOA,MAAKD;AAAA,UAAC,SAAOA,IAAE;AAAC,mBAAM;AAAA,UAAE;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,cAAIE,KAAE,CAAC;AAAE,iBAAOF,GAAE,kBAAkBF,EAAC,KAAG,EAAEA,EAAC,EAAE,QAAS,SAASC,IAAE;AAAC,YAAAG,GAAEH,EAAC,IAAEE,GAAEH,GAAEC,EAAC,GAAEC,EAAC;AAAA,UAAC,CAAE,GAAE,EAAED,EAAC,EAAE,QAAS,SAASI,IAAE;AAAC,aAAC,SAASL,IAAEC,IAAE;AAAC,qBAAO,EAAED,IAAEC,EAAC,KAAG,EAAE,OAAO,eAAe,KAAKD,IAAEC,EAAC,KAAG,OAAO,qBAAqB,KAAKD,IAAEC,EAAC;AAAA,YAAE,GAAGD,IAAEK,EAAC,MAAI,EAAEL,IAAEK,EAAC,KAAGH,GAAE,kBAAkBD,GAAEI,EAAC,CAAC,IAAED,GAAEC,EAAC,IAAE,SAASL,IAAEC,IAAE;AAAC,kBAAG,CAACA,GAAE;AAAY,uBAAO;AAAE,kBAAIC,KAAED,GAAE,YAAYD,EAAC;AAAE,qBAAM,cAAY,OAAOE,KAAEA,KAAE;AAAA,YAAC,EAAEG,IAAEH,EAAC,EAAEF,GAAEK,EAAC,GAAEJ,GAAEI,EAAC,GAAEH,EAAC,IAAEE,GAAEC,EAAC,IAAEF,GAAEF,GAAEI,EAAC,GAAEH,EAAC;AAAA,UAAE,CAAE,GAAEE;AAAA,QAAC;AAAC,iBAAS,EAAEJ,IAAEE,IAAEG,IAAE;AAAC,WAACA,KAAEA,MAAG,CAAC,GAAG,aAAWA,GAAE,cAAY,GAAEA,GAAE,oBAAkBA,GAAE,qBAAmBJ,IAAEI,GAAE,gCAA8BF;AAAE,cAAIG,KAAE,MAAM,QAAQJ,EAAC;AAAE,iBAAOI,OAAI,MAAM,QAAQN,EAAC,IAAEM,KAAED,GAAE,WAAWL,IAAEE,IAAEG,EAAC,IAAE,EAAEL,IAAEE,IAAEG,EAAC,IAAEF,GAAED,IAAEG,EAAC;AAAA,QAAC;AAAC,UAAE,MAAI,SAASL,IAAEC,IAAE;AAAC,cAAG,CAAC,MAAM,QAAQD,EAAC;AAAE,kBAAM,IAAI,MAAM,mCAAmC;AAAE,iBAAOA,GAAE,OAAQ,SAASA,IAAEE,IAAE;AAAC,mBAAO,EAAEF,IAAEE,IAAED,EAAC;AAAA,UAAC,GAAG,CAAC,CAAC;AAAA,QAAC;AAAE,YAAI,IAAE;AAAE,QAAAD,GAAE,UAAQ;AAAA,MAAC,GAAE,KAAI,SAASA,IAAE;AAAC,QAAAA,GAAE,UAAQ,WAAU;AAAC;AAAa,mBAASA,GAAEC,IAAE;AAAC,mBAAOD,KAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,qBAAO,OAAOA;AAAA,YAAC,IAAE,SAASA,IAAE;AAAC,qBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,YAAC,GAAEA,GAAEC,EAAC;AAAA,UAAC;AAAC,mBAASA,GAAED,IAAEE,IAAE;AAAC,mBAAOD,KAAE,OAAO,kBAAgB,SAASD,IAAEC,IAAE;AAAC,qBAAOD,GAAE,YAAUC,IAAED;AAAA,YAAC,GAAEC,GAAED,IAAEE,EAAC;AAAA,UAAC;AAAC,mBAASA,KAAG;AAAC,gBAAG,eAAa,OAAO,WAAS,CAAC,QAAQ;AAAU,qBAAM;AAAG,gBAAG,QAAQ,UAAU;AAAK,qBAAM;AAAG,gBAAG,cAAY,OAAO;AAAM,qBAAM;AAAG,gBAAG;AAAC,qBAAO,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAQ,CAAC,GAAG,WAAU;AAAA,cAAC,CAAE,CAAC,GAAE;AAAA,YAAE,SAAOF,IAAE;AAAC,qBAAM;AAAA,YAAE;AAAA,UAAC;AAAC,mBAASG,GAAEH,IAAEI,IAAEC,IAAE;AAAC,mBAAOF,KAAED,GAAE,IAAE,QAAQ,YAAU,SAASF,IAAEE,IAAEC,IAAE;AAAC,kBAAIC,KAAE,CAAC,IAAI;AAAE,cAAAA,GAAE,KAAK,MAAMA,IAAEF,EAAC;AAAE,kBAAIG,KAAE,KAAI,SAAS,KAAK,MAAML,IAAEI,EAAC;AAAG,qBAAOD,MAAGF,GAAEI,IAAEF,GAAE,SAAS,GAAEE;AAAA,YAAC,GAAEF,GAAE,MAAM,MAAK,SAAS;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAE;AAAC,mBAAO,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAE;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAE;AAAC,gBAAG,MAAM,QAAQA,EAAC;AAAE,qBAAO,EAAEA,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAE;AAAC,gBAAG,eAAa,OAAO,UAAQ,QAAMA,GAAE,OAAO,QAAQ,KAAG,QAAMA,GAAE,YAAY;AAAE,qBAAO,MAAM,KAAKA,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAEC,IAAE;AAAC,gBAAGD,IAAE;AAAC,kBAAG,YAAU,OAAOA;AAAE,uBAAO,EAAEA,IAAEC,EAAC;AAAE,kBAAIC,KAAE,OAAO,UAAU,SAAS,KAAKF,EAAC,EAAE,MAAM,GAAE,EAAE;AAAE,qBAAM,aAAWE,MAAGF,GAAE,gBAAcE,KAAEF,GAAE,YAAY,OAAM,UAAQE,MAAG,UAAQA,KAAE,MAAM,KAAKF,EAAC,IAAE,gBAAcE,MAAG,2CAA2C,KAAKA,EAAC,IAAE,EAAEF,IAAEC,EAAC,IAAE;AAAA,YAAM;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAEC,IAAE;AAAC,aAAC,QAAMA,MAAGA,KAAED,GAAE,YAAUC,KAAED,GAAE;AAAQ,qBAAQE,KAAE,GAAEC,KAAE,IAAI,MAAMF,EAAC,GAAEC,KAAED,IAAEC;AAAI,cAAAC,GAAED,EAAC,IAAEF,GAAEE,EAAC;AAAE,mBAAOC;AAAA,UAAC;AAAC,mBAAS,IAAG;AAAC,kBAAM,IAAI,UAAU,sIAAsI;AAAA,UAAC;AAAC,cAAI,IAAE,OAAO,gBAAe,IAAE,OAAO,gBAAe,IAAE,OAAO,UAAS,IAAE,OAAO,gBAAe,IAAE,OAAO,0BAAyB,IAAE,OAAO,QAAO,IAAE,OAAO,MAAK,IAAE,OAAO,QAAO,IAAE,eAAa,OAAO,WAAS,SAAQ,IAAE,EAAE,OAAM,IAAE,EAAE;AAAU,gBAAI,IAAE,SAASH,IAAEC,IAAEC,IAAE;AAAC,mBAAOF,GAAE,MAAMC,IAAEC,EAAC;AAAA,UAAC,IAAG,MAAI,IAAE,SAASF,IAAE;AAAC,mBAAOA;AAAA,UAAC,IAAG,MAAI,IAAE,SAASA,IAAE;AAAC,mBAAOA;AAAA,UAAC,IAAG,MAAI,IAAE,SAASA,IAAEC,IAAE;AAAC,mBAAOE,GAAEH,IAAE,EAAEC,EAAC,CAAC;AAAA,UAAC;AAAG,cAAI,IAAE,EAAE,MAAM,UAAU,OAAO,GAAE,IAAE,EAAE,MAAM,UAAU,GAAG,GAAE,IAAE,EAAE,MAAM,UAAU,IAAI,GAAE,IAAE,EAAE,OAAO,UAAU,WAAW,GAAE,IAAE,EAAE,OAAO,UAAU,KAAK,GAAE,IAAE,EAAE,OAAO,UAAU,OAAO,GAAE,IAAE,EAAE,OAAO,UAAU,OAAO,GAAE,IAAE,EAAE,OAAO,UAAU,IAAI,GAAE,IAAE,EAAE,OAAO,UAAU,IAAI,GAAE,IAAE,EAAE,SAAS;AAAE,mBAAS,EAAED,IAAE;AAAC,mBAAO,SAASC,IAAE;AAAC,uBAAQC,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,KAAE,IAAEA,KAAE,IAAE,CAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,gBAAAD,GAAEC,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,qBAAO,EAAEJ,IAAEC,IAAEE,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAE;AAAC,mBAAO,WAAU;AAAC,uBAAQC,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,gBAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,qBAAO,EAAEH,IAAEE,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAEC,IAAE;AAAC,iBAAG,EAAED,IAAE,IAAI;AAAE,qBAAQE,KAAED,GAAE,QAAOC,QAAK;AAAC,kBAAIC,KAAEF,GAAEC,EAAC;AAAE,kBAAG,YAAU,OAAOC,IAAE;AAAC,oBAAIC,KAAE,EAAED,EAAC;AAAE,gBAAAC,OAAID,OAAI,EAAEF,EAAC,MAAIA,GAAEC,EAAC,IAAEE,KAAGD,KAAEC;AAAA,cAAE;AAAC,cAAAJ,GAAEG,EAAC,IAAE;AAAA,YAAE;AAAC,mBAAOH;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAE;AAAC,gBAAIC,IAAEC,KAAE,EAAE,IAAI;AAAE,iBAAID,MAAKD;AAAE,gBAAE,GAAEA,IAAE,CAACC,EAAC,CAAC,MAAIC,GAAED,EAAC,IAAED,GAAEC,EAAC;AAAG,mBAAOC;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAEC,IAAE;AAAC,mBAAK,SAAOD,MAAG;AAAC,kBAAIE,KAAE,EAAEF,IAAEC,EAAC;AAAE,kBAAGC,IAAE;AAAC,oBAAGA,GAAE;AAAI,yBAAO,EAAEA,GAAE,GAAG;AAAE,oBAAG,cAAY,OAAOA,GAAE;AAAM,yBAAO,EAAEA,GAAE,KAAK;AAAA,cAAC;AAAC,cAAAF,KAAE,EAAEA,EAAC;AAAA,YAAC;AAAC,qBAASG,GAAEH,IAAE;AAAC,qBAAO,QAAQ,KAAK,sBAAqBA,EAAC,GAAE;AAAA,YAAI;AAAC,mBAAOG;AAAA,UAAC;AAAC,cAAI,IAAE,EAAE,CAAC,KAAI,QAAO,WAAU,WAAU,QAAO,WAAU,SAAQ,SAAQ,KAAI,OAAM,OAAM,OAAM,SAAQ,cAAa,QAAO,MAAK,UAAS,UAAS,WAAU,UAAS,QAAO,QAAO,OAAM,YAAW,WAAU,QAAO,YAAW,MAAK,aAAY,OAAM,WAAU,OAAM,UAAS,OAAM,OAAM,MAAK,MAAK,WAAU,MAAK,YAAW,cAAa,UAAS,QAAO,UAAS,QAAO,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,QAAO,UAAS,UAAS,MAAK,QAAO,KAAI,OAAM,SAAQ,OAAM,OAAM,SAAQ,UAAS,MAAK,QAAO,OAAM,QAAO,WAAU,QAAO,YAAW,SAAQ,OAAM,QAAO,MAAK,YAAW,UAAS,UAAS,KAAI,WAAU,OAAM,YAAW,KAAI,MAAK,MAAK,QAAO,KAAI,QAAO,WAAU,UAAS,UAAS,SAAQ,UAAS,UAAS,QAAO,UAAS,UAAS,SAAQ,OAAM,WAAU,OAAM,SAAQ,SAAQ,MAAK,YAAW,YAAW,SAAQ,MAAK,SAAQ,QAAO,MAAK,SAAQ,MAAK,KAAI,MAAK,OAAM,SAAQ,KAAK,CAAC,GAAE,IAAE,EAAE,CAAC,OAAM,KAAI,YAAW,eAAc,gBAAe,gBAAe,iBAAgB,oBAAmB,UAAS,YAAW,QAAO,QAAO,WAAU,UAAS,QAAO,KAAI,SAAQ,YAAW,SAAQ,SAAQ,QAAO,kBAAiB,UAAS,QAAO,YAAW,SAAQ,QAAO,WAAU,WAAU,YAAW,kBAAiB,QAAO,QAAO,SAAQ,UAAS,UAAS,QAAO,YAAW,SAAQ,QAAO,SAAQ,QAAO,OAAO,CAAC,GAAE,IAAE,EAAE,CAAC,WAAU,iBAAgB,uBAAsB,eAAc,oBAAmB,qBAAoB,qBAAoB,kBAAiB,WAAU,WAAU,WAAU,WAAU,WAAU,kBAAiB,WAAU,WAAU,eAAc,gBAAe,YAAW,gBAAe,sBAAqB,eAAc,UAAS,cAAc,CAAC,GAAE,IAAE,EAAE,CAAC,WAAU,iBAAgB,UAAS,WAAU,gBAAe,aAAY,oBAAmB,kBAAiB,iBAAgB,iBAAgB,iBAAgB,SAAQ,aAAY,QAAO,gBAAe,aAAY,WAAU,iBAAgB,UAAS,OAAM,cAAa,WAAU,KAAK,CAAC,GAAE,IAAE,EAAE,CAAC,QAAO,YAAW,UAAS,WAAU,SAAQ,UAAS,MAAK,cAAa,iBAAgB,MAAK,MAAK,SAAQ,WAAU,YAAW,SAAQ,QAAO,MAAK,UAAS,SAAQ,UAAS,QAAO,QAAO,WAAU,UAAS,OAAM,SAAQ,OAAM,UAAS,YAAY,CAAC,GAAE,IAAE,EAAE,CAAC,WAAU,eAAc,cAAa,YAAW,aAAY,WAAU,WAAU,UAAS,UAAS,SAAQ,aAAY,cAAa,kBAAiB,eAAc,MAAM,CAAC,GAAE,IAAE,EAAE,CAAC,OAAO,CAAC,GAAE,IAAE,EAAE,CAAC,UAAS,UAAS,SAAQ,OAAM,kBAAiB,gBAAe,wBAAuB,YAAW,cAAa,WAAU,UAAS,WAAU,eAAc,eAAc,WAAU,QAAO,SAAQ,SAAQ,SAAQ,QAAO,WAAU,YAAW,gBAAe,UAAS,eAAc,YAAW,YAAW,WAAU,OAAM,YAAW,2BAA0B,yBAAwB,YAAW,aAAY,WAAU,gBAAe,QAAO,OAAM,WAAU,UAAS,UAAS,QAAO,QAAO,YAAW,MAAK,aAAY,aAAY,SAAQ,QAAO,SAAQ,QAAO,QAAO,WAAU,QAAO,OAAM,OAAM,aAAY,SAAQ,UAAS,OAAM,aAAY,YAAW,SAAQ,QAAO,SAAQ,WAAU,cAAa,UAAS,QAAO,WAAU,WAAU,eAAc,eAAc,UAAS,WAAU,WAAU,cAAa,YAAW,OAAM,YAAW,OAAM,YAAW,QAAO,QAAO,WAAU,cAAa,SAAQ,YAAW,SAAQ,QAAO,SAAQ,QAAO,WAAU,SAAQ,OAAM,UAAS,QAAO,SAAQ,WAAU,YAAW,SAAQ,aAAY,QAAO,UAAS,UAAS,SAAQ,SAAQ,SAAQ,MAAM,CAAC,GAAE,IAAE,EAAE,CAAC,iBAAgB,cAAa,YAAW,sBAAqB,UAAS,iBAAgB,iBAAgB,WAAU,iBAAgB,kBAAiB,SAAQ,QAAO,MAAK,SAAQ,QAAO,iBAAgB,aAAY,aAAY,SAAQ,uBAAsB,+BAA8B,iBAAgB,mBAAkB,MAAK,MAAK,KAAI,MAAK,MAAK,mBAAkB,aAAY,WAAU,WAAU,OAAM,YAAW,aAAY,OAAM,QAAO,gBAAe,aAAY,UAAS,eAAc,eAAc,iBAAgB,eAAc,aAAY,oBAAmB,gBAAe,cAAa,gBAAe,eAAc,MAAK,MAAK,MAAK,MAAK,cAAa,YAAW,iBAAgB,qBAAoB,UAAS,QAAO,MAAK,mBAAkB,MAAK,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,WAAU,aAAY,cAAa,YAAW,QAAO,gBAAe,kBAAiB,gBAAe,oBAAmB,kBAAiB,SAAQ,cAAa,cAAa,gBAAe,gBAAe,eAAc,eAAc,oBAAmB,aAAY,OAAM,QAAO,SAAQ,UAAS,QAAO,OAAM,QAAO,cAAa,UAAS,YAAW,WAAU,SAAQ,UAAS,eAAc,UAAS,YAAW,eAAc,QAAO,cAAa,uBAAsB,oBAAmB,gBAAe,UAAS,iBAAgB,uBAAsB,kBAAiB,KAAI,MAAK,MAAK,UAAS,QAAO,QAAO,eAAc,aAAY,WAAU,UAAS,UAAS,SAAQ,QAAO,mBAAkB,oBAAmB,oBAAmB,gBAAe,eAAc,gBAAe,eAAc,cAAa,gBAAe,oBAAmB,qBAAoB,kBAAiB,mBAAkB,qBAAoB,kBAAiB,UAAS,gBAAe,SAAQ,gBAAe,kBAAiB,YAAW,WAAU,WAAU,aAAY,oBAAmB,eAAc,mBAAkB,kBAAiB,cAAa,QAAO,MAAK,MAAK,WAAU,UAAS,WAAU,cAAa,WAAU,cAAa,iBAAgB,iBAAgB,SAAQ,gBAAe,QAAO,gBAAe,oBAAmB,oBAAmB,KAAI,MAAK,MAAK,SAAQ,KAAI,MAAK,MAAK,KAAI,YAAY,CAAC,GAAE,IAAE,EAAE,CAAC,UAAS,eAAc,SAAQ,YAAW,SAAQ,gBAAe,eAAc,cAAa,cAAa,SAAQ,OAAM,WAAU,gBAAe,YAAW,SAAQ,SAAQ,UAAS,QAAO,MAAK,WAAU,UAAS,iBAAgB,UAAS,UAAS,kBAAiB,aAAY,YAAW,eAAc,WAAU,WAAU,iBAAgB,YAAW,YAAW,QAAO,YAAW,YAAW,cAAa,WAAU,UAAS,UAAS,eAAc,iBAAgB,wBAAuB,aAAY,aAAY,cAAa,YAAW,kBAAiB,kBAAiB,aAAY,WAAU,SAAQ,OAAO,CAAC,GAAE,IAAE,EAAE,CAAC,cAAa,UAAS,eAAc,aAAY,aAAa,CAAC,GAAE,IAAE,EAAE,2BAA2B,GAAE,IAAE,EAAE,uBAAuB,GAAE,IAAE,EAAE,4BAA4B,GAAE,IAAE,EAAE,gBAAgB,GAAE,IAAE,EAAE,uFAAuF,GAAE,IAAE,EAAE,uBAAuB,GAAE,IAAE,EAAE,6DAA6D,GAAE,KAAG,EAAE,SAAS,GAAE,KAAG,WAAU;AAAC,mBAAM,eAAa,OAAO,SAAO,OAAK;AAAA,UAAM,GAAE,KAAG,SAASF,IAAEC,IAAE;AAAC,gBAAG,aAAWF,GAAEC,EAAC,KAAG,cAAY,OAAOA,GAAE;AAAa,qBAAO;AAAK,gBAAIE,KAAE,MAAKC,KAAE;AAAwB,YAAAF,GAAE,iBAAeA,GAAE,cAAc,aAAaE,EAAC,MAAID,KAAED,GAAE,cAAc,aAAaE,EAAC;AAAG,gBAAIC,KAAE,eAAaF,KAAE,MAAIA,KAAE;AAAI,gBAAG;AAAC,qBAAOF,GAAE,aAAaI,IAAE,EAAC,YAAW,SAASL,IAAE;AAAC,uBAAOA;AAAA,cAAC,EAAC,CAAC;AAAA,YAAC,SAAOA,IAAE;AAAC,qBAAO,QAAQ,KAAK,yBAAuBK,KAAE,wBAAwB,GAAE;AAAA,YAAI;AAAA,UAAC;AAAE,mBAAS,KAAI;AAAC,gBAAIJ,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAG,GAAEC,KAAE,SAASF,IAAE;AAAC,qBAAO,GAAGA,EAAC;AAAA,YAAC;AAAE,gBAAGE,GAAE,UAAQ,SAAQA,GAAE,UAAQ,CAAC,GAAE,CAACD,MAAG,CAACA,GAAE,YAAU,MAAIA,GAAE,SAAS;AAAS,qBAAOC,GAAE,cAAY,OAAGA;AAAE,gBAAIC,KAAEF,GAAE,UAASI,KAAEJ,GAAE,UAASK,KAAEL,GAAE,kBAAiBM,KAAEN,GAAE,qBAAoBO,KAAEP,GAAE,MAAKQ,KAAER,GAAE,SAAQS,KAAET,GAAE,YAAWU,KAAEV,GAAE,cAAaW,KAAE,WAASD,KAAEV,GAAE,gBAAcA,GAAE,kBAAgBU,IAAEE,KAAEZ,GAAE,iBAAgBa,KAAEb,GAAE,WAAUc,KAAEd,GAAE,cAAae,KAAEP,GAAE,WAAUQ,KAAE,EAAED,IAAE,WAAW,GAAEE,KAAE,EAAEF,IAAE,aAAa,GAAEG,KAAE,EAAEH,IAAE,YAAY,GAAEI,KAAE,EAAEJ,IAAE,YAAY;AAAE,gBAAG,cAAY,OAAOT,IAAE;AAAC,kBAAIc,KAAEhB,GAAE,cAAc,UAAU;AAAE,cAAAgB,GAAE,WAASA,GAAE,QAAQ,kBAAgBhB,KAAEgB,GAAE,QAAQ;AAAA,YAAc;AAAC,gBAAI,KAAG,GAAGN,IAAEZ,EAAC,GAAE,KAAG,KAAG,GAAG,WAAW,EAAE,IAAE,IAAG,KAAGE,IAAE,KAAG,GAAG,gBAAe,KAAG,GAAG,oBAAmB,KAAG,GAAG,wBAAuB,KAAG,GAAG,sBAAqB,KAAGF,GAAE,YAAW,KAAG,CAAC;AAAE,gBAAG;AAAC,mBAAG,EAAEE,EAAC,EAAE,eAAaA,GAAE,eAAa,CAAC;AAAA,YAAC,SAAOL,IAAE;AAAA,YAAC;AAAC,gBAAI,KAAG,CAAC;AAAE,YAAAE,GAAE,cAAY,cAAY,OAAOkB,MAAG,MAAI,WAAS,GAAG,sBAAoB,MAAI;AAAG,gBAAI,IAAG,IAAG,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,MAAK,KAAG,EAAE,CAAC,GAAE,CAAC,EAAE,OAAO,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC,GAAE,KAAG,MAAK,KAAG,EAAE,CAAC,GAAE,CAAC,EAAE,OAAO,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC,GAAE,KAAG,OAAO,KAAK,OAAO,OAAO,MAAK,EAAC,cAAa,EAAC,UAAS,MAAG,cAAa,OAAG,YAAW,MAAG,OAAM,KAAI,GAAE,oBAAmB,EAAC,UAAS,MAAG,cAAa,OAAG,YAAW,MAAG,OAAM,KAAI,GAAE,gCAA+B,EAAC,UAAS,MAAG,cAAa,OAAG,YAAW,MAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,KAAG,MAAK,KAAG,MAAK,KAAG,MAAG,KAAG,MAAG,KAAG,OAAG,KAAG,OAAG,KAAG,OAAG,KAAG,OAAG,KAAG,OAAG,KAAG,OAAG,KAAG,OAAG,KAAG,OAAG,KAAG,MAAG,KAAG,MAAG,KAAG,OAAG,KAAG,CAAC,GAAE,KAAG,MAAK,KAAG,EAAE,CAAC,GAAE,CAAC,kBAAiB,SAAQ,YAAW,QAAO,iBAAgB,QAAO,UAAS,QAAO,MAAK,MAAK,MAAK,MAAK,SAAQ,WAAU,YAAW,YAAW,aAAY,UAAS,SAAQ,OAAM,YAAW,SAAQ,SAAQ,SAAQ,KAAK,CAAC,GAAE,KAAG,MAAK,KAAG,EAAE,CAAC,GAAE,CAAC,SAAQ,SAAQ,OAAM,UAAS,SAAQ,OAAO,CAAC,GAAE,KAAG,MAAK,KAAG,EAAE,CAAC,GAAE,CAAC,OAAM,SAAQ,OAAM,MAAK,SAAQ,QAAO,WAAU,eAAc,QAAO,WAAU,SAAQ,SAAQ,SAAQ,OAAO,CAAC,GAAE,KAAG,sCAAqC,KAAG,8BAA6B,KAAG,gCAA+B,KAAG,IAAG,KAAG,OAAG,KAAG,CAAC,yBAAwB,WAAW,GAAE,KAAG,aAAY,KAAG,MAAK,KAAGf,GAAE,cAAc,MAAM,GAAE,KAAG,SAASL,IAAE;AAAC,qBAAOA,cAAa,UAAQA,cAAa;AAAA,YAAQ,GAAE,KAAG,SAASC,IAAE;AAAC,oBAAI,OAAKA,OAAIA,MAAG,aAAWD,GAAEC,EAAC,MAAIA,KAAE,CAAC,IAAGA,KAAE,EAAEA,EAAC,GAAE,KAAG,kBAAiBA,KAAE,EAAE,CAAC,GAAEA,GAAE,YAAY,IAAE,IAAG,KAAG,kBAAiBA,KAAE,EAAE,CAAC,GAAEA,GAAE,YAAY,IAAE,IAAG,KAAG,uBAAsBA,KAAE,EAAE,EAAE,EAAE,GAAEA,GAAE,iBAAiB,IAAE,IAAG,KAAG,uBAAsBA,KAAE,EAAE,EAAE,EAAE,GAAEA,GAAE,iBAAiB,IAAE,IAAG,KAAG,qBAAoBA,KAAE,EAAE,CAAC,GAAEA,GAAE,eAAe,IAAE,IAAG,KAAG,iBAAgBA,KAAE,EAAE,CAAC,GAAEA,GAAE,WAAW,IAAE,CAAC,GAAE,KAAG,iBAAgBA,KAAE,EAAE,CAAC,GAAEA,GAAE,WAAW,IAAE,CAAC,GAAE,KAAG,kBAAiBA,MAAGA,GAAE,cAAa,KAAG,UAAKA,GAAE,iBAAgB,KAAG,UAAKA,GAAE,iBAAgB,KAAGA,GAAE,2BAAyB,OAAG,KAAGA,GAAE,sBAAoB,OAAG,KAAGA,GAAE,kBAAgB,OAAG,KAAGA,GAAE,cAAY,OAAG,KAAGA,GAAE,uBAAqB,OAAG,KAAGA,GAAE,uBAAqB,OAAG,KAAGA,GAAE,cAAY,OAAG,KAAG,UAAKA,GAAE,cAAa,KAAG,UAAKA,GAAE,cAAa,KAAGA,GAAE,YAAU,OAAG,KAAGA,GAAE,sBAAoB,IAAG,KAAGA,GAAE,aAAW,IAAGA,GAAE,2BAAyB,GAAGA,GAAE,wBAAwB,YAAY,MAAI,GAAG,eAAaA,GAAE,wBAAwB,eAAcA,GAAE,2BAAyB,GAAGA,GAAE,wBAAwB,kBAAkB,MAAI,GAAG,qBAAmBA,GAAE,wBAAwB,qBAAoBA,GAAE,2BAAyB,aAAW,OAAOA,GAAE,wBAAwB,mCAAiC,GAAG,iCAA+BA,GAAE,wBAAwB,iCAAgC,KAAG,KAAG,OAAK,GAAG,QAAQA,GAAE,iBAAiB,IAAE,KAAGA,GAAE,mBAAkB,KAAG,4BAA0B,KAAG,SAASD,IAAE;AAAC,uBAAOA;AAAA,cAAC,IAAE,GAAE,OAAK,KAAG,QAAI,OAAK,KAAG,OAAI,OAAK,KAAG,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAE,KAAG,CAAC,GAAE,SAAK,GAAG,SAAO,EAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC,IAAG,SAAK,GAAG,QAAM,EAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC,IAAG,SAAK,GAAG,eAAa,EAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC,IAAG,SAAK,GAAG,WAAS,EAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC,KAAIC,GAAE,aAAW,OAAK,OAAK,KAAG,EAAE,EAAE,IAAG,EAAE,IAAGA,GAAE,QAAQ,IAAGA,GAAE,aAAW,OAAK,OAAK,KAAG,EAAE,EAAE,IAAG,EAAE,IAAGA,GAAE,QAAQ,IAAGA,GAAE,qBAAmB,EAAE,IAAGA,GAAE,iBAAiB,GAAEA,GAAE,oBAAkB,OAAK,OAAK,KAAG,EAAE,EAAE,IAAG,EAAE,IAAGA,GAAE,eAAe,IAAG,OAAK,GAAG,OAAO,IAAE,OAAI,MAAI,EAAE,IAAG,CAAC,QAAO,QAAO,MAAM,CAAC,GAAE,GAAG,UAAQ,EAAE,IAAG,CAAC,OAAO,CAAC,GAAE,OAAO,GAAG,QAAO,KAAG,EAAEA,EAAC,GAAE,KAAGA;AAAA,YAAE,GAAE,KAAG,EAAE,CAAC,GAAE,CAAC,MAAK,MAAK,MAAK,MAAK,OAAO,CAAC,GAAE,KAAG,EAAE,CAAC,GAAE,CAAC,iBAAgB,QAAO,SAAQ,gBAAgB,CAAC,GAAE,KAAG,EAAE,CAAC,GAAE,CAAC,SAAQ,SAAQ,QAAO,KAAI,QAAQ,CAAC,GAAE,KAAG,EAAE,CAAC,GAAE,CAAC;AAAE,cAAE,IAAG,CAAC,GAAE,EAAE,IAAG,CAAC;AAAE,gBAAI,KAAG,EAAE,CAAC,GAAE,CAAC;AAAE,cAAE,IAAG,CAAC;AAAE,gBAAI,KAAG,SAASD,IAAE;AAAC,kBAAIC,KAAEmB,GAAEpB,EAAC;AAAE,cAAAC,MAAGA,GAAE,YAAUA,KAAE,EAAC,cAAa,IAAG,SAAQ,WAAU;AAAG,kBAAIC,KAAE,EAAEF,GAAE,OAAO,GAAEG,KAAE,EAAEF,GAAE,OAAO;AAAE,qBAAOD,GAAE,iBAAe,KAAGC,GAAE,iBAAe,KAAG,UAAQC,KAAED,GAAE,iBAAe,KAAG,UAAQC,OAAI,qBAAmBC,MAAG,GAAGA,EAAC,KAAG,QAAQ,GAAGD,EAAC,CAAC,IAAEF,GAAE,iBAAe,KAAGC,GAAE,iBAAe,KAAG,WAASC,KAAED,GAAE,iBAAe,KAAG,WAASC,MAAG,GAAGC,EAAC,IAAE,QAAQ,GAAGD,EAAC,CAAC,IAAEF,GAAE,iBAAe,MAAI,EAAEC,GAAE,iBAAe,MAAI,CAAC,GAAGE,EAAC,MAAI,EAAEF,GAAE,iBAAe,MAAI,CAAC,GAAGE,EAAC,MAAI,CAAC,GAAGD,EAAC,MAAI,GAAGA,EAAC,KAAG,CAAC,GAAGA,EAAC;AAAA,YAAE,GAAE,KAAG,SAASF,IAAE;AAAC,gBAAEE,GAAE,SAAQ,EAAC,SAAQF,GAAC,CAAC;AAAE,kBAAG;AAAC,gBAAAA,GAAE,WAAW,YAAYA,EAAC;AAAA,cAAC,SAAOC,IAAE;AAAC,oBAAG;AAAC,kBAAAD,GAAE,YAAU;AAAA,gBAAE,SAAOC,IAAE;AAAC,kBAAAD,GAAE,OAAO;AAAA,gBAAC;AAAA,cAAC;AAAA,YAAC,GAAE,KAAG,SAASA,IAAEC,IAAE;AAAC,kBAAG;AAAC,kBAAEC,GAAE,SAAQ,EAAC,WAAUD,GAAE,iBAAiBD,EAAC,GAAE,MAAKC,GAAC,CAAC;AAAA,cAAC,SAAOD,IAAE;AAAC,kBAAEE,GAAE,SAAQ,EAAC,WAAU,MAAK,MAAKD,GAAC,CAAC;AAAA,cAAC;AAAC,kBAAGA,GAAE,gBAAgBD,EAAC,GAAE,SAAOA,MAAG,CAAC,GAAGA,EAAC;AAAE,oBAAG,MAAI;AAAG,sBAAG;AAAC,uBAAGC,EAAC;AAAA,kBAAC,SAAOD,IAAE;AAAA,kBAAC;AAAA;AAAM,sBAAG;AAAC,oBAAAC,GAAE,aAAaD,IAAE,EAAE;AAAA,kBAAC,SAAOA,IAAE;AAAA,kBAAC;AAAA,YAAC,GAAE,KAAG,SAASA,IAAE;AAAC,kBAAIC,IAAEC;AAAE,kBAAG;AAAG,gBAAAF,KAAE,sBAAoBA;AAAA,mBAAM;AAAC,oBAAIG,KAAE,EAAEH,IAAE,aAAa;AAAE,gBAAAE,KAAEC,MAAGA,GAAE,CAAC;AAAA,cAAC;AAAC,0CAA0B,OAAKH,KAAE,mEAAiEA,KAAE;AAAkB,kBAAII,KAAE,KAAG,GAAG,WAAWJ,EAAC,IAAEA;AAAE,kBAAG,OAAK;AAAG,oBAAG;AAAC,kBAAAC,KAAG,IAAIa,KAAG,gBAAgBV,IAAE,EAAE;AAAA,gBAAC,SAAOJ,IAAE;AAAA,gBAAC;AAAC,kBAAG,CAACC,MAAG,CAACA,GAAE,iBAAgB;AAAC,gBAAAA,KAAE,GAAG,eAAe,IAAG,YAAW,IAAI;AAAE,oBAAG;AAAC,kBAAAA,GAAE,gBAAgB,YAAU,KAAG,KAAGG;AAAA,gBAAC,SAAOJ,IAAE;AAAA,gBAAC;AAAA,cAAC;AAAC,kBAAIM,KAAEL,GAAE,QAAMA,GAAE;AAAgB,qBAAOD,MAAGE,MAAGI,GAAE,aAAaD,GAAE,eAAeH,EAAC,GAAEI,GAAE,WAAW,CAAC,KAAG,IAAI,GAAE,OAAK,KAAG,GAAG,KAAKL,IAAE,KAAG,SAAO,MAAM,EAAE,CAAC,IAAE,KAAGA,GAAE,kBAAgBK;AAAA,YAAC,GAAE,KAAG,SAASN,IAAE;AAAC,qBAAO,GAAG,KAAKA,GAAE,iBAAeA,IAAEA,IAAEU,GAAE,eAAaA,GAAE,eAAaA,GAAE,WAAU,MAAK,KAAE;AAAA,YAAC,GAAE,KAAG,SAASV,IAAE;AAAC,qBAAOA,cAAaa,OAAI,YAAU,OAAOb,GAAE,YAAU,YAAU,OAAOA,GAAE,eAAa,cAAY,OAAOA,GAAE,eAAa,EAAEA,GAAE,sBAAsBY,OAAI,cAAY,OAAOZ,GAAE,mBAAiB,cAAY,OAAOA,GAAE,gBAAc,YAAU,OAAOA,GAAE,gBAAc,cAAY,OAAOA,GAAE;AAAA,YAAa,GAAE,KAAG,SAASC,IAAE;AAAC,qBAAM,aAAWD,GAAEQ,EAAC,IAAEP,cAAaO,KAAEP,MAAG,aAAWD,GAAEC,EAAC,KAAG,YAAU,OAAOA,GAAE,YAAU,YAAU,OAAOA,GAAE;AAAA,YAAQ,GAAE,KAAG,SAASD,IAAEC,IAAEE,IAAE;AAAC,iBAAGH,EAAC,KAAG,EAAE,GAAGA,EAAC,GAAG,SAASA,IAAE;AAAC,gBAAAA,GAAE,KAAKE,IAAED,IAAEE,IAAE,EAAE;AAAA,cAAC,CAAE;AAAA,YAAC,GAAE,KAAG,SAASH,IAAE;AAAC,kBAAIC;AAAE,kBAAG,GAAG,0BAAyBD,IAAE,IAAI,GAAE,GAAGA,EAAC;AAAE,uBAAO,GAAGA,EAAC,GAAE;AAAG,kBAAG,EAAE,mBAAkBA,GAAE,QAAQ;AAAE,uBAAO,GAAGA,EAAC,GAAE;AAAG,kBAAIG,KAAE,GAAGH,GAAE,QAAQ;AAAE,kBAAG,GAAG,uBAAsBA,IAAE,EAAC,SAAQG,IAAE,aAAY,GAAE,CAAC,GAAEH,GAAE,cAAc,KAAG,CAAC,GAAGA,GAAE,iBAAiB,MAAI,CAAC,GAAGA,GAAE,OAAO,KAAG,CAAC,GAAGA,GAAE,QAAQ,iBAAiB,MAAI,EAAE,WAAUA,GAAE,SAAS,KAAG,EAAE,WAAUA,GAAE,WAAW;AAAE,uBAAO,GAAGA,EAAC,GAAE;AAAG,kBAAG,aAAWG,MAAG,EAAE,cAAaH,GAAE,SAAS;AAAE,uBAAO,GAAGA,EAAC,GAAE;AAAG,kBAAG,CAAC,GAAGG,EAAC,KAAG,GAAGA,EAAC,GAAE;AAAC,oBAAG,CAAC,GAAGA,EAAC,KAAG,GAAGA,EAAC,GAAE;AAAC,sBAAG,GAAG,wBAAwB,UAAQ,EAAE,GAAG,cAAaA,EAAC;AAAE,2BAAM;AAAG,sBAAG,GAAG,wBAAwB,YAAU,GAAG,aAAaA,EAAC;AAAE,2BAAM;AAAA,gBAAE;AAAC,oBAAG,MAAI,CAAC,GAAGA,EAAC,GAAE;AAAC,sBAAIC,KAAEgB,GAAEpB,EAAC,KAAGA,GAAE,YAAWK,KAAEc,GAAEnB,EAAC,KAAGA,GAAE;AAAW,sBAAGK,MAAGD;AAAE,6BAAQE,KAAED,GAAE,SAAO,GAAEC,MAAG,GAAE,EAAEA;AAAE,sBAAAF,GAAE,aAAaa,GAAEZ,GAAEC,EAAC,GAAE,IAAE,GAAEY,GAAElB,EAAC,CAAC;AAAA,gBAAC;AAAC,uBAAO,GAAGA,EAAC,GAAE;AAAA,cAAE;AAAC,qBAAOA,cAAaS,MAAG,CAAC,GAAGT,EAAC,KAAG,GAAGA,EAAC,GAAE,QAAI,eAAaG,MAAG,cAAYA,MAAG,CAAC,EAAE,wBAAuBH,GAAE,SAAS,KAAG,MAAI,MAAIA,GAAE,aAAWC,KAAED,GAAE,aAAYC,KAAE,EAAEA,IAAE,IAAG,GAAG,GAAEA,KAAE,EAAEA,IAAE,IAAG,GAAG,GAAED,GAAE,gBAAcC,OAAI,EAAEC,GAAE,SAAQ,EAAC,SAAQF,GAAE,UAAU,EAAC,CAAC,GAAEA,GAAE,cAAYC,MAAI,GAAG,yBAAwBD,IAAE,IAAI,GAAE,UAAK,GAAGA,EAAC,GAAE;AAAA,YAAG,GAAE,KAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,kBAAG,OAAK,SAAOD,MAAG,WAASA,QAAKC,MAAKG,MAAGH,MAAK;AAAI,uBAAM;AAAG,kBAAG,MAAI,CAAC,GAAGD,EAAC,KAAG,EAAE,IAAGA,EAAC;AAAE;AAAA,uBAAS,MAAI,EAAE,IAAGA,EAAC;AAAE;AAAA,uBAAS,CAAC,GAAGA,EAAC,KAAG,GAAGA,EAAC,GAAE;AAAC,oBAAG,EAAE,GAAGD,EAAC,MAAI,GAAG,wBAAwB,UAAQ,EAAE,GAAG,cAAaA,EAAC,KAAG,GAAG,wBAAwB,YAAU,GAAG,aAAaA,EAAC,OAAK,GAAG,8BAA8B,UAAQ,EAAE,GAAG,oBAAmBC,EAAC,KAAG,GAAG,8BAA8B,YAAU,GAAG,mBAAmBA,EAAC,MAAI,SAAOA,MAAG,GAAG,mCAAiC,GAAG,wBAAwB,UAAQ,EAAE,GAAG,cAAaC,EAAC,KAAG,GAAG,wBAAwB,YAAU,GAAG,aAAaA,EAAC;AAAI,yBAAM;AAAA,cAAE,WAAS,GAAGD,EAAC;AAAE;AAAA,uBAAS,EAAE,IAAG,EAAEC,IAAE,IAAG,EAAE,CAAC;AAAE;AAAA,uBAAS,UAAQD,MAAG,iBAAeA,MAAG,WAASA,MAAG,aAAWD,MAAG,MAAI,EAAEE,IAAE,OAAO,KAAG,CAAC,GAAGF,EAAC;AAAE,oBAAG,MAAI,CAAC,EAAE,IAAG,EAAEE,IAAE,IAAG,EAAE,CAAC;AAAE;AAAA,yBAASA;AAAE,yBAAM;AAAA;AAAG,qBAAM;AAAA,YAAE,GAAE,KAAG,SAASF,IAAE;AAAC,qBAAOA,GAAE,QAAQ,GAAG,IAAE;AAAA,YAAC,GAAE,KAAG,SAASA,IAAE;AAAC,kBAAIC,IAAEE,IAAEC,IAAEC;AAAE,iBAAG,4BAA2BL,IAAE,IAAI;AAAE,kBAAIM,KAAEN,GAAE;AAAW,kBAAGM,IAAE;AAAC,oBAAIC,KAAE,EAAC,UAAS,IAAG,WAAU,IAAG,UAAS,MAAG,mBAAkB,GAAE;AAAE,qBAAIF,KAAEC,GAAE,QAAOD,QAAK;AAAC,sBAAIG,KAAEP,KAAEK,GAAED,EAAC,GAAEI,KAAED,GAAE,MAAKE,KAAEF,GAAE;AAAa,sBAAGL,KAAE,YAAUM,KAAER,GAAE,QAAM,EAAEA,GAAE,KAAK,GAAEG,KAAE,GAAGK,EAAC,GAAEF,GAAE,WAASH,IAAEG,GAAE,YAAUJ,IAAEI,GAAE,WAAS,MAAGA,GAAE,gBAAc,QAAO,GAAG,yBAAwBP,IAAEO,EAAC,GAAEJ,KAAEI,GAAE,WAAU,CAACA,GAAE,kBAAgB,GAAGE,IAAET,EAAC,GAAEO,GAAE;AAAU,wBAAG,EAAE,QAAOJ,EAAC;AAAE,yBAAGM,IAAET,EAAC;AAAA,yBAAM;AAAC,6BAAKG,KAAE,EAAEA,IAAE,IAAG,GAAG,GAAEA,KAAE,EAAEA,IAAE,IAAG,GAAG;AAAG,0BAAIQ,KAAE,GAAGX,GAAE,QAAQ;AAAE,0BAAG,GAAGW,IAAEP,IAAED,EAAC;AAAE,4BAAG;AAAC,0BAAAO,KAAEV,GAAE,eAAeU,IAAED,IAAEN,EAAC,IAAEH,GAAE,aAAaS,IAAEN,EAAC,GAAE,EAAED,GAAE,OAAO;AAAA,wBAAC,SAAOF,IAAE;AAAA,wBAAC;AAAA,oBAAC;AAAA,gBAAC;AAAC,mBAAG,2BAA0BA,IAAE,IAAI;AAAA,cAAC;AAAA,YAAC,GAAE,KAAG,SAASA,GAAEC,IAAE;AAAC,kBAAIC,IAAEC,KAAE,GAAGF,EAAC;AAAE,mBAAI,GAAG,2BAA0BA,IAAE,IAAI,GAAEC,KAAEC,GAAE,SAAS;AAAG,mBAAG,0BAAyBD,IAAE,IAAI,GAAE,GAAGA,EAAC,MAAIA,GAAE,mBAAmBI,MAAGN,GAAEE,GAAE,OAAO,GAAE,GAAGA,EAAC;AAAG,iBAAG,0BAAyBD,IAAE,IAAI;AAAA,YAAC;AAAE,mBAAOC,GAAE,WAAS,SAASE,IAAEC,IAAE;AAAC,kBAAIE,IAAEE,IAAEC,IAAEC,IAAEC;AAAE,mBAAI,KAAG,CAACR,QAAKA,KAAE,UAAe,YAAU,OAAOA,MAAG,CAAC,GAAGA,EAAC,GAAE;AAAC,oBAAG,cAAY,OAAOA,GAAE;AAAS,wBAAM,EAAE,4BAA4B;AAAE,oBAAG,YAAU,QAAOA,KAAEA,GAAE,SAAS;AAAG,wBAAM,EAAE,iCAAiC;AAAA,cAAC;AAAC,kBAAG,CAACF,GAAE,aAAY;AAAC,oBAAG,aAAWF,GAAEC,GAAE,YAAY,KAAG,cAAY,OAAOA,GAAE,cAAa;AAAC,sBAAG,YAAU,OAAOG;AAAE,2BAAOH,GAAE,aAAaG,EAAC;AAAE,sBAAG,GAAGA,EAAC;AAAE,2BAAOH,GAAE,aAAaG,GAAE,SAAS;AAAA,gBAAC;AAAC,uBAAOA;AAAA,cAAC;AAAC,kBAAG,MAAI,GAAGC,EAAC,GAAEH,GAAE,UAAQ,CAAC,GAAE,YAAU,OAAOE,OAAI,KAAG,QAAI,IAAG;AAAC,oBAAGA,GAAE,UAAS;AAAC,sBAAIS,KAAE,GAAGT,GAAE,QAAQ;AAAE,sBAAG,CAAC,GAAGS,EAAC,KAAG,GAAGA,EAAC;AAAE,0BAAM,EAAE,yDAAyD;AAAA,gBAAC;AAAA,cAAC,WAAST,cAAaI;AAAE,uBAAKC,MAAGF,KAAE,GAAG,SAAe,GAAG,cAAc,WAAWH,IAAE,IAAE,GAAG,YAAU,WAASK,GAAE,YAAU,WAASA,GAAE,WAASF,KAAEE,KAAEF,GAAE,YAAYE,EAAC;AAAA,mBAAM;AAAC,oBAAG,CAAC,MAAI,CAAC,MAAI,CAAC,MAAI,OAAKL,GAAE,QAAQ,GAAG;AAAE,yBAAO,MAAI,KAAG,GAAG,WAAWA,EAAC,IAAEA;AAAE,oBAAG,EAAEG,KAAE,GAAGH,EAAC;AAAG,yBAAO,KAAG,OAAK,KAAG,KAAG;AAAA,cAAE;AAAC,cAAAG,MAAG,MAAI,GAAGA,GAAE,UAAU;AAAE,uBAAQO,KAAE,GAAG,KAAGV,KAAEG,EAAC,GAAEG,KAAEI,GAAE,SAAS;AAAG,sBAAIJ,GAAE,YAAUA,OAAIC,MAAG,GAAGD,EAAC,MAAIA,GAAE,mBAAmBJ,MAAG,GAAGI,GAAE,OAAO,GAAE,GAAGA,EAAC,GAAEC,KAAED;AAAG,kBAAGC,KAAE,MAAK;AAAG,uBAAOP;AAAE,kBAAG,IAAG;AAAC,oBAAG;AAAG,uBAAIQ,KAAE,GAAG,KAAKL,GAAE,aAAa,GAAEA,GAAE;AAAY,oBAAAK,GAAE,YAAYL,GAAE,UAAU;AAAA;AAAO,kBAAAK,KAAEL;AAAE,uBAAO,GAAG,eAAaK,KAAE,GAAG,KAAKT,IAAES,IAAE,IAAE,IAAGA;AAAA,cAAC;AAAC,kBAAIU,KAAE,KAAGf,GAAE,YAAUA,GAAE;AAAU,qBAAO,MAAI,GAAG,UAAU,KAAGA,GAAE,iBAAeA,GAAE,cAAc,WAASA,GAAE,cAAc,QAAQ,QAAM,EAAE,IAAGA,GAAE,cAAc,QAAQ,IAAI,MAAIe,KAAE,eAAaf,GAAE,cAAc,QAAQ,OAAK,QAAMe,KAAG,OAAKA,KAAE,EAAEA,IAAE,IAAG,GAAG,GAAEA,KAAE,EAAEA,IAAE,IAAG,GAAG,IAAG,MAAI,KAAG,GAAG,WAAWA,EAAC,IAAEA;AAAA,YAAC,GAAEpB,GAAE,YAAU,SAASF,IAAE;AAAC,iBAAGA,EAAC,GAAE,KAAG;AAAA,YAAE,GAAEE,GAAE,cAAY,WAAU;AAAC,mBAAG,MAAK,KAAG;AAAA,YAAE,GAAEA,GAAE,mBAAiB,SAASF,IAAEC,IAAEC,IAAE;AAAC,oBAAI,GAAG,CAAC,CAAC;AAAE,kBAAIC,KAAE,GAAGH,EAAC,GAAEI,KAAE,GAAGH,EAAC;AAAE,qBAAO,GAAGE,IAAEC,IAAEF,EAAC;AAAA,YAAC,GAAEA,GAAE,UAAQ,SAASF,IAAEC,IAAE;AAAC,4BAAY,OAAOA,OAAI,GAAGD,EAAC,IAAE,GAAGA,EAAC,KAAG,CAAC,GAAE,EAAE,GAAGA,EAAC,GAAEC,EAAC;AAAA,YAAE,GAAEC,GAAE,aAAW,SAASF,IAAE;AAAC,kBAAG,GAAGA,EAAC;AAAE,uBAAO,EAAE,GAAGA,EAAC,CAAC;AAAA,YAAC,GAAEE,GAAE,cAAY,SAASF,IAAE;AAAC,iBAAGA,EAAC,MAAI,GAAGA,EAAC,IAAE,CAAC;AAAA,YAAE,GAAEE,GAAE,iBAAe,WAAU;AAAC,mBAAG,CAAC;AAAA,YAAC,GAAEA;AAAA,UAAC;AAAC,iBAAO,GAAG;AAAA,QAAC,EAAE;AAAA,MAAC,GAAE,KAAI,CAAAF,OAAG;AAAC;AAAa,YAAIC,KAAE,OAAO,UAAU,gBAAeC,KAAE;AAAI,iBAASC,KAAG;AAAA,QAAC;AAAC,iBAAS,EAAEH,IAAEC,IAAEC,IAAE;AAAC,eAAK,KAAGF,IAAE,KAAK,UAAQC,IAAE,KAAK,OAAKC,MAAG;AAAA,QAAE;AAAC,iBAAS,EAAEF,IAAEC,IAAEE,IAAEE,IAAEC,IAAE;AAAC,cAAG,cAAY,OAAOH;AAAE,kBAAM,IAAI,UAAU,iCAAiC;AAAE,cAAII,KAAE,IAAI,EAAEJ,IAAEE,MAAGL,IAAEM,EAAC,GAAE,IAAEJ,KAAEA,KAAED,KAAEA;AAAE,iBAAOD,GAAE,QAAQ,CAAC,IAAEA,GAAE,QAAQ,CAAC,EAAE,KAAGA,GAAE,QAAQ,CAAC,IAAE,CAACA,GAAE,QAAQ,CAAC,GAAEO,EAAC,IAAEP,GAAE,QAAQ,CAAC,EAAE,KAAKO,EAAC,KAAGP,GAAE,QAAQ,CAAC,IAAEO,IAAEP,GAAE,iBAAgBA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAE;AAAC,eAAG,EAAED,GAAE,eAAaA,GAAE,UAAQ,IAAIG,OAAE,OAAOH,GAAE,QAAQC,EAAC;AAAA,QAAC;AAAC,iBAAS,IAAG;AAAC,eAAK,UAAQ,IAAIE,MAAE,KAAK,eAAa;AAAA,QAAC;AAAC,eAAO,WAASA,GAAE,YAAU,uBAAO,OAAO,IAAI,GAAG,IAAIA,KAAG,cAAYD,KAAE,SAAK,EAAE,UAAU,aAAW,WAAU;AAAC,cAAIF,IAAEG,IAAEC,KAAE,CAAC;AAAE,cAAG,MAAI,KAAK;AAAa,mBAAOA;AAAE,eAAID,MAAKH,KAAE,KAAK;AAAQ,YAAAC,GAAE,KAAKD,IAAEG,EAAC,KAAGC,GAAE,KAAKF,KAAEC,GAAE,MAAM,CAAC,IAAEA,EAAC;AAAE,iBAAO,OAAO,wBAAsBC,GAAE,OAAO,OAAO,sBAAsBJ,EAAC,CAAC,IAAEI;AAAA,QAAC,GAAE,EAAE,UAAU,YAAU,SAASJ,IAAE;AAAC,cAAIC,KAAEC,KAAEA,KAAEF,KAAEA,IAAEG,KAAE,KAAK,QAAQF,EAAC;AAAE,cAAG,CAACE;AAAE,mBAAM,CAAC;AAAE,cAAGA,GAAE;AAAG,mBAAM,CAACA,GAAE,EAAE;AAAE,mBAAQC,KAAE,GAAEC,KAAEF,GAAE,QAAOG,KAAE,IAAI,MAAMD,EAAC,GAAED,KAAEC,IAAED;AAAI,YAAAE,GAAEF,EAAC,IAAED,GAAEC,EAAC,EAAE;AAAG,iBAAOE;AAAA,QAAC,GAAE,EAAE,UAAU,gBAAc,SAASN,IAAE;AAAC,cAAIC,KAAEC,KAAEA,KAAEF,KAAEA,IAAEG,KAAE,KAAK,QAAQF,EAAC;AAAE,iBAAOE,KAAEA,GAAE,KAAG,IAAEA,GAAE,SAAO;AAAA,QAAC,GAAE,EAAE,UAAU,OAAK,SAASH,IAAEC,IAAEE,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAEL,KAAEA,KAAEF,KAAEA;AAAE,cAAG,CAAC,KAAK,QAAQO,EAAC;AAAE,mBAAM;AAAG,cAAI,GAAE,GAAE,IAAE,KAAK,QAAQA,EAAC,GAAE,IAAE,UAAU;AAAO,cAAG,EAAE,IAAG;AAAC,oBAAO,EAAE,QAAM,KAAK,eAAeP,IAAE,EAAE,IAAG,QAAO,IAAE,GAAE,GAAE;AAAA,cAAC,KAAK;AAAE,uBAAO,EAAE,GAAG,KAAK,EAAE,OAAO,GAAE;AAAA,cAAG,KAAK;AAAE,uBAAO,EAAE,GAAG,KAAK,EAAE,SAAQC,EAAC,GAAE;AAAA,cAAG,KAAK;AAAE,uBAAO,EAAE,GAAG,KAAK,EAAE,SAAQA,IAAEE,EAAC,GAAE;AAAA,cAAG,KAAK;AAAE,uBAAO,EAAE,GAAG,KAAK,EAAE,SAAQF,IAAEE,IAAEC,EAAC,GAAE;AAAA,cAAG,KAAK;AAAE,uBAAO,EAAE,GAAG,KAAK,EAAE,SAAQH,IAAEE,IAAEC,IAAEC,EAAC,GAAE;AAAA,cAAG,KAAK;AAAE,uBAAO,EAAE,GAAG,KAAK,EAAE,SAAQJ,IAAEE,IAAEC,IAAEC,IAAEC,EAAC,GAAE;AAAA,YAAE;AAAC,iBAAI,IAAE,GAAE,IAAE,IAAI,MAAM,IAAE,CAAC,GAAE,IAAE,GAAE;AAAI,gBAAE,IAAE,CAAC,IAAE,UAAU,CAAC;AAAE,cAAE,GAAG,MAAM,EAAE,SAAQ,CAAC;AAAA,UAAC,OAAK;AAAC,gBAAI,GAAE,IAAE,EAAE;AAAO,iBAAI,IAAE,GAAE,IAAE,GAAE;AAAI,sBAAO,EAAE,CAAC,EAAE,QAAM,KAAK,eAAeN,IAAE,EAAE,CAAC,EAAE,IAAG,QAAO,IAAE,GAAE,GAAE;AAAA,gBAAC,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,OAAO;AAAE;AAAA,gBAAM,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,SAAQC,EAAC;AAAE;AAAA,gBAAM,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,SAAQA,IAAEE,EAAC;AAAE;AAAA,gBAAM,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,SAAQF,IAAEE,IAAEC,EAAC;AAAE;AAAA,gBAAM;AAAQ,sBAAG,CAAC;AAAE,yBAAI,IAAE,GAAE,IAAE,IAAI,MAAM,IAAE,CAAC,GAAE,IAAE,GAAE;AAAI,wBAAE,IAAE,CAAC,IAAE,UAAU,CAAC;AAAE,oBAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,SAAQ,CAAC;AAAA,cAAC;AAAA,UAAC;AAAC,iBAAM;AAAA,QAAE,GAAE,EAAE,UAAU,KAAG,SAASJ,IAAEC,IAAEC,IAAE;AAAC,iBAAO,EAAE,MAAKF,IAAEC,IAAEC,IAAE,KAAE;AAAA,QAAC,GAAE,EAAE,UAAU,OAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC,iBAAO,EAAE,MAAKF,IAAEC,IAAEC,IAAE,IAAE;AAAA,QAAC,GAAE,EAAE,UAAU,iBAAe,SAASF,IAAEC,IAAEE,IAAEC,IAAE;AAAC,cAAIC,KAAEH,KAAEA,KAAEF,KAAEA;AAAE,cAAG,CAAC,KAAK,QAAQK,EAAC;AAAE,mBAAO;AAAK,cAAG,CAACJ;AAAE,mBAAO,EAAE,MAAKI,EAAC,GAAE;AAAK,cAAIE,KAAE,KAAK,QAAQF,EAAC;AAAE,cAAGE,GAAE;AAAG,YAAAA,GAAE,OAAKN,MAAGG,MAAG,CAACG,GAAE,QAAMJ,MAAGI,GAAE,YAAUJ,MAAG,EAAE,MAAKE,EAAC;AAAA,eAAM;AAAC,qBAAQ,IAAE,GAAE,IAAE,CAAC,GAAE,IAAEE,GAAE,QAAO,IAAE,GAAE;AAAI,eAACA,GAAE,CAAC,EAAE,OAAKN,MAAGG,MAAG,CAACG,GAAE,CAAC,EAAE,QAAMJ,MAAGI,GAAE,CAAC,EAAE,YAAUJ,OAAI,EAAE,KAAKI,GAAE,CAAC,CAAC;AAAE,cAAE,SAAO,KAAK,QAAQF,EAAC,IAAE,MAAI,EAAE,SAAO,EAAE,CAAC,IAAE,IAAE,EAAE,MAAKA,EAAC;AAAA,UAAC;AAAC,iBAAO;AAAA,QAAI,GAAE,EAAE,UAAU,qBAAmB,SAASL,IAAE;AAAC,cAAIC;AAAE,iBAAOD,MAAGC,KAAEC,KAAEA,KAAEF,KAAEA,IAAE,KAAK,QAAQC,EAAC,KAAG,EAAE,MAAKA,EAAC,MAAI,KAAK,UAAQ,IAAIE,MAAE,KAAK,eAAa,IAAG;AAAA,QAAI,GAAE,EAAE,UAAU,MAAI,EAAE,UAAU,gBAAe,EAAE,UAAU,cAAY,EAAE,UAAU,IAAG,EAAE,WAASD,IAAE,EAAE,eAAa,GAAEF,GAAE,UAAQ;AAAA,MAAC,GAAE,KAAI,CAAAA,OAAG;AAAC,sBAAY,OAAO,OAAO,SAAOA,GAAE,UAAQ,SAASA,IAAEC,IAAE;AAAC,UAAAD,GAAE,SAAOC,IAAED,GAAE,YAAU,OAAO,OAAOC,GAAE,WAAU,EAAC,aAAY,EAAC,OAAMD,IAAE,YAAW,OAAG,UAAS,MAAG,cAAa,KAAE,EAAC,CAAC;AAAA,QAAC,IAAEA,GAAE,UAAQ,SAASA,IAAEC,IAAE;AAAC,UAAAD,GAAE,SAAOC;AAAE,cAAIC,KAAE,WAAU;AAAA,UAAC;AAAE,UAAAA,GAAE,YAAUD,GAAE,WAAUD,GAAE,YAAU,IAAIE,MAAEF,GAAE,UAAU,cAAYA;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAA,OAAG;AAAC,YAAIC,KAAE,KAAIC,KAAE,KAAGD,IAAEE,KAAE,KAAGD,IAAE,IAAE,KAAGC,IAAE,IAAE,IAAE,GAAE,IAAE,SAAO;AAAE,iBAAS,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAEH,MAAG,MAAIC;AAAE,iBAAO,KAAK,MAAMF,KAAEE,EAAC,IAAE,MAAIC,MAAGC,KAAE,MAAI;AAAA,QAAG;AAAC,QAAAJ,GAAE,UAAQ,SAASA,IAAE,GAAE;AAAC,cAAE,KAAG,CAAC;AAAE,cAAI,IAAE,OAAOA;AAAE,cAAG,aAAW,KAAGA,GAAE,SAAO;AAAE,mBAAO,SAASA,IAAE;AAAC,mBAAIA,KAAE,OAAOA,EAAC,GAAG,SAAO;AAAI;AAAO,kBAAIO,KAAE,mIAAmI,KAAKP,EAAC;AAAE,kBAAG,CAACO;AAAE;AAAO,kBAAIC,KAAE,WAAWD,GAAE,CAAC,CAAC;AAAE,uBAAQA,GAAE,CAAC,KAAG,MAAM,YAAY,GAAE;AAAA,gBAAC,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAA,gBAAK,KAAI;AAAI,yBAAOC,KAAE;AAAA,gBAAE,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAI,yBAAOA,KAAE;AAAA,gBAAE,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAI,yBAAOA,KAAE;AAAA,gBAAE,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAA,gBAAK,KAAI;AAAI,yBAAOA,KAAEL;AAAA,gBAAE,KAAI;AAAA,gBAAU,KAAI;AAAA,gBAAS,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAI,yBAAOK,KAAEN;AAAA,gBAAE,KAAI;AAAA,gBAAU,KAAI;AAAA,gBAAS,KAAI;AAAA,gBAAO,KAAI;AAAA,gBAAM,KAAI;AAAI,yBAAOM,KAAEP;AAAA,gBAAE,KAAI;AAAA,gBAAe,KAAI;AAAA,gBAAc,KAAI;AAAA,gBAAQ,KAAI;AAAA,gBAAO,KAAI;AAAK,yBAAOO;AAAA,gBAAE;AAAQ;AAAA,cAAM;AAAA,YAAC,EAAER,EAAC;AAAE,cAAG,aAAW,KAAG,SAASA,EAAC;AAAE,mBAAO,EAAE,OAAK,SAASA,IAAE;AAAC,kBAAIK,KAAE,KAAK,IAAIL,EAAC;AAAE,kBAAGK,MAAG;AAAE,uBAAO,EAAEL,IAAEK,IAAE,GAAE,KAAK;AAAE,kBAAGA,MAAGF;AAAE,uBAAO,EAAEH,IAAEK,IAAEF,IAAE,MAAM;AAAE,kBAAGE,MAAGH;AAAE,uBAAO,EAAEF,IAAEK,IAAEH,IAAE,QAAQ;AAAE,kBAAGG,MAAGJ;AAAE,uBAAO,EAAED,IAAEK,IAAEJ,IAAE,QAAQ;AAAE,qBAAOD,KAAE;AAAA,YAAK,EAAEA,EAAC,IAAE,SAASA,IAAE;AAAC,kBAAIK,KAAE,KAAK,IAAIL,EAAC;AAAE,kBAAGK,MAAG;AAAE,uBAAO,KAAK,MAAML,KAAE,CAAC,IAAE;AAAI,kBAAGK,MAAGF;AAAE,uBAAO,KAAK,MAAMH,KAAEG,EAAC,IAAE;AAAI,kBAAGE,MAAGH;AAAE,uBAAO,KAAK,MAAMF,KAAEE,EAAC,IAAE;AAAI,kBAAGG,MAAGJ;AAAE,uBAAO,KAAK,MAAMD,KAAEC,EAAC,IAAE;AAAI,qBAAOD,KAAE;AAAA,YAAI,EAAEA,EAAC;AAAE,gBAAM,IAAI,MAAM,0DAAwD,KAAK,UAAUA,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAACA,IAAEC,IAAEC,OAAI;AAAC;AAAa,YAAIC,KAAED,GAAE,GAAG,GAAE,IAAE,YAAUC,GAAE,UAAS,IAAED,GAAE,GAAG;AAAE,iBAAS,EAAEF,IAAEC,IAAE;AAAC,mBAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAI;AAAC,gBAAIC,KAAEJ,GAAEG,EAAC;AAAE,YAAAC,MAAG,QAAMA,OAAI,SAAOA,KAAEF,GAAE,UAAQ,SAAOA,GAAEA,GAAE,SAAO,CAAC,IAAEA,GAAE,IAAI,IAAED,MAAGC,GAAE,KAAK,IAAI,IAAEA,GAAE,KAAKE,EAAC;AAAA,UAAE;AAAC,iBAAOF;AAAA,QAAC;AAAC,iBAAS,EAAEF,IAAE;AAAC,mBAAQC,KAAED,GAAE,SAAO,GAAEE,KAAE,GAAEA,MAAGD,MAAG,CAACD,GAAEE,EAAC,GAAEA;AAAI;AAAC,mBAAQC,KAAEF,IAAEE,MAAG,KAAG,CAACH,GAAEG,EAAC,GAAEA;AAAI;AAAC,iBAAO,MAAID,MAAGC,OAAIF,KAAED,KAAEE,KAAEC,KAAE,CAAC,IAAEH,GAAE,MAAME,IAAEC,KAAE,CAAC;AAAA,QAAC;AAAC,YAAI,IAAE,sEAAqE,IAAE,iEAAgE,IAAE,CAAC;AAAE,iBAAS,EAAEH,IAAE;AAAC,cAAIC,KAAE,EAAE,KAAKD,EAAC,GAAEE,MAAGD,GAAE,CAAC,KAAG,OAAKA,GAAE,CAAC,KAAG,KAAIE,KAAEF,GAAE,CAAC,KAAG,IAAGG,KAAE,EAAE,KAAKD,EAAC;AAAE,iBAAM,CAACD,IAAEE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,QAAC;AAAC,iBAAS,EAAEJ,IAAE;AAAC,cAAIC,KAAE,EAAE,KAAKD,EAAC,GAAEE,KAAED,GAAE,CAAC,KAAG,IAAGE,KAAE,CAAC,CAACD,MAAG,QAAMA,GAAE,CAAC;AAAE,iBAAM,EAAC,QAAOA,IAAE,OAAMC,IAAE,YAAWA,MAAG,CAAC,CAACF,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAE;AAAC,iBAAM,SAAOA,GAAE,QAAQ,YAAW,EAAE,EAAE,QAAQ,YAAW,IAAI;AAAA,QAAC;AAAC,UAAE,UAAQ,WAAU;AAAC,mBAAQA,KAAE,IAAGC,KAAE,IAAGC,KAAE,OAAGE,KAAE,UAAU,SAAO,GAAEA,MAAG,IAAGA,MAAI;AAAC,gBAAIG;AAAE,gBAAGH,MAAG,IAAEG,KAAE,UAAUH,EAAC,IAAEJ,MAAGO,KAAEJ,GAAE,IAAI,MAAIH,EAAC,MAAIO,GAAE,OAAO,GAAE,CAAC,EAAE,YAAY,MAAIP,GAAE,YAAY,IAAE,SAAOO,KAAEP,KAAE,QAAMO,KAAEJ,GAAE,IAAI,GAAE,CAAC,EAAE,SAASI,EAAC;AAAE,oBAAM,IAAI,UAAU,2CAA2C;AAAE,gBAAGA,IAAE;AAAC,kBAAIC,KAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,QAAOE,KAAEF,GAAE,OAAMG,KAAEH,GAAE,YAAWM,KAAEN,GAAE;AAAK,mBAAI,CAACC,MAAG,CAACT,MAAGS,GAAE,YAAY,MAAIT,GAAE,YAAY,OAAKA,OAAIA,KAAES,KAAGP,OAAID,KAAEa,KAAE,OAAKb,IAAEC,KAAES,KAAGX,MAAGE;AAAG;AAAA,YAAK;AAAA,UAAC;AAAC,iBAAOQ,OAAIV,KAAE,EAAEA,EAAC,IAAGA,MAAGE,KAAE,OAAK,OAAKD,KAAE,EAAEA,GAAE,MAAM,SAAS,GAAE,CAACC,EAAC,EAAE,KAAK,IAAI,MAAI;AAAA,QAAG,GAAE,EAAE,YAAU,SAASF,IAAE;AAAC,cAAIC,KAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,QAAOE,KAAEF,GAAE,OAAMG,KAAEH,GAAE,YAAWI,KAAEJ,GAAE,MAAKM,KAAE,UAAU,KAAKF,EAAC;AAAE,kBAAOA,KAAE,EAAEA,GAAE,MAAM,SAAS,GAAE,CAACD,EAAC,EAAE,KAAK,IAAI,MAAIA,OAAIC,KAAE,MAAKA,MAAGE,OAAIF,MAAG,OAAMF,OAAID,KAAE,EAAEA,EAAC,IAAGA,MAAGE,KAAE,OAAK,MAAIC;AAAA,QAAC,GAAE,EAAE,aAAW,SAASL,IAAE;AAAC,iBAAO,EAAEA,EAAC,EAAE;AAAA,QAAU,GAAE,EAAE,OAAK,WAAU;AAAC,mBAAQA,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAE,UAAU,QAAOA,MAAI;AAAC,gBAAIC,KAAE,UAAUD,EAAC;AAAE,gBAAG,CAAC,EAAE,SAASC,EAAC;AAAE,oBAAM,IAAI,UAAU,wCAAwC;AAAE,YAAAA,MAAGF,GAAE,KAAKE,EAAC;AAAA,UAAC;AAAC,cAAIC,KAAEH,GAAE,KAAK,IAAI;AAAE,iBAAM,oBAAoB,KAAKA,GAAE,CAAC,CAAC,MAAIG,KAAEA,GAAE,QAAQ,eAAc,IAAI,IAAG,EAAE,UAAUA,EAAC;AAAA,QAAC,GAAE,EAAE,WAAS,SAASH,IAAEC,IAAE;AAAC,UAAAD,KAAE,EAAE,QAAQA,EAAC,GAAEC,KAAE,EAAE,QAAQA,EAAC;AAAE,mBAAQC,KAAEF,GAAE,YAAY,GAAEG,KAAEF,GAAE,YAAY,GAAEG,KAAE,EAAEH,GAAE,MAAM,IAAI,CAAC,GAAEI,KAAE,EAAEH,GAAE,MAAM,IAAI,CAAC,GAAEI,KAAE,EAAEH,GAAE,MAAM,IAAI,CAAC,GAAEK,KAAE,KAAK,IAAIH,GAAE,QAAOC,GAAE,MAAM,GAAEG,KAAED,IAAEG,KAAE,GAAEA,KAAEH,IAAEG;AAAI,gBAAGN,GAAEM,EAAC,MAAIL,GAAEK,EAAC,GAAE;AAAC,cAAAF,KAAEE;AAAE;AAAA,YAAK;AAAC,cAAG,KAAGF;AAAE,mBAAOR;AAAE,cAAIW,KAAE,CAAC;AAAE,eAAID,KAAEF,IAAEE,KAAEN,GAAE,QAAOM;AAAI,YAAAC,GAAE,KAAK,IAAI;AAAE,kBAAOA,KAAEA,GAAE,OAAOR,GAAE,MAAMK,EAAC,CAAC,GAAG,KAAK,IAAI;AAAA,QAAC,GAAE,EAAE,YAAU,SAAST,IAAE;AAAC,cAAG,CAAC,EAAE,SAASA,EAAC;AAAE,mBAAOA;AAAE,cAAG,CAACA;AAAE,mBAAM;AAAG,cAAIC,KAAE,EAAE,QAAQD,EAAC;AAAE,iBAAM,gBAAgB,KAAKC,EAAC,IAAE,YAAUA,KAAE,aAAa,KAAKA,EAAC,IAAE,iBAAeA,GAAE,UAAU,CAAC,IAAED;AAAA,QAAC,GAAE,EAAE,UAAQ,SAASA,IAAE;AAAC,cAAIC,KAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,iBAAOC,MAAGC,MAAGA,OAAIA,KAAEA,GAAE,OAAO,GAAEA,GAAE,SAAO,CAAC,IAAGD,KAAEC,MAAG;AAAA,QAAG,GAAE,EAAE,WAAS,SAASH,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAEF,EAAC,EAAE,CAAC;AAAE,iBAAOC,MAAGC,GAAE,OAAO,KAAGD,GAAE,MAAM,MAAIA,OAAIC,KAAEA,GAAE,OAAO,GAAEA,GAAE,SAAOD,GAAE,MAAM,IAAGC;AAAA,QAAC,GAAE,EAAE,UAAQ,SAASF,IAAE;AAAC,iBAAO,EAAEA,EAAC,EAAE,CAAC;AAAA,QAAC,GAAE,EAAE,SAAO,SAASA,IAAE;AAAC,cAAG,CAAC,EAAE,SAASA,EAAC;AAAE,kBAAM,IAAI,UAAU,mDAAiD,OAAOA,EAAC;AAAE,cAAIC,KAAED,GAAE,QAAM;AAAG,cAAG,CAAC,EAAE,SAASC,EAAC;AAAE,kBAAM,IAAI,UAAU,0DAAwD,OAAOD,GAAE,IAAI;AAAE,cAAIE,KAAEF,GAAE,KAAIG,KAAEH,GAAE,QAAM;AAAG,iBAAOE,KAAEA,GAAEA,GAAE,SAAO,CAAC,MAAI,EAAE,MAAIA,KAAEC,KAAED,KAAE,EAAE,MAAIC,KAAEA;AAAA,QAAC,GAAE,EAAE,QAAM,SAASH,IAAE;AAAC,cAAG,CAAC,EAAE,SAASA,EAAC;AAAE,kBAAM,IAAI,UAAU,kDAAgD,OAAOA,EAAC;AAAE,cAAIC,KAAE,EAAED,EAAC;AAAE,cAAG,CAACC,MAAG,MAAIA,GAAE;AAAO,kBAAM,IAAI,UAAU,mBAAiBD,KAAE,GAAG;AAAE,iBAAM,EAAC,MAAKC,GAAE,CAAC,GAAE,KAAIA,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAM,GAAE,EAAE,GAAE,MAAKA,GAAE,CAAC,GAAE,KAAIA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,EAAE,MAAM,GAAEA,GAAE,CAAC,EAAE,SAAOA,GAAE,CAAC,EAAE,MAAM,EAAC;AAAA,QAAC,GAAE,EAAE,MAAI,MAAK,EAAE,YAAU;AAAI,YAAI,IAAE,iEAAgE,IAAE,CAAC;AAAE,iBAAS,EAAED,IAAE;AAAC,iBAAO,EAAE,KAAKA,EAAC,EAAE,MAAM,CAAC;AAAA,QAAC;AAAC,UAAE,UAAQ,WAAU;AAAC,mBAAQA,KAAE,IAAGC,KAAE,OAAGC,KAAE,UAAU,SAAO,GAAEA,MAAG,MAAI,CAACD,IAAEC,MAAI;AAAC,gBAAIE,KAAEF,MAAG,IAAE,UAAUA,EAAC,IAAEC,GAAE,IAAI;AAAE,gBAAG,CAAC,EAAE,SAASC,EAAC;AAAE,oBAAM,IAAI,UAAU,2CAA2C;AAAE,YAAAA,OAAIJ,KAAEI,KAAE,MAAIJ,IAAEC,KAAE,QAAMG,GAAE,CAAC;AAAA,UAAE;AAAC,kBAAOH,KAAE,MAAI,OAAKD,KAAE,EAAEA,GAAE,MAAM,GAAG,GAAE,CAACC,EAAC,EAAE,KAAK,GAAG,MAAI;AAAA,QAAG,GAAE,EAAE,YAAU,SAASD,IAAE;AAAC,cAAIC,KAAE,EAAE,WAAWD,EAAC,GAAEE,KAAEF,MAAG,QAAMA,GAAEA,GAAE,SAAO,CAAC;AAAE,kBAAOA,KAAE,EAAEA,GAAE,MAAM,GAAG,GAAE,CAACC,EAAC,EAAE,KAAK,GAAG,MAAIA,OAAID,KAAE,MAAKA,MAAGE,OAAIF,MAAG,OAAMC,KAAE,MAAI,MAAID;AAAA,QAAC,GAAE,EAAE,aAAW,SAASA,IAAE;AAAC,iBAAM,QAAMA,GAAE,OAAO,CAAC;AAAA,QAAC,GAAE,EAAE,OAAK,WAAU;AAAC,mBAAQA,KAAE,IAAGC,KAAE,GAAEA,KAAE,UAAU,QAAOA,MAAI;AAAC,gBAAIC,KAAE,UAAUD,EAAC;AAAE,gBAAG,CAAC,EAAE,SAASC,EAAC;AAAE,oBAAM,IAAI,UAAU,wCAAwC;AAAE,YAAAA,OAAIF,MAAGA,KAAE,MAAIE,KAAEA;AAAA,UAAE;AAAC,iBAAO,EAAE,UAAUF,EAAC;AAAA,QAAC,GAAE,EAAE,WAAS,SAASA,IAAEC,IAAE;AAAC,UAAAD,KAAE,EAAE,QAAQA,EAAC,EAAE,OAAO,CAAC,GAAEC,KAAE,EAAE,QAAQA,EAAC,EAAE,OAAO,CAAC;AAAE,mBAAQC,KAAE,EAAEF,GAAE,MAAM,GAAG,CAAC,GAAEG,KAAE,EAAEF,GAAE,MAAM,GAAG,CAAC,GAAEG,KAAE,KAAK,IAAIF,GAAE,QAAOC,GAAE,MAAM,GAAEE,KAAED,IAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,gBAAGJ,GAAEI,EAAC,MAAIH,GAAEG,EAAC,GAAE;AAAC,cAAAD,KAAEC;AAAE;AAAA,YAAK;AAAC,cAAIE,KAAE,CAAC;AAAE,eAAIF,KAAED,IAAEC,KAAEJ,GAAE,QAAOI;AAAI,YAAAE,GAAE,KAAK,IAAI;AAAE,kBAAOA,KAAEA,GAAE,OAAOL,GAAE,MAAME,EAAC,CAAC,GAAG,KAAK,GAAG;AAAA,QAAC,GAAE,EAAE,YAAU,SAASL,IAAE;AAAC,iBAAOA;AAAA,QAAC,GAAE,EAAE,UAAQ,SAASA,IAAE;AAAC,cAAIC,KAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,iBAAOC,MAAGC,MAAGA,OAAIA,KAAEA,GAAE,OAAO,GAAEA,GAAE,SAAO,CAAC,IAAGD,KAAEC,MAAG;AAAA,QAAG,GAAE,EAAE,WAAS,SAASH,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAEF,EAAC,EAAE,CAAC;AAAE,iBAAOC,MAAGC,GAAE,OAAO,KAAGD,GAAE,MAAM,MAAIA,OAAIC,KAAEA,GAAE,OAAO,GAAEA,GAAE,SAAOD,GAAE,MAAM,IAAGC;AAAA,QAAC,GAAE,EAAE,UAAQ,SAASF,IAAE;AAAC,iBAAO,EAAEA,EAAC,EAAE,CAAC;AAAA,QAAC,GAAE,EAAE,SAAO,SAASA,IAAE;AAAC,cAAG,CAAC,EAAE,SAASA,EAAC;AAAE,kBAAM,IAAI,UAAU,mDAAiD,OAAOA,EAAC;AAAE,cAAIC,KAAED,GAAE,QAAM;AAAG,cAAG,CAAC,EAAE,SAASC,EAAC;AAAE,kBAAM,IAAI,UAAU,0DAAwD,OAAOD,GAAE,IAAI;AAAE,kBAAOA,GAAE,MAAIA,GAAE,MAAI,EAAE,MAAI,OAAKA,GAAE,QAAM;AAAA,QAAG,GAAE,EAAE,QAAM,SAASA,IAAE;AAAC,cAAG,CAAC,EAAE,SAASA,EAAC;AAAE,kBAAM,IAAI,UAAU,kDAAgD,OAAOA,EAAC;AAAE,cAAIC,KAAE,EAAED,EAAC;AAAE,cAAG,CAACC,MAAG,MAAIA,GAAE;AAAO,kBAAM,IAAI,UAAU,mBAAiBD,KAAE,GAAG;AAAE,iBAAOC,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG,IAAGA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG,IAAGA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG,IAAG,EAAC,MAAKA,GAAE,CAAC,GAAE,KAAIA,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAM,GAAE,EAAE,GAAE,MAAKA,GAAE,CAAC,GAAE,KAAIA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,EAAE,MAAM,GAAEA,GAAE,CAAC,EAAE,SAAOA,GAAE,CAAC,EAAE,MAAM,EAAC;AAAA,QAAC,GAAE,EAAE,MAAI,KAAI,EAAE,YAAU,KAAID,GAAE,UAAQ,IAAE,IAAE,GAAEA,GAAE,QAAQ,QAAM,GAAEA,GAAE,QAAQ,QAAM;AAAA,MAAC,GAAE,KAAI,CAAAA,OAAG;AAAC,YAAIC,IAAEC,IAAEC,KAAEH,GAAE,UAAQ,CAAC;AAAE,iBAAS,IAAG;AAAC,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QAAC;AAAC,iBAAS,IAAG;AAAC,gBAAM,IAAI,MAAM,mCAAmC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,cAAGC,OAAI;AAAW,mBAAO,WAAWD,IAAE,CAAC;AAAE,eAAIC,OAAI,KAAG,CAACA,OAAI;AAAW,mBAAOA,KAAE,YAAW,WAAWD,IAAE,CAAC;AAAE,cAAG;AAAC,mBAAOC,GAAED,IAAE,CAAC;AAAA,UAAC,SAAOE,IAAE;AAAC,gBAAG;AAAC,qBAAOD,GAAE,KAAK,MAAKD,IAAE,CAAC;AAAA,YAAC,SAAOE,IAAE;AAAC,qBAAOD,GAAE,KAAK,MAAKD,IAAE,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAC,SAAC,WAAU;AAAC,cAAG;AAAC,YAAAC,KAAE,cAAY,OAAO,aAAW,aAAW;AAAA,UAAC,SAAOD,IAAE;AAAC,YAAAC,KAAE;AAAA,UAAC;AAAC,cAAG;AAAC,YAAAC,KAAE,cAAY,OAAO,eAAa,eAAa;AAAA,UAAC,SAAOF,IAAE;AAAC,YAAAE,KAAE;AAAA,UAAC;AAAA,QAAC,EAAE;AAAE,YAAI,GAAE,IAAE,CAAC,GAAE,IAAE,OAAG,IAAE;AAAG,iBAAS,IAAG;AAAC,eAAG,MAAI,IAAE,OAAG,EAAE,SAAO,IAAE,EAAE,OAAO,CAAC,IAAE,IAAE,IAAG,EAAE,UAAQ,EAAE;AAAA,QAAE;AAAC,iBAAS,IAAG;AAAC,cAAG,CAAC,GAAE;AAAC,gBAAIF,KAAE,EAAE,CAAC;AAAE,gBAAE;AAAG,qBAAQC,KAAE,EAAE,QAAOA,MAAG;AAAC,mBAAI,IAAE,GAAE,IAAE,CAAC,GAAE,EAAE,IAAEA;AAAG,qBAAG,EAAE,CAAC,EAAE,IAAI;AAAE,kBAAE,IAAGA,KAAE,EAAE;AAAA,YAAM;AAAC,gBAAE,MAAK,IAAE,OAAG,SAASD,IAAE;AAAC,kBAAGE,OAAI;AAAa,uBAAO,aAAaF,EAAC;AAAE,mBAAIE,OAAI,KAAG,CAACA,OAAI;AAAa,uBAAOA,KAAE,cAAa,aAAaF,EAAC;AAAE,kBAAG;AAAC,gBAAAE,GAAEF,EAAC;AAAA,cAAC,SAAOC,IAAE;AAAC,oBAAG;AAAC,yBAAOC,GAAE,KAAK,MAAKF,EAAC;AAAA,gBAAC,SAAOC,IAAE;AAAC,yBAAOC,GAAE,KAAK,MAAKF,EAAC;AAAA,gBAAC;AAAA,cAAC;AAAA,YAAC,EAAEA,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAE;AAAC,eAAK,MAAID,IAAE,KAAK,QAAMC;AAAA,QAAC;AAAC,iBAAS,IAAG;AAAA,QAAC;AAAC,QAAAE,GAAE,WAAS,SAASH,IAAE;AAAC,cAAIC,KAAE,IAAI,MAAM,UAAU,SAAO,CAAC;AAAE,cAAG,UAAU,SAAO;AAAE,qBAAQC,KAAE,GAAEA,KAAE,UAAU,QAAOA;AAAI,cAAAD,GAAEC,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,YAAE,KAAK,IAAI,EAAEF,IAAEC,EAAC,CAAC,GAAE,MAAI,EAAE,UAAQ,KAAG,EAAE,CAAC;AAAA,QAAC,GAAE,EAAE,UAAU,MAAI,WAAU;AAAC,eAAK,IAAI,MAAM,MAAK,KAAK,KAAK;AAAA,QAAC,GAAEE,GAAE,QAAM,WAAUA,GAAE,UAAQ,MAAGA,GAAE,MAAI,CAAC,GAAEA,GAAE,OAAK,CAAC,GAAEA,GAAE,UAAQ,IAAGA,GAAE,WAAS,CAAC,GAAEA,GAAE,KAAG,GAAEA,GAAE,cAAY,GAAEA,GAAE,OAAK,GAAEA,GAAE,MAAI,GAAEA,GAAE,iBAAe,GAAEA,GAAE,qBAAmB,GAAEA,GAAE,OAAK,GAAEA,GAAE,kBAAgB,GAAEA,GAAE,sBAAoB,GAAEA,GAAE,YAAU,SAASH,IAAE;AAAC,iBAAM,CAAC;AAAA,QAAC,GAAEG,GAAE,UAAQ,SAASH,IAAE;AAAC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QAAC,GAAEG,GAAE,MAAI,WAAU;AAAC,iBAAM;AAAA,QAAG,GAAEA,GAAE,QAAM,SAASH,IAAE;AAAC,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAAC,GAAEG,GAAE,QAAM,WAAU;AAAC,iBAAO;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAH,OAAG;AAAC,QAAAA,GAAE,UAAQ,SAASA,IAAE;AAAC,iBAAOA,MAAG,YAAU,OAAOA,MAAG,cAAY,OAAOA,GAAE,QAAM,cAAY,OAAOA,GAAE,QAAM,cAAY,OAAOA,GAAE;AAAA,QAAS;AAAA,MAAC,GAAE,KAAI,CAACA,IAAEC,IAAEC,OAAI;AAAC,YAAIC,KAAED,GAAE,GAAG,GAAE,IAAE;AAAW,QAAAD,GAAE,SAAO,SAASD,IAAE;AAAC,cAAG,CAAC,EAAEA,EAAC,GAAE;AAAC,qBAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAE,UAAU,QAAOA;AAAI,cAAAD,GAAE,KAAK,EAAE,UAAUC,EAAC,CAAC,CAAC;AAAE,mBAAOD,GAAE,KAAK,GAAG;AAAA,UAAC;AAAC,UAAAC,KAAE;AAAE,mBAAQC,KAAE,WAAUE,KAAEF,GAAE,QAAOG,KAAE,OAAON,EAAC,EAAE,QAAQ,GAAG,SAASA,IAAE;AAAC,gBAAG,SAAOA;AAAE,qBAAM;AAAI,gBAAGE,MAAGG;AAAE,qBAAOL;AAAE,oBAAOA,IAAE;AAAA,cAAC,KAAI;AAAK,uBAAO,OAAOG,GAAED,IAAG,CAAC;AAAA,cAAE,KAAI;AAAK,uBAAO,OAAOC,GAAED,IAAG,CAAC;AAAA,cAAE,KAAI;AAAK,oBAAG;AAAC,yBAAO,KAAK,UAAUC,GAAED,IAAG,CAAC;AAAA,gBAAC,SAAOF,IAAE;AAAC,yBAAM;AAAA,gBAAY;AAAA,cAAC;AAAQ,uBAAOA;AAAA,YAAC;AAAA,UAAC,CAAE,GAAEQ,KAAEL,GAAED,EAAC,GAAEA,KAAEG,IAAEG,KAAEL,GAAE,EAAED,EAAC;AAAE,cAAEM,EAAC,KAAG,CAAC,EAAEA,EAAC,IAAEF,MAAG,MAAIE,KAAEF,MAAG,MAAI,EAAEE,EAAC;AAAE,iBAAOF;AAAA,QAAC,GAAEL,GAAE,YAAU,SAASD,IAAEI,IAAE;AAAC,cAAG,EAAEF,GAAE,EAAE,OAAO;AAAE,mBAAO,WAAU;AAAC,qBAAOD,GAAE,UAAUD,IAAEI,EAAC,EAAE,MAAM,MAAK,SAAS;AAAA,YAAC;AAAE,cAAG,SAAKD,GAAE;AAAc,mBAAOH;AAAE,cAAIK,KAAE;AAAG,iBAAO,WAAU;AAAC,gBAAG,CAACA,IAAE;AAAC,kBAAGF,GAAE;AAAiB,sBAAM,IAAI,MAAMC,EAAC;AAAE,cAAAD,GAAE,mBAAiB,QAAQ,MAAMC,EAAC,IAAE,QAAQ,MAAMA,EAAC,GAAEC,KAAE;AAAA,YAAE;AAAC,mBAAOL,GAAE,MAAM,MAAK,SAAS;AAAA,UAAC;AAAA,QAAC;AAAE,YAAI,GAAE,IAAE,CAAC;AAAE,iBAAS,EAAEA,IAAEE,IAAE;AAAC,cAAIC,KAAE,EAAC,MAAK,CAAC,GAAE,SAAQ,EAAC;AAAE,iBAAO,UAAU,UAAQ,MAAIA,GAAE,QAAM,UAAU,CAAC,IAAG,UAAU,UAAQ,MAAIA,GAAE,SAAO,UAAU,CAAC,IAAG,EAAED,EAAC,IAAEC,GAAE,aAAWD,KAAEA,MAAGD,GAAE,QAAQE,IAAED,EAAC,GAAE,EAAEC,GAAE,UAAU,MAAIA,GAAE,aAAW,QAAI,EAAEA,GAAE,KAAK,MAAIA,GAAE,QAAM,IAAG,EAAEA,GAAE,MAAM,MAAIA,GAAE,SAAO,QAAI,EAAEA,GAAE,aAAa,MAAIA,GAAE,gBAAc,OAAIA,GAAE,WAASA,GAAE,UAAQ,IAAG,EAAEA,IAAEH,IAAEG,GAAE,KAAK;AAAA,QAAC;AAAC,iBAAS,EAAEH,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAE,OAAOD,EAAC;AAAE,iBAAOC,KAAE,UAAK,EAAE,OAAOA,EAAC,EAAE,CAAC,IAAE,MAAIF,KAAE,UAAK,EAAE,OAAOE,EAAC,EAAE,CAAC,IAAE,MAAIF;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAE;AAAC,iBAAOD;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEE,IAAEC,IAAE;AAAC,cAAGH,GAAE,iBAAeE,MAAG,EAAEA,GAAE,OAAO,KAAGA,GAAE,YAAUD,GAAE,YAAU,CAACC,GAAE,eAAaA,GAAE,YAAY,cAAYA,KAAG;AAAC,gBAAIE,KAAEF,GAAE,QAAQC,IAAEH,EAAC;AAAE,mBAAO,EAAEI,EAAC,MAAIA,KAAE,EAAEJ,IAAEI,IAAED,EAAC,IAAGC;AAAA,UAAC;AAAC,cAAIC,KAAE,SAASL,IAAEC,IAAE;AAAC,gBAAG,EAAEA,EAAC;AAAE,qBAAOD,GAAE,QAAQ,aAAY,WAAW;AAAE,gBAAG,EAAEC,EAAC,GAAE;AAAC,kBAAIC,KAAE,MAAI,KAAK,UAAUD,EAAC,EAAE,QAAQ,UAAS,EAAE,EAAE,QAAQ,MAAK,KAAK,EAAE,QAAQ,QAAO,GAAG,IAAE;AAAI,qBAAOD,GAAE,QAAQE,IAAE,QAAQ;AAAA,YAAC;AAAC,gBAAG,EAAED,EAAC;AAAE,qBAAOD,GAAE,QAAQ,KAAGC,IAAE,QAAQ;AAAE,gBAAG,EAAEA,EAAC;AAAE,qBAAOD,GAAE,QAAQ,KAAGC,IAAE,SAAS;AAAE,gBAAG,EAAEA,EAAC;AAAE,qBAAOD,GAAE,QAAQ,QAAO,MAAM;AAAA,UAAC,EAAEA,IAAEE,EAAC;AAAE,cAAGG;AAAE,mBAAOA;AAAE,cAAIC,KAAE,OAAO,KAAKJ,EAAC,GAAEK,KAAE,SAASP,IAAE;AAAC,gBAAIC,KAAE,CAAC;AAAE,mBAAOD,GAAE,QAAS,SAASA,IAAEE,IAAE;AAAC,cAAAD,GAAED,EAAC,IAAE;AAAA,YAAE,CAAE,GAAEC;AAAA,UAAC,EAAEK,EAAC;AAAE,cAAGN,GAAE,eAAaM,KAAE,OAAO,oBAAoBJ,EAAC,IAAG,EAAEA,EAAC,MAAII,GAAE,QAAQ,SAAS,KAAG,KAAGA,GAAE,QAAQ,aAAa,KAAG;AAAG,mBAAO,EAAEJ,EAAC;AAAE,cAAG,MAAII,GAAE,QAAO;AAAC,gBAAG,EAAEJ,EAAC,GAAE;AAAC,kBAAIM,KAAEN,GAAE,OAAK,OAAKA,GAAE,OAAK;AAAG,qBAAOF,GAAE,QAAQ,cAAYQ,KAAE,KAAI,SAAS;AAAA,YAAC;AAAC,gBAAG,EAAEN,EAAC;AAAE,qBAAOF,GAAE,QAAQ,OAAO,UAAU,SAAS,KAAKE,EAAC,GAAE,QAAQ;AAAE,gBAAG,EAAEA,EAAC;AAAE,qBAAOF,GAAE,QAAQ,KAAK,UAAU,SAAS,KAAKE,EAAC,GAAE,MAAM;AAAE,gBAAG,EAAEA,EAAC;AAAE,qBAAO,EAAEA,EAAC;AAAA,UAAC;AAAC,cAAIO,IAAEU,KAAE,IAAGI,KAAE,OAAGC,KAAE,CAAC,KAAI,GAAG;AAAE,WAAC,EAAEtB,EAAC,MAAIqB,KAAE,MAAGC,KAAE,CAAC,KAAI,GAAG,IAAG,EAAEtB,EAAC,OAAKiB,KAAE,gBAAcjB,GAAE,OAAK,OAAKA,GAAE,OAAK,MAAI;AAAK,iBAAO,EAAEA,EAAC,MAAIiB,KAAE,MAAI,OAAO,UAAU,SAAS,KAAKjB,EAAC,IAAG,EAAEA,EAAC,MAAIiB,KAAE,MAAI,KAAK,UAAU,YAAY,KAAKjB,EAAC,IAAG,EAAEA,EAAC,MAAIiB,KAAE,MAAI,EAAEjB,EAAC,IAAG,MAAII,GAAE,UAAQiB,MAAG,KAAGrB,GAAE,SAAOC,KAAE,IAAE,EAAED,EAAC,IAAEF,GAAE,QAAQ,OAAO,UAAU,SAAS,KAAKE,EAAC,GAAE,QAAQ,IAAEF,GAAE,QAAQ,YAAW,SAAS,KAAGA,GAAE,KAAK,KAAKE,EAAC,GAAEO,KAAEc,KAAE,SAASvB,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,qBAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAEN,GAAE,QAAOK,KAAEC,IAAE,EAAED;AAAE,gBAAEL,IAAE,OAAOK,EAAC,CAAC,IAAED,GAAE,KAAK,EAAEL,IAAEC,IAAEC,IAAEC,IAAE,OAAOG,EAAC,GAAE,IAAE,CAAC,IAAED,GAAE,KAAK,EAAE;AAAE,mBAAOD,GAAE,QAAS,SAASA,IAAE;AAAC,cAAAA,GAAE,MAAM,OAAO,KAAGC,GAAE,KAAK,EAAEL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,IAAE,CAAC;AAAA,YAAC,CAAE,GAAEC;AAAA,UAAC,EAAEL,IAAEE,IAAEC,IAAEI,IAAED,EAAC,IAAEA,GAAE,IAAK,SAASL,IAAE;AAAC,mBAAO,EAAED,IAAEE,IAAEC,IAAEI,IAAEN,IAAEsB,EAAC;AAAA,UAAC,CAAE,GAAEvB,GAAE,KAAK,IAAI,GAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,gBAAGF,GAAE,OAAQ,SAASA,IAAEC,IAAE;AAAC,qBAAOA,GAAE,QAAQ,IAAI,KAAG,KAAG,GAAED,KAAEC,GAAE,QAAQ,mBAAkB,EAAE,EAAE,SAAO;AAAA,YAAC,GAAG,CAAC,IAAE;AAAG,qBAAOC,GAAE,CAAC,KAAG,OAAKD,KAAE,KAAGA,KAAE,SAAO,MAAID,GAAE,KAAK,OAAO,IAAE,MAAIE,GAAE,CAAC;AAAE,mBAAOA,GAAE,CAAC,IAAED,KAAE,MAAID,GAAE,KAAK,IAAI,IAAE,MAAIE,GAAE,CAAC;AAAA,UAAC,EAAEO,IAAEU,IAAEK,EAAC,KAAGA,GAAE,CAAC,IAAEL,KAAEK,GAAE,CAAC;AAAA,QAAC;AAAC,iBAAS,EAAExB,IAAE;AAAC,iBAAM,MAAI,MAAM,UAAU,SAAS,KAAKA,EAAC,IAAE;AAAA,QAAG;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,IAAEC,IAAEC;AAAE,eAAIA,KAAE,OAAO,yBAAyBP,IAAEG,EAAC,KAAG,EAAC,OAAMH,GAAEG,EAAC,EAAC,GAAG,MAAIG,KAAEC,GAAE,MAAIR,GAAE,QAAQ,mBAAkB,SAAS,IAAEA,GAAE,QAAQ,YAAW,SAAS,IAAEQ,GAAE,QAAMD,KAAEP,GAAE,QAAQ,YAAW,SAAS,IAAG,EAAEG,IAAEC,EAAC,MAAIE,KAAE,MAAIF,KAAE,MAAKG,OAAIP,GAAE,KAAK,QAAQQ,GAAE,KAAK,IAAE,KAAGD,KAAE,EAAEL,EAAC,IAAE,EAAEF,IAAEQ,GAAE,OAAM,IAAI,IAAE,EAAER,IAAEQ,GAAE,OAAMN,KAAE,CAAC,GAAG,QAAQ,IAAI,IAAE,OAAKK,KAAEF,KAAEE,GAAE,MAAM,IAAI,EAAE,IAAK,SAASP,IAAE;AAAC,mBAAM,OAAKA;AAAA,UAAC,CAAE,EAAE,KAAK,IAAI,EAAE,OAAO,CAAC,IAAE,OAAKO,GAAE,MAAM,IAAI,EAAE,IAAK,SAASP,IAAE;AAAC,mBAAM,QAAMA;AAAA,UAAC,CAAE,EAAE,KAAK,IAAI,KAAGO,KAAEP,GAAE,QAAQ,cAAa,SAAS,IAAG,EAAEM,EAAC,GAAE;AAAC,gBAAGD,MAAGD,GAAE,MAAM,OAAO;AAAE,qBAAOG;AAAE,aAACD,KAAE,KAAK,UAAU,KAAGF,EAAC,GAAG,MAAM,8BAA8B,KAAGE,KAAEA,GAAE,OAAO,GAAEA,GAAE,SAAO,CAAC,GAAEA,KAAEN,GAAE,QAAQM,IAAE,MAAM,MAAIA,KAAEA,GAAE,QAAQ,MAAK,KAAK,EAAE,QAAQ,QAAO,GAAG,EAAE,QAAQ,YAAW,GAAG,GAAEA,KAAEN,GAAE,QAAQM,IAAE,QAAQ;AAAA,UAAE;AAAC,iBAAOA,KAAE,OAAKC;AAAA,QAAC;AAAC,iBAAS,EAAEP,IAAE;AAAC,iBAAO,MAAM,QAAQA,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAM,aAAW,OAAOA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAO,SAAOA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAM,YAAU,OAAOA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAM,YAAU,OAAOA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAO,WAASA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAO,EAAEA,EAAC,KAAG,sBAAoB,EAAEA,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAM,YAAU,OAAOA,MAAG,SAAOA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAO,EAAEA,EAAC,KAAG,oBAAkB,EAAEA,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAO,EAAEA,EAAC,MAAI,qBAAmB,EAAEA,EAAC,KAAGA,cAAa;AAAA,QAAM;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAM,cAAY,OAAOA;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAO,OAAO,UAAU,SAAS,KAAKA,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAOA,KAAE,KAAG,MAAIA,GAAE,SAAS,EAAE,IAAEA,GAAE,SAAS,EAAE;AAAA,QAAC;AAAC,QAAAC,GAAE,WAAS,SAASD,IAAE;AAAC,cAAG,EAAE,CAAC,MAAI,IAAEG,GAAE,IAAI,cAAY,KAAIH,KAAEA,GAAE,YAAY,GAAE,CAAC,EAAEA,EAAC;AAAE,gBAAG,IAAI,OAAO,QAAMA,KAAE,OAAM,GAAG,EAAE,KAAK,CAAC,GAAE;AAAC,kBAAIE,KAAEC,GAAE;AAAI,gBAAEH,EAAC,IAAE,WAAU;AAAC,oBAAIG,KAAEF,GAAE,OAAO,MAAMA,IAAE,SAAS;AAAE,wBAAQ,MAAM,aAAYD,IAAEE,IAAEC,EAAC;AAAA,cAAC;AAAA,YAAC;AAAM,gBAAEH,EAAC,IAAE,WAAU;AAAA,cAAC;AAAE,iBAAO,EAAEA,EAAC;AAAA,QAAC,GAAEC,GAAE,UAAQ,GAAE,EAAE,SAAO,EAAC,MAAK,CAAC,GAAE,EAAE,GAAE,QAAO,CAAC,GAAE,EAAE,GAAE,WAAU,CAAC,GAAE,EAAE,GAAE,SAAQ,CAAC,GAAE,EAAE,GAAE,OAAM,CAAC,IAAG,EAAE,GAAE,MAAK,CAAC,IAAG,EAAE,GAAE,OAAM,CAAC,IAAG,EAAE,GAAE,MAAK,CAAC,IAAG,EAAE,GAAE,MAAK,CAAC,IAAG,EAAE,GAAE,OAAM,CAAC,IAAG,EAAE,GAAE,SAAQ,CAAC,IAAG,EAAE,GAAE,KAAI,CAAC,IAAG,EAAE,GAAE,QAAO,CAAC,IAAG,EAAE,EAAC,GAAE,EAAE,SAAO,EAAC,SAAQ,QAAO,QAAO,UAAS,SAAQ,UAAS,WAAU,QAAO,MAAK,QAAO,QAAO,SAAQ,MAAK,WAAU,QAAO,MAAK,GAAEA,GAAE,UAAQ,GAAEA,GAAE,YAAU,GAAEA,GAAE,SAAO,GAAEA,GAAE,oBAAkB,SAASD,IAAE;AAAC,iBAAO,QAAMA;AAAA,QAAC,GAAEC,GAAE,WAAS,GAAEA,GAAE,WAAS,GAAEA,GAAE,WAAS,SAASD,IAAE;AAAC,iBAAM,YAAU,OAAOA;AAAA,QAAC,GAAEC,GAAE,cAAY,GAAEA,GAAE,WAAS,GAAEA,GAAE,WAAS,GAAEA,GAAE,SAAO,GAAEA,GAAE,UAAQ,GAAEA,GAAE,aAAW,GAAEA,GAAE,cAAY,SAASD,IAAE;AAAC,iBAAO,SAAOA,MAAG,aAAW,OAAOA,MAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,MAAG,WAASA;AAAA,QAAC,GAAEC,GAAE,WAASC,GAAE,GAAG;AAAE,YAAI,IAAE,CAAC,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,KAAK;AAAE,iBAAS,IAAG;AAAC,cAAIF,KAAE,oBAAI,QAAKC,KAAE,CAAC,EAAED,GAAE,SAAS,CAAC,GAAE,EAAEA,GAAE,WAAW,CAAC,GAAE,EAAEA,GAAE,WAAW,CAAC,CAAC,EAAE,KAAK,GAAG;AAAE,iBAAM,CAACA,GAAE,QAAQ,GAAE,EAAEA,GAAE,SAAS,CAAC,GAAEC,EAAC,EAAE,KAAK,GAAG;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAE;AAAC,iBAAO,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC;AAAA,QAAC;AAAC,QAAAA,GAAE,MAAI,WAAU;AAAC,kBAAQ,IAAI,WAAU,EAAE,GAAEA,GAAE,OAAO,MAAMA,IAAE,SAAS,CAAC;AAAA,QAAC,GAAEA,GAAE,WAASC,GAAE,GAAG,GAAED,GAAE,UAAQ,SAASD,IAAEC,IAAE;AAAC,cAAG,CAACA,MAAG,CAAC,EAAEA,EAAC;AAAE,mBAAOD;AAAE,mBAAQE,KAAE,OAAO,KAAKD,EAAC,GAAEE,KAAED,GAAE,QAAOC;AAAK,YAAAH,GAAEE,GAAEC,EAAC,CAAC,IAAEF,GAAEC,GAAEC,EAAC,CAAC;AAAE,iBAAOH;AAAA,QAAC;AAAA,MAAC,EAAC,GAAE,IAAE,CAAC;AAAE,eAAS,EAAEG,IAAE;AAAC,YAAI,IAAE,EAAEA,EAAC;AAAE,YAAG,WAAS;AAAE,iBAAO,EAAE;AAAQ,YAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,eAAO,EAAEA,EAAC,EAAE,KAAK,EAAE,SAAQ,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE;AAAA,MAAO;AAAC,QAAE,IAAE,CAAAH,OAAG;AAAC,YAAIC,KAAED,MAAGA,GAAE,aAAW,MAAIA,GAAE,UAAQ,MAAIA;AAAE,eAAO,EAAE,EAAEC,IAAE,EAAC,GAAEA,GAAC,CAAC,GAAEA;AAAA,MAAC,GAAE,EAAE,IAAE,CAACD,IAAEC,OAAI;AAAC,iBAAQE,MAAKF;AAAE,YAAE,EAAEA,IAAEE,EAAC,KAAG,CAAC,EAAE,EAAEH,IAAEG,EAAC,KAAG,OAAO,eAAeH,IAAEG,IAAE,EAAC,YAAW,MAAG,KAAIF,GAAEE,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,EAAE,IAAE,WAAU;AAAC,YAAG,YAAU,OAAO;AAAW,iBAAO;AAAW,YAAG;AAAC,iBAAO,QAAM,IAAI,SAAS,aAAa,EAAE;AAAA,QAAC,SAAOH,IAAE;AAAC,cAAG,YAAU,OAAO;AAAO,mBAAO;AAAA,QAAM;AAAA,MAAC,EAAE,GAAE,EAAE,IAAE,CAACA,IAAEC,OAAI,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC,GAAE,EAAE,IAAE,CAAAD,OAAG;AAAC,uBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,MAAC;AAAE,UAAI,IAAE,CAAC;AAAE,cAAO,MAAI;AAAC;AAAa,UAAE,EAAE,CAAC,GAAE,EAAE,EAAE,GAAE,EAAC,cAAa,MAAI,IAAG,yBAAwB,MAAI,GAAE,CAAC;AAAE,YAAIA,KAAE,EAAE,GAAG,GAAEC,MAAG,EAAE,GAAG,GAAE,EAAE,GAAG,IAAG,IAAE,EAAE,EAAEA,EAAC;AAAE,YAAI,IAAE,WAAU;AAAC,iBAAO,IAAE,OAAO,UAAQ,SAASD,IAAE;AAAC,qBAAQC,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED;AAAI,uBAAQE,MAAKH,KAAE,UAAUC,EAAC;AAAE,uBAAO,UAAU,eAAe,KAAKD,IAAEG,EAAC,MAAIJ,GAAEI,EAAC,IAAEH,GAAEG,EAAC;AAAG,mBAAOJ;AAAA,UAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,QAAC;AAAE,eAAO;AAAO,eAAO;AAAO,iBAAS,EAAEA,IAAE;AAAC,iBAAOA,GAAE,YAAY;AAAA,QAAC;AAAC,YAAI,IAAE,CAAC,sBAAqB,sBAAsB,GAAE,IAAE;AAAe,iBAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,cAAa,SAAOD,GAAE,QAAQC,IAAEC,EAAC,IAAED,GAAE,OAAQ,SAASD,IAAEC,IAAE;AAAC,mBAAOD,GAAE,QAAQC,IAAEC,EAAC;AAAA,UAAC,GAAGF,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAE;AAAC,iBAAO,WAASA,OAAIA,KAAE,CAAC,IAAG,SAASD,IAAEC,IAAE;AAAC,uBAASA,OAAIA,KAAE,CAAC;AAAG,qBAAQC,KAAED,GAAE,aAAYE,KAAE,WAASD,KAAE,IAAEA,IAAEE,KAAEH,GAAE,aAAYI,KAAE,WAASD,KAAE,IAAEA,IAAEM,KAAET,GAAE,WAAUU,KAAE,WAASD,KAAE,IAAEA,IAAEE,KAAEX,GAAE,WAAUY,KAAE,WAASD,KAAE,MAAIA,IAAEE,KAAE,EAAE,EAAEd,IAAEG,IAAE,QAAQ,GAAEE,IAAE,IAAI,GAAEiB,KAAE,GAAEP,KAAED,GAAE,QAAO,SAAOA,GAAE,OAAOQ,EAAC;AAAG,cAAAA;AAAI,mBAAK,SAAOR,GAAE,OAAOC,KAAE,CAAC;AAAG,cAAAA;AAAI,mBAAOD,GAAE,MAAMQ,IAAEP,EAAC,EAAE,MAAM,IAAI,EAAE,IAAIJ,EAAC,EAAE,KAAKE,EAAC;AAAA,UAAC,EAAEb,IAAE,EAAE,EAAC,WAAU,IAAG,GAAEC,EAAC,CAAC;AAAA,QAAC;AAAC,YAAI,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC;AAAE,iBAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAC,cAAM,IAAE,YAAU,UAAU,SAAS,YAAY,IAAEA,GAAE,QAAMA,GAAE;AAAM,cAAM,IAAE,SAASA,IAAEC,IAAE;AAAC,iBAAO,WAASA,OAAIA,KAAE,CAAC,IAAG,EAAED,IAAE,EAAE,EAAC,WAAU,IAAG,GAAEC,EAAC,CAAC;AAAA,QAAC;AAAA,QAAE,MAAM,UAAU,EAAE,EAAE;AAAA,UAAC,YAAYD,IAAEC,IAAE;AAAC,kBAAM,GAAE,EAAE,MAAK,QAAO,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,SAAQ,CAAC,CAAC,GAAE,KAAK,OAAKD,IAAE,KAAK,QAAMC;AAAA,UAAC;AAAA,UAAC,MAAMD,IAAEC,IAAEC,IAAE;AAAC,gBAAIC;AAAE,oBAAMF,MAAGA,GAAE,UAAQ,SAAKA,GAAEA,GAAE,SAAO,CAAC,MAAIC,KAAE,MAAGD,GAAE,IAAI;AAAG,kBAAMG,KAAEH,GAAE,OAAQ,CAACD,IAAEC,QAAKA,MAAGA,cAAa,QAAMD,MAAG,GAAGC,GAAE,OAAO,IAAIA,GAAE,KAAK,KAAGD,MAAGC,GAAE,SAAS,GAAED,KAAI,IAAI,KAAK,IAAI,MAAM,oBAAI,QAAM,mBAAmB,CAAC,IAAI;AAAE,gBAAIK;AAAE,aAAC,KAAK,MAAM,KAAK,CAACL,IAAEI,EAAC,CAAC,GAAEF,MAAG,UAAQC,KAAE,KAAK,UAAQ,WAASA,MAAGA,GAAE,aAAW,UAAQE,KAAE,YAAU,WAASA,MAAGA,GAAE,YAAUL,KAAE,UAAQ,OAAO,EAAE,GAAGA,EAAC,KAAKI,EAAC,EAAE;AAAG,iBAAK,KAAK,QAAQ;AAAA,UAAC;AAAA,UAAC,QAAO;AAAC,iBAAK,QAAM,CAAC,GAAE,KAAK,KAAK,QAAQ;AAAA,UAAC;AAAA,UAAC,QAAQJ,IAAE;AAAC,iBAAK,MAAM,QAAOA,EAAC;AAAA,UAAC;AAAA,UAAC,SAASA,IAAE;AAAC,iBAAK,MAAM,SAAQA,EAAC;AAAA,UAAC;AAAA,UAAC,QAAQA,IAAE;AAAC,iBAAK,MAAM,QAAOA,EAAC;AAAA,UAAC;AAAA,UAAC,OAAOA,IAAE;AAAC,iBAAK,OAAKA;AAAA,UAAC;AAAA,UAAC,SAAQ;AAAC,mBAAO,KAAK;AAAA,UAAK;AAAA,QAAC;AAAC,iBAAS,EAAEA,OAAKC,IAAE;AAAC,cAAG;AAAC,kBAAMC,KAAE,IAAI,IAAIF,EAAC;AAAE,gBAAG,CAACE,GAAE;AAAO,oBAAM,IAAI,MAAM,IAAI;AAAE,kBAAMC,KAAE,EAAE,KAAKH,GAAE,OAAOE,GAAE,OAAO,MAAM,GAAE,GAAGD,EAAC;AAAE,mBAAOC,GAAE,SAAOC;AAAA,UAAC,SAAOD,IAAE;AAAC,mBAAO,EAAE,KAAKF,IAAE,GAAGC,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAE;AAAC,cAAIC,IAAEC,IAAEC,KAAE;AAAG,gBAAMC,KAAE,CAAAJ,OAAG,CAAAC,OAAG;AAAC,YAAAF,MAAG,aAAaA,EAAC,GAAEC,GAAEC,EAAC,GAAEE,KAAE;AAAA,UAAE,GAAEE,KAAE,IAAI,QAAS,CAACF,IAAEE,OAAI;AAAC,YAAAJ,KAAEG,GAAED,EAAC,GAAED,KAAEE,GAAEC,EAAC,GAAEN,OAAIA,KAAE,WAAY,MAAIG,GAAE,IAAI,MAAM,sBAAsBF,EAAC,EAAE,CAAC,GAAGD,EAAC;AAAA,UAAE,CAAE;AAAE,iBAAM,EAAC,SAAQ,KAAK,IAAI,GAAE,QAAO,CAAAA,OAAGC,KAAED,IAAE,SAAQE,IAAE,QAAOC,IAAE,SAAQG,IAAE,IAAI,UAAS;AAAC,mBAAOF;AAAA,UAAC,EAAC;AAAA,QAAC;AAAC,cAAM,IAAE,oBAAI;AAAI,eAAO,sBAAoB;AAAE,YAAI,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC;AAAE,iBAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAC,cAAM,IAAE;AAAiC,YAAI,IAAE;AAAE,cAAM,IAAE,EAAC,WAAU,GAAE,mBAAkB,GAAE,MAAK,GAAE,MAAK,GAAE,OAAM,GAAE,SAAQ,EAAC,GAAE,IAAE,CAACA,IAAEC,QAAK,YAAU,OAAOA,MAAGD,GAAE,WAASC,QAAK,CAAC,CAACD,GAAE,UAAQ,YAAU,OAAOA,GAAE,QAAM,cAAaA,GAAE,UAAQA,GAAE,KAAK,SAAO,KAAG,CAAC,CAAC,EAAEA,GAAE,KAAK,QAAQ;AAAA,QAAK,MAAM,EAAC;AAAA,UAAC,YAAYA,IAAE;AAAC,cAAE,MAAK,UAAS,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,UAAS,CAAC,CAAC,GAAE,EAAE,MAAK,eAAc,MAAM,GAAE,EAAE,MAAK,YAAW,MAAM,GAAE,KAAK,SAAOA,GAAE,QAAO,KAAK,QAAMA,GAAE,OAAM,KAAK,QAAMA,GAAE,OAAM,KAAK,cAAYA,GAAE,aAAY,KAAK,WAAS,CAAAA,OAAG;AAAC,kBAAG,CAAC,EAAEA,IAAE,KAAK,WAAW;AAAE,uBAAM;AAAG,oBAAK,EAAC,MAAKC,IAAE,MAAKC,GAAC,MAAIF,MAAG,CAAC,GAAG,QAAM,CAAC,GAAG,SAAO,CAAC;AAAE,yBAASA,GAAE,KAAK,YAAUE,MAAK,KAAK,UAAQ,KAAK,OAAOA,EAAC,EAAE,QAAS,CAAAF,OAAG;AAAC,gBAAAA,GAAE,KAAK,MAAKC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,GAAE,KAAK,OAAO,iBAAiB,WAAU,KAAK,UAAS,KAAE;AAAA,UAAC;AAAA,UAAC,IAAID,OAAKC,IAAE;AAAC,mBAAO,IAAI,QAAS,CAACC,IAAEC,OAAI;AAAC,oBAAMC,KAAE,EAAE,GAAEC,KAAE,CAAAL,OAAG;AAAC,gBAAAA,GAAE,KAAK,QAAMI,MAAG,YAAUJ,GAAE,KAAK,aAAW,KAAK,OAAO,oBAAoB,WAAUK,IAAE,KAAE,GAAEL,GAAE,KAAK,QAAMG,GAAEH,GAAE,KAAK,KAAK,IAAEE,GAAEF,GAAE,KAAK,KAAK;AAAA,cAAE;AAAE,mBAAK,OAAO,iBAAiB,WAAUK,IAAE,KAAE,GAAE,KAAK,MAAM,YAAY,EAAC,UAAS,WAAU,MAAK,GAAE,UAASL,IAAE,MAAKC,IAAE,KAAIG,GAAC,GAAE,KAAK,WAAW;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,KAAKJ,IAAEC,IAAE;AAAC,iBAAK,MAAM,YAAY,EAAC,UAAS,QAAO,MAAK,GAAE,UAASD,IAAE,MAAKC,GAAC,GAAE,KAAK,WAAW;AAAA,UAAC;AAAA,UAAC,GAAGD,IAAEC,IAAE;AAAC,iBAAK,OAAOD,EAAC,MAAI,KAAK,OAAOA,EAAC,IAAE,CAAC,IAAG,KAAK,OAAOA,EAAC,EAAE,KAAKC,EAAC;AAAA,UAAC;AAAA,UAAC,UAAS;AAAC,mBAAO,oBAAoB,WAAU,KAAK,UAAS,KAAE,GAAE,KAAK,MAAM,WAAW,YAAY,KAAK,KAAK;AAAA,UAAC;AAAA,QAAC;AAAA,QAAC,MAAM,EAAC;AAAA,UAAC,YAAYD,IAAE;AAAC,cAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,UAAS,MAAM,GAAE,EAAE,MAAK,gBAAe,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,KAAK,QAAMA,GAAE,OAAM,KAAK,SAAOA,GAAE,QAAO,KAAK,eAAaA,GAAE,cAAa,KAAK,QAAMA,GAAE,OAAM,KAAK,MAAM,iBAAiB,WAAW,CAAAA,OAAG;AAAC,kBAAG,CAAC,EAAEA,IAAE,KAAK,YAAY;AAAE;AAAO,oBAAK,EAAC,UAASC,IAAE,KAAIC,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAEJ,GAAE;AAAK,yBAASA,GAAE,KAAK,YAAU,CAACA,IAAEC,IAAEC,OAAI;AAAC,sBAAMC,KAAE,cAAY,OAAOH,GAAEC,EAAC,IAAED,GAAEC,EAAC,EAAE,MAAM,MAAKC,EAAC,IAAEF,GAAEC,EAAC;AAAE,uBAAO,QAAQ,QAAQE,EAAC;AAAA,cAAC,GAAG,KAAK,OAAMF,IAAEG,EAAC,EAAE,KAAM,CAAAD,OAAG;AAAC,gBAAAH,GAAE,OAAO,YAAY,EAAC,UAASC,IAAE,UAAS,SAAQ,MAAK,GAAE,KAAIC,IAAE,OAAMC,GAAC,GAAEH,GAAE,MAAM;AAAA,cAAC,CAAE,EAAE,MAAO,CAAAG,OAAG;AAAC,gBAAAH,GAAE,OAAO,YAAY,EAAC,UAASC,IAAE,UAAS,SAAQ,MAAK,GAAE,KAAIC,IAAE,OAAMC,GAAC,GAAEH,GAAE,MAAM;AAAA,cAAC,CAAE,IAAEC,MAAK,KAAK,SAAO,cAAY,OAAO,KAAK,MAAMA,EAAC,KAAG,KAAK,MAAMA,EAAC,EAAEE,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,KAAKH,IAAEC,IAAE;AAAC,iBAAK,OAAO,YAAY,EAAC,UAAS,QAAO,MAAK,GAAE,OAAM,EAAC,MAAKD,IAAE,MAAKC,GAAC,EAAC,GAAE,KAAK,YAAY;AAAA,UAAC;AAAA,QAAC;AAAA,QAAC,MAAM,EAAC;AAAA,UAAC,YAAYD,IAAE;AAAC,cAAE,MAAK,aAAY,MAAM,GAAE,EAAE,MAAK,UAAS,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,eAAc,MAAM,GAAE,EAAE,MAAK,OAAM,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,KAAK,YAAUA,GAAE,WAAU,KAAK,MAAIA,GAAE,KAAI,KAAK,SAAO,QAAO,KAAK,QAAM,SAAS,cAAc,QAAQ,GAAEA,GAAE,OAAK,KAAK,MAAM,KAAGA,GAAE,KAAIA,GAAE,SAAO,KAAK,MAAM,OAAKA,GAAE,OAAM,KAAK,MAAM,UAAU,IAAI,MAAM,KAAK,MAAM,WAAUA,GAAE,kBAAgB,CAAC,CAAC,GAAE,KAAK,UAAU,YAAY,KAAK,KAAK,GAAE,KAAK,QAAM,KAAK,MAAM,eAAc,KAAK,QAAMA,GAAE,SAAO,CAAC;AAAA,UAAC;AAAA,UAAC,cAAcA,IAAE;AAAC,kBAAMC,MAAG,CAAAD,OAAG;AAAC,oBAAMC,KAAE,SAAS,cAAc,GAAG;AAAE,cAAAA,GAAE,OAAKD;AAAE,oBAAME,KAAED,GAAE,SAAS,SAAO,IAAEA,GAAE,WAAS,OAAO,SAAS,UAASE,KAAEF,GAAE,KAAK,SAAO,SAAOA,GAAE,QAAM,UAAQA,GAAE,OAAKA,GAAE,WAASA,GAAE,OAAK,OAAO,SAAS;AAAK,qBAAOA,GAAE,UAAQ,GAAGC,EAAC,KAAKC,EAAC;AAAA,YAAE,GAAGH,KAAEA,MAAG,KAAK,GAAG;AAAE,gBAAIE,IAAEC,KAAE;AAAE,mBAAO,IAAI,QAAS,CAACC,IAAEC,OAAI;AAAC,oBAAMC,KAAE,CAAAN,OAAG,CAAC,CAAC,EAAEA,IAAEC,EAAC,MAAI,sBAAoBD,GAAE,KAAK,YAAU,cAAcE,EAAC,GAAE,KAAK,OAAO,oBAAoB,WAAUI,IAAE,KAAE,GAAE,KAAK,cAAYN,GAAE,QAAOI,GAAE,IAAI,EAAE,IAAI,CAAC,KAAGC,GAAE,kBAAkB;AAAG,mBAAK,OAAO,iBAAiB,WAAUC,IAAE,KAAE;AAAE,oBAAMC,KAAE,MAAI;AAAC,gBAAAJ,MAAI,KAAK,MAAM,YAAY,EAAC,UAAS,aAAY,MAAK,GAAE,OAAM,KAAK,MAAK,GAAEF,EAAC,GAAE,MAAIE,MAAG,cAAcD,EAAC;AAAA,cAAC;AAAE,mBAAK,MAAM,iBAAiB,QAAQ,MAAI;AAAC,gBAAAK,GAAE,GAAEL,KAAE,YAAYK,IAAE,GAAG;AAAA,cAAC,CAAE,GAAE,KAAK,MAAM,MAAIP;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,UAAS;AAAC,iBAAK,MAAM,WAAW,YAAY,KAAK,KAAK;AAAA,UAAC;AAAA,QAAC;AAAC,UAAE,GAAE,SAAQ,KAAE,GAAE,EAAE,GAAE,SAAQ,MAAM;AAAA,QAAE,MAAM,EAAC;AAAA,UAAC,YAAYA,IAAE;AAAC,cAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,UAAS,MAAM,GAAE,EAAE,MAAK,gBAAe,MAAM,GAAE,KAAK,QAAM,QAAO,KAAK,QAAMA,IAAE,KAAK,SAAO,KAAK,MAAM;AAAA,UAAM;AAAA,UAAC,qBAAoB;AAAC,mBAAO,IAAI,QAAS,CAACA,IAAEC,OAAI;AAAC,oBAAMC,KAAE,CAAAC,OAAG;AAAC,oBAAGA,GAAE,KAAK,UAAS;AAAC,sBAAG,gBAAcA,GAAE,KAAK,UAAS;AAAC,uBAAE,KAAK,MAAM,oBAAoB,WAAUD,IAAE,KAAE,GAAEC,GAAE,OAAO,YAAY,EAAC,UAAS,mBAAkB,MAAK,EAAC,GAAEA,GAAE,MAAM,GAAE,KAAK,eAAaA,GAAE;AAAO,0BAAMF,KAAEE,GAAE,KAAK;AAAM,2BAAOF,MAAG,OAAO,KAAKA,EAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,2BAAK,MAAMA,EAAC,IAAEC,GAAED,EAAC;AAAA,oBAAC,CAAE,GAAEA,GAAE,IAAI,EAAE,IAAI,CAAC;AAAA,kBAAC;AAAC,yBAAOC,GAAE,wBAAwB;AAAA,gBAAC;AAAA,cAAC;AAAE,mBAAK,MAAM,iBAAiB,WAAUC,IAAE,KAAE;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAC,cAAK,EAAC,YAAW,GAAE,wBAAuB,EAAC,IAAE,OAAO,YAAU,CAAC;AAAE,iBAAS,EAAEA,IAAEC,IAAE;AAAC,iBAAOD,GAAE,WAAW,MAAM,IAAE,MAAMA,IAAEC,EAAC,KAAGD,KAAEA,GAAE,QAAQ,WAAU,EAAE,GAAE,IAAI,QAAS,OAAMC,IAAEC,OAAI;AAAC,gBAAG;AAAC,oBAAMA,KAAE,MAAM,OAAO,KAAK,SAAS,CAAC,YAAWF,EAAC,CAAC;AAAE,cAAAC,GAAE,EAAC,MAAK,MAAIC,GAAC,CAAC;AAAA,YAAC,SAAOF,IAAE;AAAC,sBAAQ,MAAMA,EAAC,GAAEE,GAAEF,EAAC;AAAA,YAAC;AAAA,UAAC,CAAE;AAAA,QAAE;AAAA,QAAC,MAAM,UAAU,EAAE,EAAE;AAAA,UAAC,YAAYA,IAAE;AAAC,kBAAM,GAAE,EAAE,MAAK,gBAAe,MAAM,GAAE,EAAE,MAAK,UAAS,MAAM,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,WAAU,KAAE,GAAE,EAAE,MAAK,eAAc,CAAC,CAAC,GAAE,KAAK,eAAaA,IAAEA,GAAE,SAAU,MAAI;AAAC,mBAAK,SAAS;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,MAAM,OAAM;AAAC,kBAAK,EAAC,MAAKA,IAAE,OAAMC,GAAC,IAAE,KAAK,aAAa;AAAQ,gBAAG,KAAK,UAAQ,CAACA;AAAE;AAAO,kBAAK,EAAC,UAASC,IAAE,aAAYC,GAAC,IAAE,MAAM,EAAEF,IAAE,EAAC,OAAM,EAAC,CAAC;AAAE,iBAAK,OAAOC,IAAE,SAAS,IAAI;AAAE,kBAAME,KAAE,EAAEJ,IAAE,EAAC,eAAc,MAAI;AAAC,kBAAIA;AAAE,qBAAO,UAAQA,KAAE,KAAK,UAAQ,WAASA,KAAE,SAAOA,GAAE;AAAA,YAAU,EAAC,CAAC,EAAE,SAAS;AAAM,YAAAI,GAAE,kBAAgB,MAAGA,GAAE,gBAAc,KAAK,cAAaA,GAAE,iBAAe,MAAKA,GAAE,eAAaA,GAAE,SAAO,IAAI,GAAG,KAAK,aAAa,OAAO,GAAE,KAAK,aAAa,MAAM;AAAE,kBAAMC,KAAE,MAAMF,GAAEC,IAAE,IAAE;AAAE,iBAAK,YAAY,KAAKC,GAAE,OAAO,GAAE,KAAK,UAAQ;AAAA,UAAE;AAAA,UAAC,OAAOL,IAAEC,IAAE;AAAC,kBAAMC,KAAE,KAAK,SAAO,SAAS,cAAc,KAAK;AAAE,YAAAA,GAAE,UAAU,IAAI,oBAAoB,GAAEA,GAAE,KAAG,KAAK,aAAa,IAAG,KAAK,QAAMA,GAAE,aAAa,EAAC,MAAK,OAAM,CAAC,GAAE,KAAK,MAAM,YAAU,QAAQF,EAAC,UAASC,GAAE,YAAYC,EAAC,GAAE,KAAK,KAAK,SAAS;AAAA,UAAC;AAAA,UAAC,WAAU;AAAC,uBAAUF,MAAK,KAAK;AAAY,cAAAA,MAAGA,GAAE,KAAK,IAAI;AAAA,UAAC;AAAA,UAAC,UAAS;AAAC,gBAAIA,IAAEC;AAAE,sBAAQD,KAAE,KAAK,UAAQ,WAASA,MAAG,UAAQC,KAAED,GAAE,eAAa,WAASC,MAAGA,GAAE,YAAY,KAAK,KAAK;AAAA,UAAC;AAAA,UAAC,IAAI,SAAQ;AAAC,mBAAO,KAAK;AAAA,UAAO;AAAA,UAAC,IAAI,WAAU;AAAC,gBAAID;AAAE,mBAAO,UAAQA,KAAE,KAAK,UAAQ,WAASA,KAAE,SAAOA,GAAE;AAAA,UAAU;AAAA,UAAC,IAAI,QAAO;AAAC,mBAAO,KAAK;AAAA,UAAM;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAC,cAAM,IAAE,EAAE,EAAE,iBAAiB,GAAE,IAAE,oBAAmB,IAAE,YAAW,IAAE,kBAAiB,IAAE,CAAAA,OAAG,WAAWA,EAAC;AAAA,QAAG,MAAM,UAAU,EAAE,EAAE;AAAA,UAAC,YAAYA,IAAE;AAAC,kBAAM,GAAE,EAAE,MAAK,gBAAe,MAAM,GAAE,EAAE,MAAK,cAAa,KAAE,GAAE,EAAE,MAAK,WAAU,MAAM,GAAE,EAAE,MAAK,UAAS,MAAM,GAAE,EAAE,MAAK,WAAU,MAAM,GAAE,EAAE,MAAK,WAAU,MAAM,GAAE,EAAE,MAAK,cAAa,CAAC,CAAC,GAAE,EAAE,MAAK,SAAQ,MAAM,GAAE,EAAE,MAAK,kBAAiB,MAAM,GAAE,EAAE,MAAK,aAAY,EAAE,GAAE,KAAK,eAAaA,IAAEA,OAAI,KAAK,YAAUA,GAAE;AAAA,UAAS;AAAA,UAAC,MAAM,iBAAgB;AAAC,gBAAG,KAAK;AAAW;AAAO,kBAAK,EAAC,QAAOA,GAAC,IAAE,KAAK;AAAa,YAAAA,KAAE,MAAM,KAAK,oBAAoB,IAAE,MAAM,KAAK,oBAAoB;AAAA,UAAC;AAAA,UAAC,MAAM,gBAAgBA,KAAE,CAAC,GAAE;AAAC,gBAAG,KAAK;AAAW;AAAO,kBAAMC,KAAE,MAAKC,KAAE,QAAM,KAAK;AAAa,gBAAIC,KAAE,GAAEC,KAAE;AAAE,kBAAMC,KAAE,oBAAI,OAAIC,KAAE,EAAE,GAAG,GAAEC,KAAE,KAAK,iBAAiB,EAAC,kBAAiB,OAAMP,OAAG;AAAC,cAAAO,GAAE,EAAE,QAAMP,KAAE,SAAOA,GAAE,GAAG,CAAC,IAAE,CAAC,EAAC,MAAKA,IAAE,SAAQE,GAAC,MAAI;AAAC,kBAAE,2BAA2B,KAAK,SAAS,IAAGF,IAAEE,EAAC,GAAED,GAAE,KAAKD,IAAEE,EAAC;AAAA,cAAC,GAAE,MAAMI,GAAE,QAAQ;AAAA,YAAC,GAAE,yBAAwB,OAAMN,OAAG;AAAC,oBAAME,KAAE,EAAE,GAAG;AAAE,cAAAD,GAAE,KAAK,gBAAe,OAAO,OAAO,EAAC,OAAMC,GAAC,GAAEF,EAAC,CAAC,GAAE,MAAME,GAAE;AAAA,YAAO,GAAE,qBAAoB,OAAM,EAAC,MAAKF,IAAE,SAAQE,GAAC,MAAI;AAAC,cAAAD,GAAE,KAAK,oBAAmBC,EAAC;AAAA,YAAC,GAAE,CAAC,CAAC,GAAE,OAAM,EAAC,IAAGF,IAAE,MAAKE,IAAE,SAAQC,GAAC,MAAI;AAAC,gBAAE,2BAA2B,KAAK,SAAS,OAAOH,EAAC,SAASE,EAAC,IAAGC,EAAC,GAAEH,MAAGA,GAAE,WAAW,MAAM,IAAEC,GAAE,KAAK,GAAGD,EAAC,IAAIE,EAAC,IAAGC,EAAC,IAAEF,GAAE,KAAKC,IAAEC,EAAC;AAAA,YAAC,GAAE,kBAAiB,CAAC,EAAC,OAAMH,IAAE,QAAOC,GAAC,MAAI;AAAC,kBAAG,EAAE,yBAAyBD,EAAC,IAAGC,EAAC,GAAEI,GAAE,IAAIL,EAAC,GAAE;AAAC,sBAAME,KAAEG,GAAE,IAAIL,EAAC;AAAE,gBAAAE,OAAI,QAAMD,MAAGA,GAAE,eAAe,CAAC,IAAEC,GAAE,OAAOD,GAAE,CAAC,CAAC,IAAEC,GAAE,QAAQD,EAAC,GAAEI,GAAE,OAAOL,EAAC;AAAA,cAAE;AAAA,YAAC,GAAE,GAAGA,GAAC,CAAC;AAAE,gBAAIQ;AAAE,gBAAGN;AAAE,qBAAO,MAAMI,GAAE,SAAQ,KAAK,MAAM,KAAK,UAAU,UAAQE,KAAE,KAAK,iBAAe,WAASA,KAAE,SAAOA,GAAE,OAAO,CAAC,CAAC;AAAE,kBAAMC,KAAE,IAAI,EAAEF,EAAC,EAAE,mBAAmB;AAAE,mBAAO,KAAK,UAAQ,WAAU,MAAME,GAAE,KAAM,CAAAT,OAAG;AAAC,mBAAK,SAAOA,IAAE,KAAK,aAAW,MAAG,KAAK,QAAM,OAAMC,IAAEC,KAAE,CAAC,GAAEC,OAAI;AAAC,oBAAGA,IAAE;AAAC,wBAAMH,KAAE,EAAEI;AAAE,kBAAAC,GAAE,IAAIL,IAAEG,EAAC,GAAED,GAAE,QAAMF,IAAEG,GAAE,OAAO,eAAeH,EAAC,EAAE,GAAE,EAAE,eAAeA,EAAC,EAAE;AAAA,gBAAC;AAAC,uBAAOA,GAAE,KAAK,EAAEO,GAAE,SAAS,EAAE,GAAE,EAAC,MAAKN,IAAE,SAAQC,GAAC,CAAC,GAAE,QAAMC,KAAE,SAAOA,GAAE;AAAA,cAAO,GAAE,KAAK,iBAAe,OAAMH,IAAEC,OAAI;AAAC,oBAAG;AAAC,kBAAAM,GAAEP,EAAC,EAAEC,EAAC;AAAA,gBAAC,SAAOA,IAAE;AAAC,oBAAE,mBAAmBD,EAAC,cAAc;AAAA,gBAAC;AAAA,cAAC,GAAEG,KAAE,YAAa,MAAI;AAAC,oBAAGE,GAAE,OAAK;AAAI,6BAAS,CAACL,IAAEC,EAAC,KAAII;AAAE,oBAAAJ,GAAE,WAASI,GAAE,OAAOL,EAAC;AAAA,cAAC,GAAG,IAAI;AAAA,YAAC,CAAE,EAAE,QAAS,MAAI;AAAC,mBAAK,UAAQ;AAAA,YAAM,CAAE,GAAE,MAAMM,GAAE,SAAQC,GAAE;AAAA,UAAQ;AAAA,UAAC,MAAM,KAAKP,IAAEC,KAAE,CAAC,GAAE;AAAC,gBAAIC;AAAE,mBAAO,UAAQA,KAAE,KAAK,UAAQ,WAASA,KAAE,SAAOA,GAAE,KAAK,MAAKF,IAAEC,EAAC;AAAA,UAAC;AAAA,UAAC,MAAM,UAAUD,IAAEC,KAAE,CAAC,GAAE;AAAC,gBAAIC;AAAE,kBAAMC,KAAE,EAAE,GAAG;AAAE,mBAAO,UAAQD,KAAE,KAAK,UAAQ,WAASA,KAAE,SAAOA,GAAE,KAAK,MAAKF,IAAEC,IAAEE,EAAC;AAAA,UAAC;AAAA,UAAC,MAAM,cAAcH,OAAKC,IAAE;AAAC,gBAAIC;AAAE,mBAAO,UAAQA,KAAE,KAAK,mBAAiB,WAASA,KAAE,SAAOA,GAAE,MAAM,MAAK,CAACF,IAAE,GAAGC,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,MAAM,mBAAmBD,OAAKC,IAAE;AAAC,gBAAIC;AAAE,mBAAOF,KAAE,GAAG,CAAC,GAAGA,EAAC,IAAG,UAAQE,KAAE,KAAK,mBAAiB,WAASA,KAAE,SAAOA,GAAE,MAAM,MAAK,CAACF,IAAE,GAAGC,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,MAAM,sBAAqB;AAAC,kBAAMD,KAAE,KAAK,cAAaC,KAAED,GAAE,IAAGE,KAAE,GAAGD,EAAC,aAAYE,KAAE,IAAI,IAAIH,GAAE,QAAQ,KAAK;AAAE,YAAAG,GAAE,aAAa,IAAI,SAAQH,GAAE,QAAQ,OAAO;AAAE,kBAAMI,KAAE,SAAS,cAAc,IAAIF,EAAC,EAAE;AAAE,YAAAE,MAAGA,GAAE,cAAc,YAAYA,EAAC;AAAE,kBAAMC,KAAE,SAAS,cAAc,KAAK;AAAE,YAAAA,GAAE,UAAU,IAAI,8BAA8B,GAAEA,GAAE,KAAGH,IAAEG,GAAE,QAAQ,MAAIJ;AAAE,gBAAG;AAAC,kBAAIK;AAAE,oBAAMN,KAAE,UAAQM,KAAE,MAAM,KAAK,aAAa,iBAAiB,MAAI,WAASA,KAAE,SAAOA,GAAE;AAAI,kBAAGN,IAAE;AAAC,gBAAAK,GAAE,QAAQ,gBAAc;AAAO,oBAAG,EAAC,OAAMJ,IAAE,QAAOC,IAAE,MAAKC,IAAE,KAAIC,IAAE,IAAGE,IAAE,IAAGC,GAAC,IAAEP;AAAE,gBAAAG,KAAE,KAAK,IAAIA,IAAE,CAAC,GAAEA,KAAE,YAAU,OAAOG,KAAE,GAAG,KAAK,IAAI,MAAIH,KAAEG,IAAE,EAAE,CAAC,MAAI,GAAGH,EAAC,MAAKC,KAAE,KAAK,IAAIA,IAAE,EAAE,GAAEA,KAAE,YAAU,OAAOG,KAAE,GAAG,KAAK,IAAI,MAAIH,KAAEG,IAAE,EAAE,CAAC,MAAI,GAAGH,EAAC,MAAK,OAAO,OAAOC,GAAE,OAAM,EAAC,OAAMJ,KAAE,MAAK,QAAOC,KAAE,MAAK,MAAKC,IAAE,KAAIC,GAAC,CAAC;AAAA,cAAC;AAAA,YAAC,SAAOJ,IAAE;AAAC,sBAAQ,MAAM,0BAAyBA,EAAC;AAAA,YAAC;AAAC,qBAAS,KAAK,YAAYK,EAAC;AAAE,kBAAME,KAAE,IAAI,EAAE,EAAC,IAAGN,KAAE,WAAU,WAAUI,IAAE,KAAIF,GAAE,MAAK,gBAAe,CAAC,oBAAoB,GAAE,OAAM,EAAC,UAAS,KAAK,MAAM,KAAK,UAAUH,GAAE,OAAO,CAAC,CAAC,EAAC,EAAC,CAAC;AAAE,gBAAIQ,IAAEC,KAAEF,GAAE,cAAc;AAAE,mBAAO,KAAK,UAAQ,WAAU,IAAI,QAAS,CAACN,IAAEC,OAAI;AAAC,cAAAM,KAAE,WAAY,MAAI;AAAC,gBAAAN,GAAE,IAAI,MAAM,mBAAmB,CAAC,GAAEK,GAAE,QAAQ;AAAA,cAAC,GAAG,GAAG,GAAEE,GAAE,KAAM,CAAAP,OAAG;AAAC,qBAAK,UAAQA,IAAE,KAAK,aAAW,MAAG,KAAK,KAAK,WAAW,GAAEA,GAAE,GAAG,EAAEF,GAAE,EAAE,GAAG,CAAC,EAAC,MAAKA,IAAE,SAAQC,GAAC,MAAI;AAAC,sBAAIC,IAAEC;AAAE,oBAAE,oBAAmBH,IAAEC,EAAC,GAAE,UAAQC,KAAE,KAAK,iBAAe,WAASA,MAAGA,GAAE,KAAKF,IAAEC,MAAG,CAAC,CAAC,GAAE,UAAQE,KAAE,KAAK,iBAAe,WAASA,MAAGA,GAAE,OAAO,KAAKH,IAAEC,MAAG,CAAC,CAAC;AAAA,gBAAC,CAAE,GAAE,KAAK,QAAM,UAASA,OAAI;AAAC,wBAAMC,GAAE,KAAK,EAAEF,GAAE,EAAE,GAAE,EAAC,MAAKC,GAAE,CAAC,GAAE,SAAQ,OAAO,OAAOA,GAAE,CAAC,KAAG,CAAC,GAAE,EAAC,OAAMD,GAAE,GAAE,CAAC,EAAC,CAAC;AAAA,gBAAC,GAAE,KAAK,iBAAe,OAAMA,OAAKC,OAAI;AAAC,sBAAGD,GAAE,WAAW,CAAC;AAAE,2BAAO,MAAME,GAAE,IAAIF,GAAE,QAAQ,GAAE,EAAE,GAAE,GAAGC,EAAC;AAAE,kBAAAC,GAAE,KAAKF,IAAE,QAAMC,KAAE,SAAOA,GAAE,CAAC,CAAC;AAAA,gBAAC,GAAEA,GAAE,IAAI;AAAA,cAAC,CAAE,EAAE,MAAO,CAAAD,OAAG;AAAC,gBAAAE,GAAEF,EAAC;AAAA,cAAC,CAAE,EAAE,QAAS,MAAI;AAAC,6BAAaQ,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE,EAAE,MAAO,CAAAR,OAAG;AAAC,oBAAM,EAAE,0BAAyBA,EAAC,GAAEA;AAAA,YAAC,CAAE,EAAE,QAAS,MAAI;AAAC,mBAAK,UAAQ;AAAA,YAAM,CAAE;AAAA,UAAC;AAAA,UAAC,MAAM,sBAAqB;AAAC,kBAAMA,KAAE,KAAK,cAAaC,KAAE,KAAK,UAAQ,IAAI,EAAED,EAAC;AAAE,gBAAG;AAAC,mBAAK,UAAQ,WAAU,MAAMC,GAAE,KAAK,GAAE,KAAK,aAAW,MAAG,KAAK,KAAK,WAAW,GAAE,KAAK,QAAM,OAAMA,IAAEC,KAAE,CAAC,GAAEC,OAAI;AAAC,oBAAIC;AAAE,uBAAOD,OAAID,GAAE,QAAMC,KAAG,UAAQC,KAAE,KAAK,iBAAe,WAASA,MAAGA,GAAE,KAAKH,IAAE,OAAO,OAAOC,IAAE,EAAC,OAAMF,GAAE,GAAE,CAAC,CAAC,GAAE,QAAMG,KAAE,SAAOA,GAAE;AAAA,cAAO,GAAE,KAAK,iBAAe,UAASH,OAAI;AAAC,oBAAIC;AAAE,oBAAIC,KAAEF,GAAE,CAAC;AAAE,0BAAQC,KAAEC,OAAI,WAASD,MAAGA,GAAE,WAAW,CAAC,MAAIC,KAAEA,GAAE,QAAQ,GAAE,EAAE;AAAG,sBAAMC,KAAEH,GAAE,CAAC,KAAG,CAAC,GAAEI,KAAE,KAAK,WAAWF,EAAC;AAAE,8BAAY,OAAOE,MAAG,MAAMA,GAAE,KAAK,MAAKD,EAAC;AAAA,cAAC;AAAA,YAAC,SAAOH,IAAE;AAAC,oBAAM,EAAE,0BAAyBA,EAAC,GAAEA;AAAA,YAAC,UAAC;AAAQ,mBAAK,UAAQ;AAAA,YAAM;AAAA,UAAC;AAAA,UAAC,iBAAiBA,IAAE;AAAC,mBAAO,OAAO,OAAO,KAAK,YAAWA,EAAC;AAAA,UAAC;AAAA,UAAC,6BAA4B;AAAC,gBAAIA;AAAE,mBAAO,UAAQA,KAAE,KAAK,YAAU,WAASA,KAAE,SAAOA,GAAE,MAAM;AAAA,UAAU;AAAA,UAAC,6BAA4B;AAAC,gBAAIA;AAAE,mBAAO,UAAQA,KAAE,KAAK,YAAU,WAASA,KAAE,SAAOA,GAAE,MAAM;AAAA,UAAU;AAAA,UAAC,wBAAuB;AAAC,gBAAIA;AAAE,mBAAO,UAAQA,KAAE,KAAK,YAAU,WAASA,KAAE,SAAOA,GAAE;AAAA,UAAK;AAAA,UAAC,wBAAuB;AAAC,gBAAIA;AAAE,mBAAO,UAAQA,KAAE,KAAK,YAAU,WAASA,KAAE,SAAOA,GAAE;AAAA,UAAK;AAAA,UAAC,IAAI,SAASA,IAAE;AAAC,iBAAK,YAAUA;AAAA,UAAC;AAAA,UAAC,MAAM,UAAS;AAAC,gBAAIA;AAAE,gBAAIC,KAAE;AAAK,iBAAK,YAAUA,KAAE,KAAK,2BAA2B,GAAE,MAAM,KAAK,QAAQ,QAAQ,IAAG,KAAK,YAAUA,KAAE,KAAK,2BAA2B,GAAE,KAAK,QAAQ,QAAQ,IAAG,UAAQD,KAAEC,OAAI,WAASD,MAAGA,GAAE,WAAW,YAAYC,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAA,QAAC,MAAM,EAAC;AAAA,UAAC,YAAYA,IAAEC,IAAE;AAAC,cAAE,MAAK,OAAM,MAAM,GAAE,EAAE,MAAK,QAAO,MAAM,GAAE,KAAK,MAAID,IAAE,KAAK,OAAKC;AAAA,UAAC;AAAA,UAAC,IAAI,QAAO;AAAC,mBAAO,KAAK,IAAI,SAAS;AAAA,UAAE;AAAA,UAAC,QAAQD,IAAEC,IAAE;AAAC,gBAAIC;AAAE,mBAAO,KAAK,IAAI,OAAO,UAAU,YAAW,EAAC,QAAO,6BAA4B,MAAK,CAAC,KAAK,OAAMF,IAAEC,IAAE,UAAQC,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,MAAM,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,QAAQF,IAAE;AAAC,gBAAIC;AAAE,mBAAO,KAAK,IAAI,OAAO,UAAU,YAAW,EAAC,QAAO,4BAA2B,MAAK,CAAC,KAAK,OAAMD,IAAE,UAAQC,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,MAAM,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,WAAWD,IAAE;AAAC,gBAAIC;AAAE,mBAAO,KAAK,IAAI,OAAO,KAAK,YAAW,EAAC,QAAO,8BAA6B,MAAK,CAAC,KAAK,OAAMD,IAAE,UAAQC,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,MAAM,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,UAAS;AAAC,gBAAID;AAAE,mBAAO,KAAK,IAAI,OAAO,UAAU,YAAW,EAAC,QAAO,6BAA4B,MAAK,CAAC,KAAK,OAAM,UAAQA,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,MAAM,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,QAAO;AAAC,gBAAIA;AAAE,mBAAO,KAAK,IAAI,OAAO,KAAK,YAAW,EAAC,QAAO,8BAA6B,MAAK,CAAC,KAAK,OAAM,UAAQA,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,MAAM,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,QAAQA,IAAE;AAAC,gBAAIC;AAAE,mBAAO,KAAK,IAAI,OAAO,UAAU,YAAW,EAAC,QAAO,6BAA4B,MAAK,CAAC,KAAK,OAAMD,IAAE,UAAQC,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,MAAM,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,QAAC,MAAM,EAAC;AAAA,UAAC,YAAYD,IAAE;AAAC,gBAAIC,IAAEC,IAAEC;AAAE,YAAAA,KAAE,SAAQD,KAAE,WAASD,KAAE,QAAM,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAE,KAAK,MAAIH;AAAA,UAAC;AAAA,UAAC,IAAI,QAAO;AAAC,mBAAO,KAAK,gBAAgB,EAAE;AAAA,UAAK;AAAA,UAAC,IAAI,WAAU;AAAC,mBAAO,KAAK,gBAAgB,EAAE;AAAA,UAAQ;AAAA,UAAC,IAAI,cAAa;AAAC,mBAAO,KAAK,gBAAgB,EAAE,aAAa,aAAa,KAAK,IAAI,SAAS,EAAE;AAAA,UAAC;AAAA,UAAC,kBAAkBA,OAAKC,IAAE;AAAC,gBAAIC,IAAEC;AAAE,kBAAMC,KAAE,KAAK,gBAAgB;AAAE,mBAAOJ,KAAE,UAAQE,KAAE,EAAEF,EAAC,MAAI,WAASE,KAAE,SAAOA,GAAE,YAAY,GAAE,UAAQC,KAAEC,GAAE,OAAO,IAAI,WAASJ,EAAC,MAAI,WAASG,KAAE,SAAOA,GAAE,MAAMC,IAAEH,EAAC;AAAA,UAAC;AAAA,UAAC,MAAM,eAAeD,IAAE;AAAC,aAACA,KAAEA,GAAE,IAAK,CAAAA,OAAG,QAAMA,MAAGA,GAAE,WAAW,MAAM,IAAEA,KAAE,KAAK,IAAI,uBAAuBA,EAAC,CAAE,GAAG,QAAQ,KAAK,IAAI,SAAS,EAAE,GAAE,MAAM,KAAK,kBAAkB,eAAc,GAAGA,EAAC;AAAA,UAAC;AAAA,UAAC,2BAA2BA,IAAEC,IAAE;AAAC,mBAAO,KAAK,gBAAgB,EAAE,OAAO,IAAI,oCAAoC,KAAK,IAAI,SAAS,IAAGD,IAAEC,EAAC;AAAA,UAAC;AAAA,UAAC,2BAA2BD,IAAEC,IAAE;AAAC,kBAAMC,KAAE,KAAK,gBAAgB;AAAE,gBAAG,YAAUF;AAAE,cAAAE,GAAE,SAAOD,GAAEC,GAAE,KAAK,EAAE,MAAM,QAAQ,KAAK;AAAE,mBAAOA,GAAE,OAAO,IAAI,mCAAmC,KAAK,IAAI,SAAS,IAAGF,IAAEC,EAAC;AAAA,UAAC;AAAA,UAAC,kBAAiB;AAAC,gBAAG,WAAS;AAAI,oBAAM,IAAI,MAAM,4BAA4B;AAAE,mBAAO;AAAA,UAAG;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAC,cAAM,IAAE,CAAAA,OAAG,iBAAiBA,EAAC;AAAA,QAAG,MAAM,EAAC;AAAA,UAAC,YAAYA,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,cAAE,MAAK,WAAU,MAAM,GAAE,EAAE,MAAK,cAAa,MAAM,GAAE,EAAE,MAAK,mBAAkB,MAAM,GAAE,EAAE,MAAK,YAAW,MAAM,GAAE,EAAE,MAAK,YAAW,KAAE,GAAE,KAAK,UAAQF,IAAE,KAAK,aAAWC,IAAE,KAAK,kBAAgBC,IAAE,KAAK,WAAS,IAAI,QAAS,CAACF,IAAEC,OAAI;AAAC,kBAAG,CAAC,KAAK;AAAW,uBAAOA,GAAE,IAAI;AAAE,mBAAK,QAAQ,KAAK,EAAE,KAAK,UAAU,GAAG,CAAAC,OAAG;AAAC,gBAAAA,MAAGA,cAAa,QAAMD,GAAEC,EAAC,IAAEF,GAAEE,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE;AAAE,kBAAK,EAAC,SAAQC,IAAE,MAAKC,IAAE,OAAMC,GAAC,IAAE,KAAK;AAAgB,iBAAK,SAAS,KAAM,CAAAL,OAAG;AAAC,sBAAMG,MAAGA,GAAEH,EAAC;AAAA,YAAC,CAAE,EAAE,MAAO,CAAAA,OAAG;AAAC,sBAAMI,MAAGA,GAAEJ,EAAC;AAAA,YAAC,CAAE,EAAE,QAAS,MAAI;AAAC,sBAAMK,MAAGA,GAAE;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,QAAO;AAAC,iBAAK,gBAAgB,aAAW,CAAC,KAAK,aAAW,KAAK,QAAQ,IAAI,iBAAiB,sBAAqB,KAAK,UAAU,GAAE,KAAK,WAAS;AAAA,UAAG;AAAA,UAAC,IAAI,UAAS;AAAC,mBAAO,KAAK;AAAA,UAAQ;AAAA,UAAC,IAAI,SAAQ;AAAC,mBAAO,KAAK;AAAA,UAAO;AAAA,UAAC,IAAI,YAAW;AAAC,mBAAO,KAAK;AAAA,UAAU;AAAA,QAAC;AAAA,QAAC,MAAM,UAAU,EAAE,aAAY;AAAA,UAAC,YAAYL,IAAE;AAAC,kBAAM,GAAE,EAAE,MAAK,QAAO,MAAM,GAAE,KAAK,OAAKA,IAAE,KAAK,IAAI,OAAO,GAAG,yBAAyB,CAAAA,OAAG;AAAC,oBAAMC,KAAE,QAAMD,KAAE,SAAOA,GAAE;AAAU,cAAAC,MAAG,KAAK,KAAK,EAAEA,EAAC,GAAE,QAAMD,KAAE,SAAOA,GAAE,OAAO;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,OAAO,kBAAkBA,IAAEC,IAAEC,IAAE;AAAC,mBAAO,IAAI,EAAEF,IAAEC,IAAEC,EAAC;AAAA,UAAC;AAAA,UAAC,MAAM,SAASF,IAAE;AAAC,kBAAMC,KAAE,KAAK,IAAI,SAAS,IAAG,EAAC,SAAQC,IAAE,MAAKC,IAAE,OAAMC,IAAE,GAAGC,GAAC,IAAEL,IAAEM,KAAE,KAAK,IAAI,YAAY,kBAAkB,WAAUL,IAAEI,EAAC,GAAEE,KAAE,EAAE,kBAAkB,KAAK,IAAI,SAAQD,IAAEN,EAAC;AAAE,mBAAOK,GAAE,YAAUE,KAAEA,GAAE;AAAA,UAAO;AAAA,UAAC,IAAI,MAAK;AAAC,mBAAO,KAAK;AAAA,UAAI;AAAA,QAAC;AAAC,cAAM,IAAE,MAAM;AAAQ,cAAM,IAAE,YAAU,OAAO,UAAQ,UAAQ,OAAO,WAAS,UAAQ;AAAO,YAAI,IAAE,YAAU,OAAO,QAAM,QAAM,KAAK,WAAS,UAAQ;AAAK,cAAM,IAAE,KAAG,KAAG,SAAS,aAAa,EAAE;AAAE,cAAM,KAAG,EAAE;AAAO,YAAI,KAAG,OAAO,WAAU,KAAG,GAAG,gBAAe,KAAG,GAAG,UAAS,KAAG,KAAG,GAAG,cAAY;AAAO,cAAM,KAAG,SAASP,IAAE;AAAC,cAAIC,KAAE,GAAG,KAAKD,IAAE,EAAE,GAAEE,KAAEF,GAAE,EAAE;AAAE,cAAG;AAAC,YAAAA,GAAE,EAAE,IAAE;AAAO,gBAAIG,KAAE;AAAA,UAAE,SAAOH,IAAE;AAAA,UAAC;AAAC,cAAII,KAAE,GAAG,KAAKJ,EAAC;AAAE,iBAAOG,OAAIF,KAAED,GAAE,EAAE,IAAEE,KAAE,OAAOF,GAAE,EAAE,IAAGI;AAAA,QAAC;AAAE,YAAI,KAAG,OAAO,UAAU;AAAS,cAAM,KAAG,SAASJ,IAAE;AAAC,iBAAO,GAAG,KAAKA,EAAC;AAAA,QAAC;AAAE,YAAI,KAAG,KAAG,GAAG,cAAY;AAAO,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,QAAMA,KAAE,WAASA,KAAE,uBAAqB,kBAAgB,MAAI,MAAM,OAAOA,EAAC,IAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,cAAIC,KAAE,OAAOD;AAAE,iBAAO,QAAMA,OAAI,YAAUC,MAAG,cAAYA;AAAA,QAAE;AAAE,cAAM,KAAG,SAASD,IAAE;AAAC,cAAG,CAAC,GAAGA,EAAC;AAAE,mBAAM;AAAG,cAAIC,KAAE,GAAGD,EAAC;AAAE,iBAAM,uBAAqBC,MAAG,gCAA8BA,MAAG,4BAA0BA,MAAG,oBAAkBA;AAAA,QAAC;AAAE,cAAM,KAAG,EAAE,oBAAoB;AAAE,YAAI,IAAG,MAAI,KAAG,SAAS,KAAK,MAAI,GAAG,QAAM,GAAG,KAAK,YAAU,EAAE,KAAG,mBAAiB,KAAG;AAAG,cAAM,KAAG,SAASD,IAAE;AAAC,iBAAM,CAAC,CAAC,MAAI,MAAMA;AAAA,QAAC;AAAE,YAAI,KAAG,SAAS,UAAU;AAAS,cAAM,KAAG,SAASA,IAAE;AAAC,cAAG,QAAMA,IAAE;AAAC,gBAAG;AAAC,qBAAO,GAAG,KAAKA,EAAC;AAAA,YAAC,SAAOA,IAAE;AAAA,YAAC;AAAC,gBAAG;AAAC,qBAAOA,KAAE;AAAA,YAAE,SAAOA,IAAE;AAAA,YAAC;AAAA,UAAC;AAAC,iBAAM;AAAA,QAAE;AAAE,YAAI,KAAG,+BAA8B,KAAG,SAAS,WAAU,KAAG,OAAO,WAAU,KAAG,GAAG,UAAS,KAAG,GAAG,gBAAe,KAAG,OAAO,MAAI,GAAG,KAAK,EAAE,EAAE,QAAQ,uBAAsB,MAAM,EAAE,QAAQ,0DAAyD,OAAO,IAAE,GAAG;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAM,EAAE,CAAC,GAAGA,EAAC,KAAG,GAAGA,EAAC,OAAK,GAAGA,EAAC,IAAE,KAAG,IAAI,KAAK,GAAGA,EAAC,CAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,iBAAO,QAAMD,KAAE,SAAOA,GAAEC,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,cAAIC,KAAE,GAAGF,IAAEC,EAAC;AAAE,iBAAO,GAAGC,EAAC,IAAEA,KAAE;AAAA,QAAM;AAAE,cAAM,KAAG,WAAU;AAAC,cAAG;AAAC,gBAAIF,KAAE,GAAG,QAAO,gBAAgB;AAAE,mBAAOA,GAAE,CAAC,GAAE,IAAG,CAAC,CAAC,GAAEA;AAAA,UAAC,SAAOA,IAAE;AAAA,UAAC;AAAA,QAAC,EAAE;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,yBAAaD,MAAG,KAAG,GAAGD,IAAEC,IAAE,EAAC,cAAa,MAAG,YAAW,MAAG,OAAMC,IAAE,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,iBAAO,SAASC,IAAEC,IAAEC,IAAE;AAAC,qBAAQC,KAAE,IAAGC,KAAE,OAAOJ,EAAC,GAAEK,KAAEH,GAAEF,EAAC,GAAEM,KAAED,GAAE,QAAOC,QAAK;AAAC,kBAAIC,KAAEF,GAAEN,KAAEO,KAAE,EAAEH,EAAC;AAAE,kBAAG,UAAKF,GAAEG,GAAEG,EAAC,GAAEA,IAAEH,EAAC;AAAE;AAAA,YAAK;AAAC,mBAAOJ;AAAA,UAAC;AAAA,QAAC,EAAE;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,mBAAQC,KAAE,IAAGC,KAAE,MAAMH,EAAC,GAAE,EAAEE,KAAEF;AAAG,YAAAG,GAAED,EAAC,IAAED,GAAEC,EAAC;AAAE,iBAAOC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASH,IAAE;AAAC,iBAAO,QAAMA,MAAG,YAAU,OAAOA;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,GAAGA,EAAC,KAAG,wBAAsB,GAAGA,EAAC;AAAA,QAAC;AAAE,YAAI,KAAG,OAAO,WAAU,KAAG,GAAG,gBAAe,KAAG,GAAG;AAAqB,cAAM,KAAG,GAAG,WAAU;AAAC,iBAAO;AAAA,QAAS,EAAE,CAAC,IAAE,KAAG,SAASA,IAAE;AAAC,iBAAO,GAAGA,EAAC,KAAG,GAAG,KAAKA,IAAE,QAAQ,KAAG,CAAC,GAAG,KAAKA,IAAE,QAAQ;AAAA,QAAC;AAAE,cAAM,KAAG,WAAU;AAAC,iBAAM;AAAA,QAAE;AAAE,YAAI,KAAG,YAAU,OAAO,WAAS,WAAS,CAAC,QAAQ,YAAU,SAAQ,KAAG,MAAI,YAAU,OAAO,UAAQ,UAAQ,CAAC,OAAO,YAAU,QAAO,KAAG,MAAI,GAAG,YAAU,KAAG,EAAE,SAAO;AAAO,cAAM,MAAI,KAAG,GAAG,WAAS,WAAS;AAAG,YAAI,KAAG;AAAmB,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,OAAOF;AAAE,iBAAM,CAAC,EAAEC,KAAE,QAAMA,KAAE,mBAAiBA,QAAK,YAAUC,MAAG,YAAUA,MAAG,GAAG,KAAKF,EAAC,MAAIA,KAAE,MAAIA,KAAE,KAAG,KAAGA,KAAEC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAE;AAAC,iBAAM,YAAU,OAAOA,MAAGA,KAAE,MAAIA,KAAE,KAAG,KAAGA,MAAG;AAAA,QAAgB;AAAE,YAAI,KAAG,CAAC;AAAE,WAAG,uBAAuB,IAAE,GAAG,uBAAuB,IAAE,GAAG,oBAAoB,IAAE,GAAG,qBAAqB,IAAE,GAAG,qBAAqB,IAAE,GAAG,qBAAqB,IAAE,GAAG,4BAA4B,IAAE,GAAG,sBAAsB,IAAE,GAAG,sBAAsB,IAAE,MAAG,GAAG,oBAAoB,IAAE,GAAG,gBAAgB,IAAE,GAAG,sBAAsB,IAAE,GAAG,kBAAkB,IAAE,GAAG,mBAAmB,IAAE,GAAG,eAAe,IAAE,GAAG,gBAAgB,IAAE,GAAG,mBAAmB,IAAE,GAAG,cAAc,IAAE,GAAG,iBAAiB,IAAE,GAAG,iBAAiB,IAAE,GAAG,iBAAiB,IAAE,GAAG,cAAc,IAAE,GAAG,iBAAiB,IAAE,GAAG,kBAAkB,IAAE;AAAG,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,GAAGA,EAAC,KAAG,GAAGA,GAAE,MAAM,KAAG,CAAC,CAAC,GAAG,GAAGA,EAAC,CAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,SAASC,IAAE;AAAC,mBAAOD,GAAEC,EAAC;AAAA,UAAC;AAAA,QAAC;AAAE,YAAI,KAAG,YAAU,OAAO,WAAS,WAAS,CAAC,QAAQ,YAAU,SAAQ,KAAG,MAAI,YAAU,OAAO,UAAQ,UAAQ,CAAC,OAAO,YAAU,QAAO,KAAG,MAAI,GAAG,YAAU,MAAI,EAAE,SAAQ,KAAG,WAAU;AAAC,cAAG;AAAC,gBAAID,KAAE,MAAI,GAAG,WAAS,GAAG,QAAQ,MAAM,EAAE;AAAM,mBAAOA,MAAG,MAAI,GAAG,WAAS,GAAG,QAAQ,MAAM;AAAA,UAAC,SAAOA,IAAE;AAAA,UAAC;AAAA,QAAC,EAAE;AAAE,YAAI,KAAG,MAAI,GAAG;AAAa,cAAM,KAAG,KAAG,GAAG,EAAE,IAAE;AAAG,YAAI,KAAG,OAAO,UAAU;AAAe,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAEF,EAAC,GAAEG,KAAE,CAACD,MAAG,GAAGF,EAAC,GAAEI,KAAE,CAACF,MAAG,CAACC,MAAG,GAAGH,EAAC,GAAEK,KAAE,CAACH,MAAG,CAACC,MAAG,CAACC,MAAG,GAAGJ,EAAC,GAAEM,KAAEJ,MAAGC,MAAGC,MAAGC,IAAEE,KAAED,KAAE,GAAGN,GAAE,QAAO,MAAM,IAAE,CAAC,GAAEQ,KAAED,GAAE;AAAO,mBAAQE,MAAKT;AAAE,aAACC,MAAG,CAAC,GAAG,KAAKD,IAAES,EAAC,KAAGH,OAAI,YAAUG,MAAGL,OAAI,YAAUK,MAAG,YAAUA,OAAIJ,OAAI,YAAUI,MAAG,gBAAcA,MAAG,gBAAcA,OAAI,GAAGA,IAAED,EAAC,MAAID,GAAE,KAAKE,EAAC;AAAE,iBAAOF;AAAA,QAAC;AAAE,YAAI,KAAG,OAAO;AAAU,cAAM,KAAG,SAASP,IAAE;AAAC,cAAIC,KAAED,MAAGA,GAAE;AAAY,iBAAOA,QAAK,cAAY,OAAOC,MAAGA,GAAE,aAAW;AAAA,QAAG;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,iBAAO,SAASC,IAAE;AAAC,mBAAOF,GAAEC,GAAEC,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC,EAAE,OAAO,MAAK,MAAM;AAAE,YAAI,KAAG,OAAO,UAAU;AAAe,cAAM,KAAG,SAASF,IAAE;AAAC,cAAG,CAAC,GAAGA,EAAC;AAAE,mBAAO,GAAGA,EAAC;AAAE,cAAIC,KAAE,CAAC;AAAE,mBAAQC,MAAK,OAAOF,EAAC;AAAE,eAAG,KAAKA,IAAEE,EAAC,KAAG,iBAAeA,MAAGD,GAAE,KAAKC,EAAC;AAAE,iBAAOD;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAE;AAAC,iBAAO,QAAMA,MAAG,GAAGA,GAAE,MAAM,KAAG,CAAC,GAAGA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,GAAGA,EAAC,IAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,iBAAOD,MAAG,GAAGA,IAAEC,IAAE,EAAE;AAAA,QAAC;AAAE,cAAM,KAAG,WAAU;AAAC,eAAK,WAAS,CAAC,GAAE,KAAK,OAAK;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,iBAAOD,OAAIC,MAAGD,MAAGA,MAAGC,MAAGA;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,mBAAQC,KAAEF,GAAE,QAAOE;AAAK,gBAAG,GAAGF,GAAEE,EAAC,EAAE,CAAC,GAAED,EAAC;AAAE,qBAAOC;AAAE,iBAAM;AAAA,QAAE;AAAE,YAAI,KAAG,MAAM,UAAU;AAAO,cAAM,KAAG,SAASF,IAAE;AAAC,cAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAED,EAAC;AAAE,iBAAM,EAAEE,KAAE,OAAKA,MAAGD,GAAE,SAAO,IAAEA,GAAE,IAAI,IAAE,GAAG,KAAKA,IAAEC,IAAE,CAAC,GAAE,EAAE,KAAK,MAAK;AAAA,QAAG;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,cAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAED,EAAC;AAAE,iBAAOE,KAAE,IAAE,SAAOD,GAAEC,EAAC,EAAE,CAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,iBAAO,GAAG,KAAK,UAASA,EAAC,IAAE;AAAA,QAAE;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAEF,EAAC;AAAE,iBAAOG,KAAE,KAAG,EAAE,KAAK,MAAKD,GAAE,KAAK,CAACF,IAAEC,EAAC,CAAC,KAAGC,GAAEC,EAAC,EAAE,CAAC,IAAEF,IAAE;AAAA,QAAI;AAAE,iBAAS,GAAGD,IAAE;AAAC,cAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,eAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,gBAAIC,KAAEH,GAAEC,EAAC;AAAE,iBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,WAAG,UAAU,QAAM,IAAG,GAAG,UAAU,SAAO,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI;AAAG,cAAM,KAAG;AAAG,cAAM,KAAG,WAAU;AAAC,eAAK,WAAS,IAAI,MAAG,KAAK,OAAK;AAAA,QAAC;AAAE,cAAM,KAAG,SAASH,IAAE;AAAC,cAAIC,KAAE,KAAK,UAASC,KAAED,GAAE,OAAOD,EAAC;AAAE,iBAAO,KAAK,OAAKC,GAAE,MAAKC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,iBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,GAAG,GAAE,KAAK;AAAE,cAAM,KAAG,GAAG,QAAO,QAAQ;AAAE,cAAM,KAAG,WAAU;AAAC,eAAK,WAAS,KAAG,GAAG,IAAI,IAAE,CAAC,GAAE,KAAK,OAAK;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,cAAIC,KAAE,KAAK,IAAID,EAAC,KAAG,OAAO,KAAK,SAASA,EAAC;AAAE,iBAAO,KAAK,QAAMC,KAAE,IAAE,GAAEA;AAAA,QAAC;AAAE,YAAI,KAAG,OAAO,UAAU;AAAe,cAAM,KAAG,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK;AAAS,cAAG,IAAG;AAAC,gBAAIC,KAAED,GAAED,EAAC;AAAE,mBAAM,gCAA8BE,KAAE,SAAOA;AAAA,UAAC;AAAC,iBAAO,GAAG,KAAKD,IAAED,EAAC,IAAEC,GAAED,EAAC,IAAE;AAAA,QAAM;AAAE,YAAI,KAAG,OAAO,UAAU;AAAe,cAAM,KAAG,SAASA,IAAE;AAAC,cAAIC,KAAE,KAAK;AAAS,iBAAO,KAAG,WAASA,GAAED,EAAC,IAAE,GAAG,KAAKC,IAAED,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK;AAAS,iBAAO,KAAK,QAAM,KAAK,IAAIF,EAAC,IAAE,IAAE,GAAEE,GAAEF,EAAC,IAAE,MAAI,WAASC,KAAE,8BAA4BA,IAAE;AAAA,QAAI;AAAE,iBAAS,GAAGD,IAAE;AAAC,cAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,eAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,gBAAIC,KAAEH,GAAEC,EAAC;AAAE,iBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,WAAG,UAAU,QAAM,IAAG,GAAG,UAAU,SAAO,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI;AAAG,cAAM,KAAG;AAAG,cAAM,KAAG,WAAU;AAAC,eAAK,OAAK,GAAE,KAAK,WAAS,EAAC,MAAK,IAAI,MAAG,KAAI,KAAI,MAAI,OAAI,QAAO,IAAI,KAAE;AAAA,QAAC;AAAE,cAAM,KAAG,SAASH,IAAE;AAAC,cAAIC,KAAE,OAAOD;AAAE,iBAAM,YAAUC,MAAG,YAAUA,MAAG,YAAUA,MAAG,aAAWA,KAAE,gBAAcD,KAAE,SAAOA;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAEF,GAAE;AAAS,iBAAO,GAAGC,EAAC,IAAEC,GAAE,YAAU,OAAOD,KAAE,WAAS,MAAM,IAAEC,GAAE;AAAA,QAAG;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,cAAIC,KAAE,GAAG,MAAKD,EAAC,EAAE,OAAOA,EAAC;AAAE,iBAAO,KAAK,QAAMC,KAAE,IAAE,GAAEA;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAE;AAAC,iBAAO,GAAG,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,GAAG,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,GAAG,MAAKF,EAAC,GAAEG,KAAED,GAAE;AAAK,iBAAOA,GAAE,IAAIF,IAAEC,EAAC,GAAE,KAAK,QAAMC,GAAE,QAAMC,KAAE,IAAE,GAAE;AAAA,QAAI;AAAE,iBAAS,GAAGH,IAAE;AAAC,cAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,eAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,gBAAIC,KAAEH,GAAEC,EAAC;AAAE,iBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,WAAG,UAAU,QAAM,IAAG,GAAG,UAAU,SAAO,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI;AAAG,cAAM,KAAG;AAAG,cAAM,KAAG,SAASH,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK;AAAS,cAAGA,cAAa,IAAG;AAAC,gBAAIC,KAAED,GAAE;AAAS,gBAAG,CAAC,MAAIC,GAAE,SAAO;AAAI,qBAAOA,GAAE,KAAK,CAACH,IAAEC,EAAC,CAAC,GAAE,KAAK,OAAK,EAAEC,GAAE,MAAK;AAAK,YAAAA,KAAE,KAAK,WAAS,IAAI,GAAGC,EAAC;AAAA,UAAC;AAAC,iBAAOD,GAAE,IAAIF,IAAEC,EAAC,GAAE,KAAK,OAAKC,GAAE,MAAK;AAAA,QAAI;AAAE,iBAAS,GAAGF,IAAE;AAAC,cAAIC,KAAE,KAAK,WAAS,IAAI,GAAGD,EAAC;AAAE,eAAK,OAAKC,GAAE;AAAA,QAAI;AAAC,WAAG,UAAU,QAAM,IAAG,GAAG,UAAU,SAAO,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI,IAAG,GAAG,UAAU,MAAI;AAAG,cAAM,KAAG;AAAG,cAAM,KAAG,SAASD,IAAE;AAAC,iBAAO,KAAK,SAAS,IAAIA,IAAE,2BAA2B,GAAE;AAAA,QAAI;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,QAAC;AAAE,iBAAS,GAAGA,IAAE;AAAC,cAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,eAAI,KAAK,WAAS,IAAI,MAAG,EAAEC,KAAEC;AAAG,iBAAK,IAAIF,GAAEC,EAAC,CAAC;AAAA,QAAC;AAAC,WAAG,UAAU,MAAI,GAAG,UAAU,OAAK,IAAG,GAAG,UAAU,MAAI;AAAG,cAAM,KAAG;AAAG,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,mBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAO,EAAEE,KAAEC;AAAG,gBAAGF,GAAED,GAAEE,EAAC,GAAEA,IAAEF,EAAC;AAAE,qBAAM;AAAG,iBAAM;AAAA,QAAE;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,iBAAOD,GAAE,IAAIC,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAE,IAAEJ,IAAEK,KAAEP,GAAE,QAAOQ,KAAEP,GAAE;AAAO,cAAGM,MAAGC,MAAG,EAAEF,MAAGE,KAAED;AAAG,mBAAM;AAAG,cAAIE,KAAEJ,GAAE,IAAIL,EAAC,GAAEU,KAAEL,GAAE,IAAIJ,EAAC;AAAE,cAAGQ,MAAGC;AAAE,mBAAOD,MAAGR,MAAGS,MAAGV;AAAE,cAAIW,KAAE,IAAGC,KAAE,MAAGC,KAAE,IAAEX,KAAE,IAAI,OAAG;AAAO,eAAIG,GAAE,IAAIL,IAAEC,EAAC,GAAEI,GAAE,IAAIJ,IAAED,EAAC,GAAE,EAAEW,KAAEJ,MAAG;AAAC,gBAAIO,KAAEd,GAAEW,EAAC,GAAEW,KAAErB,GAAEU,EAAC;AAAE,gBAAGR;AAAE,kBAAIY,KAAET,KAAEH,GAAEmB,IAAER,IAAEH,IAAEV,IAAED,IAAEK,EAAC,IAAEF,GAAEW,IAAEQ,IAAEX,IAAEX,IAAEC,IAAEI,EAAC;AAAE,gBAAG,WAASU,IAAE;AAAC,kBAAGA;AAAE;AAAS,cAAAH,KAAE;AAAG;AAAA,YAAK;AAAC,gBAAGC,IAAE;AAAC,kBAAG,CAAC,GAAGZ,IAAG,SAASD,IAAEC,IAAE;AAAC,oBAAG,CAAC,GAAGY,IAAEZ,EAAC,MAAIa,OAAId,MAAGI,GAAEU,IAAEd,IAAEE,IAAEC,IAAEE,EAAC;AAAG,yBAAOQ,GAAE,KAAKZ,EAAC;AAAA,cAAC,CAAE,GAAE;AAAC,gBAAAW,KAAE;AAAG;AAAA,cAAK;AAAA,YAAC,WAASE,OAAIQ,MAAG,CAAClB,GAAEU,IAAEQ,IAAEpB,IAAEC,IAAEE,EAAC,GAAE;AAAC,cAAAO,KAAE;AAAG;AAAA,YAAK;AAAA,UAAC;AAAC,iBAAOP,GAAE,OAAOL,EAAC,GAAEK,GAAE,OAAOJ,EAAC,GAAEW;AAAA,QAAC;AAAE,cAAM,KAAG,EAAE;AAAW,cAAM,KAAG,SAASZ,IAAE;AAAC,cAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,iBAAOA,GAAE,QAAS,SAASA,IAAEG,IAAE;AAAC,YAAAD,GAAE,EAAED,EAAC,IAAE,CAACE,IAAEH,EAAC;AAAA,UAAC,CAAE,GAAEE;AAAA,QAAC;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,cAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,iBAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,YAAAE,GAAE,EAAED,EAAC,IAAED;AAAA,UAAC,CAAE,GAAEE;AAAA,QAAC;AAAE,YAAI,KAAG,KAAG,GAAG,YAAU,QAAO,KAAG,KAAG,GAAG,UAAQ;AAAO,cAAM,KAAG,SAASF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,kBAAOJ,IAAE;AAAA,YAAC,KAAI;AAAoB,kBAAGF,GAAE,cAAYC,GAAE,cAAYD,GAAE,cAAYC,GAAE;AAAW,uBAAM;AAAG,cAAAD,KAAEA,GAAE,QAAOC,KAAEA,GAAE;AAAA,YAAO,KAAI;AAAuB,qBAAM,EAAED,GAAE,cAAYC,GAAE,cAAY,CAACI,GAAE,IAAI,GAAGL,EAAC,GAAE,IAAI,GAAGC,EAAC,CAAC;AAAA,YAAG,KAAI;AAAA,YAAmB,KAAI;AAAA,YAAgB,KAAI;AAAkB,qBAAO,GAAG,CAACD,IAAE,CAACC,EAAC;AAAA,YAAE,KAAI;AAAiB,qBAAOD,GAAE,QAAMC,GAAE,QAAMD,GAAE,WAASC,GAAE;AAAA,YAAQ,KAAI;AAAA,YAAkB,KAAI;AAAkB,qBAAOD,MAAGC,KAAE;AAAA,YAAG,KAAI;AAAe,kBAAIM,KAAE;AAAA,YAAG,KAAI;AAAe,kBAAIC,KAAE,IAAEL;AAAE,kBAAGI,OAAIA,KAAE,KAAIP,GAAE,QAAMC,GAAE,QAAM,CAACO;AAAE,uBAAM;AAAG,kBAAIC,KAAEH,GAAE,IAAIN,EAAC;AAAE,kBAAGS;AAAE,uBAAOA,MAAGR;AAAE,cAAAE,MAAG,GAAEG,GAAE,IAAIN,IAAEC,EAAC;AAAE,kBAAIS,KAAE,GAAGH,GAAEP,EAAC,GAAEO,GAAEN,EAAC,GAAEE,IAAEC,IAAEC,IAAEC,EAAC;AAAE,qBAAOA,GAAE,OAAON,EAAC,GAAEU;AAAA,YAAE,KAAI;AAAkB,kBAAG;AAAG,uBAAO,GAAG,KAAKV,EAAC,KAAG,GAAG,KAAKC,EAAC;AAAA,UAAC;AAAC,iBAAM;AAAA,QAAE;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,mBAAQC,KAAE,IAAGC,KAAEF,GAAE,QAAOG,KAAEJ,GAAE,QAAO,EAAEE,KAAEC;AAAG,YAAAH,GAAEI,KAAEF,EAAC,IAAED,GAAEC,EAAC;AAAE,iBAAOF;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAEF,GAAED,EAAC;AAAE,iBAAO,EAAEA,EAAC,IAAEG,KAAE,GAAGA,IAAED,GAAEF,EAAC,CAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,mBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAOI,KAAE,GAAEC,KAAE,CAAC,GAAE,EAAEH,KAAEC,MAAG;AAAC,gBAAIG,KAAEN,GAAEE,EAAC;AAAE,YAAAD,GAAEK,IAAEJ,IAAEF,EAAC,MAAIK,GAAED,IAAG,IAAEE;AAAA,UAAE;AAAC,iBAAOD;AAAA,QAAC;AAAE,cAAM,KAAG,WAAU;AAAC,iBAAM,CAAC;AAAA,QAAC;AAAE,YAAI,KAAG,OAAO,UAAU,sBAAqB,KAAG,OAAO;AAAsB,cAAM,KAAG,KAAG,SAASL,IAAE;AAAC,iBAAO,QAAMA,KAAE,CAAC,KAAGA,KAAE,OAAOA,EAAC,GAAE,GAAG,GAAGA,EAAC,GAAG,SAASC,IAAE;AAAC,mBAAO,GAAG,KAAKD,IAAEC,EAAC;AAAA,UAAC,CAAE;AAAA,QAAE,IAAE;AAAG,cAAM,KAAG,SAASD,IAAE;AAAC,iBAAO,GAAGA,IAAE,IAAG,EAAE;AAAA,QAAC;AAAE,YAAI,KAAG,OAAO,UAAU;AAAe,cAAM,KAAG,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAE,IAAEJ,IAAEK,KAAE,GAAGP,EAAC,GAAEQ,KAAED,GAAE;AAAO,cAAGC,MAAG,GAAGP,EAAC,EAAE,UAAQ,CAACK;AAAE,mBAAM;AAAG,mBAAQG,KAAED,IAAEC,QAAK;AAAC,gBAAIC,KAAEH,GAAEE,EAAC;AAAE,gBAAG,EAAEH,KAAEI,MAAKT,KAAE,GAAG,KAAKA,IAAES,EAAC;AAAG,qBAAM;AAAA,UAAE;AAAC,cAAIC,KAAEN,GAAE,IAAIL,EAAC,GAAEY,KAAEP,GAAE,IAAIJ,EAAC;AAAE,cAAGU,MAAGC;AAAE,mBAAOD,MAAGV,MAAGW,MAAGZ;AAAE,cAAIa,KAAE;AAAG,UAAAR,GAAE,IAAIL,IAAEC,EAAC,GAAEI,GAAE,IAAIJ,IAAED,EAAC;AAAE,mBAAQc,KAAER,IAAE,EAAEG,KAAED,MAAG;AAAC,gBAAIc,KAAEtB,GAAEU,KAAEH,GAAEE,EAAC,CAAC,GAAEM,KAAEd,GAAES,EAAC;AAAE,gBAAGP;AAAE,kBAAIa,KAAEV,KAAEH,GAAEY,IAAEO,IAAEZ,IAAET,IAAED,IAAEK,EAAC,IAAEF,GAAEmB,IAAEP,IAAEL,IAAEV,IAAEC,IAAEI,EAAC;AAAE,gBAAG,EAAE,WAASW,KAAEM,OAAIP,MAAGX,GAAEkB,IAAEP,IAAEb,IAAEC,IAAEE,EAAC,IAAEW,KAAG;AAAC,cAAAH,KAAE;AAAG;AAAA,YAAK;AAAC,YAAAC,OAAIA,KAAE,iBAAeJ;AAAA,UAAE;AAAC,cAAGG,MAAG,CAACC,IAAE;AAAC,gBAAIG,KAAEjB,GAAE,aAAYkB,KAAEjB,GAAE;AAAY,YAAAgB,MAAGC,MAAG,EAAE,iBAAgBlB,OAAI,EAAE,iBAAgBC,OAAI,cAAY,OAAOgB,MAAGA,cAAaA,MAAG,cAAY,OAAOC,MAAGA,cAAaA,OAAIL,KAAE;AAAA,UAAG;AAAC,iBAAOR,GAAE,OAAOL,EAAC,GAAEK,GAAE,OAAOJ,EAAC,GAAEY;AAAA,QAAC;AAAE,cAAM,KAAG,GAAG,GAAE,UAAU;AAAE,cAAM,KAAG,GAAG,GAAE,SAAS;AAAE,cAAM,KAAG,GAAG,GAAE,KAAK;AAAE,cAAM,KAAG,GAAG,GAAE,SAAS;AAAE,YAAI,KAAG,gBAAe,KAAG,oBAAmB,KAAG,gBAAe,KAAG,oBAAmB,KAAG,qBAAoB,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG;AAAG,SAAC,MAAI,GAAG,IAAI,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,KAAG,MAAI,MAAI,GAAG,IAAI,IAAE,KAAG,MAAI,MAAI,GAAG,GAAG,QAAQ,CAAC,KAAG,MAAI,MAAI,GAAG,IAAI,IAAE,KAAG,MAAI,MAAI,GAAG,IAAI,IAAE,KAAG,QAAM,KAAG,SAASb,IAAE;AAAC,cAAIC,KAAE,GAAGD,EAAC,GAAEE,KAAE,qBAAmBD,KAAED,GAAE,cAAY,QAAOG,KAAED,KAAE,GAAGA,EAAC,IAAE;AAAG,cAAGC;AAAE,oBAAOA,IAAE;AAAA,cAAC,KAAK;AAAG,uBAAO;AAAA,cAAG,KAAK;AAAG,uBAAO;AAAA,cAAG,KAAK;AAAG,uBAAO;AAAA,cAAG,KAAK;AAAG,uBAAO;AAAA,cAAG,KAAK;AAAG,uBAAO;AAAA,YAAE;AAAC,iBAAOF;AAAA,QAAC;AAAG,cAAM,KAAG;AAAG,YAAI,KAAG,sBAAqB,KAAG,kBAAiB,KAAG,mBAAkB,KAAG,OAAO,UAAU;AAAe,cAAM,KAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAEN,EAAC,GAAEO,KAAE,EAAEN,EAAC,GAAEO,KAAEF,KAAE,KAAG,GAAGN,EAAC,GAAES,KAAEF,KAAE,KAAG,GAAGN,EAAC,GAAES,MAAGF,KAAEA,MAAG,KAAG,KAAGA,OAAI,IAAGG,MAAGF,KAAEA,MAAG,KAAG,KAAGA,OAAI,IAAGG,KAAEJ,MAAGC;AAAE,cAAGG,MAAG,GAAGZ,EAAC,GAAE;AAAC,gBAAG,CAAC,GAAGC,EAAC;AAAE,qBAAM;AAAG,YAAAK,KAAE,MAAGI,KAAE;AAAA,UAAE;AAAC,cAAGE,MAAG,CAACF;AAAE,mBAAOL,OAAIA,KAAE,IAAI,OAAIC,MAAG,GAAGN,EAAC,IAAE,GAAGA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,EAAC,IAAE,GAAGL,IAAEC,IAAEO,IAAEN,IAAEC,IAAEC,IAAEC,EAAC;AAAE,cAAG,EAAE,IAAEH,KAAG;AAAC,gBAAIW,KAAEH,MAAG,GAAG,KAAKV,IAAE,aAAa,GAAEc,KAAEH,MAAG,GAAG,KAAKV,IAAE,aAAa;AAAE,gBAAGY,MAAGC,IAAE;AAAC,kBAAIQ,KAAET,KAAEb,GAAE,MAAM,IAAEA,IAAEe,KAAED,KAAEb,GAAE,MAAM,IAAEA;AAAE,qBAAOI,OAAIA,KAAE,IAAI,OAAID,GAAEkB,IAAEP,IAAEb,IAAEC,IAAEE,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,iBAAM,CAAC,CAACO,OAAIP,OAAIA,KAAE,IAAI,OAAI,GAAGL,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAA,QAAE;AAAE,cAAM,KAAG,SAASL,GAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAOJ,OAAIC,OAAI,QAAMD,MAAG,QAAMC,MAAG,CAAC,GAAGD,EAAC,KAAG,CAAC,GAAGC,EAAC,IAAED,MAAGA,MAAGC,MAAGA,KAAE,GAAGD,IAAEC,IAAEC,IAAEC,IAAEJ,IAAEK,EAAC;AAAA,QAAE;AAAE,cAAM,KAAG,SAASL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAEF,GAAE,QAAOG,KAAED,IAAEE,KAAE,CAACH;AAAE,cAAG,QAAMH;AAAE,mBAAM,CAACK;AAAE,eAAIL,KAAE,OAAOA,EAAC,GAAEI,QAAK;AAAC,gBAAIG,KAAEL,GAAEE,EAAC;AAAE,gBAAGE,MAAGC,GAAE,CAAC,IAAEA,GAAE,CAAC,MAAIP,GAAEO,GAAE,CAAC,CAAC,IAAE,EAAEA,GAAE,CAAC,KAAIP;AAAG,qBAAM;AAAA,UAAE;AAAC,iBAAK,EAAEI,KAAEC,MAAG;AAAC,gBAAIG,MAAGD,KAAEL,GAAEE,EAAC,GAAG,CAAC,GAAEK,KAAET,GAAEQ,EAAC,GAAEE,KAAEH,GAAE,CAAC;AAAE,gBAAGD,MAAGC,GAAE,CAAC,GAAE;AAAC,kBAAG,WAASE,MAAG,EAAED,MAAKR;AAAG,uBAAM;AAAA,YAAE,OAAK;AAAC,kBAAIW,KAAE,IAAI;AAAG,kBAAGR;AAAE,oBAAIS,KAAET,GAAEM,IAAEC,IAAEF,IAAER,IAAEC,IAAEU,EAAC;AAAE,kBAAG,EAAE,WAASC,KAAE,GAAGF,IAAED,IAAE,GAAEN,IAAEQ,EAAC,IAAEC;AAAG,uBAAM;AAAA,YAAE;AAAA,UAAC;AAAC,iBAAM;AAAA,QAAE;AAAE,cAAM,KAAG,SAASZ,IAAE;AAAC,iBAAOA,MAAGA,MAAG,CAAC,GAAGA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,mBAAQC,KAAE,GAAGD,EAAC,GAAEE,KAAED,GAAE,QAAOC,QAAK;AAAC,gBAAIC,KAAEF,GAAEC,EAAC,GAAEE,KAAEJ,GAAEG,EAAC;AAAE,YAAAF,GAAEC,EAAC,IAAE,CAACC,IAAEC,IAAE,GAAGA,EAAC,CAAC;AAAA,UAAC;AAAC,iBAAOH;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,iBAAO,SAASC,IAAE;AAAC,mBAAO,QAAMA,OAAIA,GAAEF,EAAC,MAAIC,OAAI,WAASA,MAAGD,MAAK,OAAOE,EAAC;AAAA,UAAG;AAAA,QAAC;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,cAAIC,KAAE,GAAGD,EAAC;AAAE,iBAAO,KAAGC,GAAE,UAAQA,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC,IAAE,SAASC,IAAE;AAAC,mBAAOA,OAAIF,MAAG,GAAGE,IAAEF,IAAEC,EAAC;AAAA,UAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAE;AAAC,iBAAM,YAAU,OAAOA,MAAG,GAAGA,EAAC,KAAG,qBAAmB,GAAGA,EAAC;AAAA,QAAC;AAAE,YAAI,KAAG,oDAAmD,KAAG;AAAQ,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAG,EAAED,EAAC;AAAE,mBAAM;AAAG,cAAIE,KAAE,OAAOF;AAAE,iBAAM,EAAE,YAAUE,MAAG,YAAUA,MAAG,aAAWA,MAAG,QAAMF,MAAG,CAAC,GAAGA,EAAC,OAAK,GAAG,KAAKA,EAAC,KAAG,CAAC,GAAG,KAAKA,EAAC,KAAG,QAAMC,MAAGD,MAAK,OAAOC,EAAC;AAAA,QAAE;AAAE,iBAAS,GAAGD,IAAEC,IAAE;AAAC,cAAG,cAAY,OAAOD,MAAG,QAAMC,MAAG,cAAY,OAAOA;AAAE,kBAAM,IAAI,UAAU,qBAAqB;AAAE,cAAIC,KAAE,WAAU;AAAC,gBAAIC,KAAE,WAAUC,KAAEH,KAAEA,GAAE,MAAM,MAAKE,EAAC,IAAEA,GAAE,CAAC,GAAEE,KAAEH,GAAE;AAAM,gBAAGG,GAAE,IAAID,EAAC;AAAE,qBAAOC,GAAE,IAAID,EAAC;AAAE,gBAAIE,KAAEN,GAAE,MAAM,MAAKG,EAAC;AAAE,mBAAOD,GAAE,QAAMG,GAAE,IAAID,IAAEE,EAAC,KAAGD,IAAEC;AAAA,UAAC;AAAE,iBAAOJ,GAAE,QAAM,KAAI,GAAG,SAAO,OAAIA;AAAA,QAAC;AAAC,WAAG,QAAM;AAAG,cAAM,KAAG;AAAG,YAAI,KAAG,oGAAmG,KAAG;AAAW,cAAM,KAAG,SAASF,IAAE;AAAC,cAAIC,KAAE,GAAGD,IAAG,SAASA,IAAE;AAAC,mBAAO,QAAME,GAAE,QAAMA,GAAE,MAAM,GAAEF;AAAA,UAAC,CAAE,GAAEE,KAAED,GAAE;AAAM,iBAAOA;AAAA,QAAC,EAAG,SAASD,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,iBAAO,OAAKD,GAAE,WAAW,CAAC,KAAGC,GAAE,KAAK,EAAE,GAAED,GAAE,QAAQ,IAAI,SAASA,IAAEE,IAAEC,IAAEC,IAAE;AAAC,YAAAH,GAAE,KAAKE,KAAEC,GAAE,QAAQ,IAAG,IAAI,IAAEF,MAAGF,EAAC;AAAA,UAAC,CAAE,GAAEC;AAAA,QAAC,CAAE;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,mBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAOI,KAAE,MAAMD,EAAC,GAAE,EAAED,KAAEC;AAAG,YAAAC,GAAEF,EAAC,IAAED,GAAED,GAAEE,EAAC,GAAEA,IAAEF,EAAC;AAAE,iBAAOI;AAAA,QAAC;AAAE,YAAI,KAAG,KAAG,GAAG,YAAU,QAAO,KAAG,KAAG,GAAG,WAAS;AAAO,cAAM,KAAG,SAASJ,GAAEC,IAAE;AAAC,cAAG,YAAU,OAAOA;AAAE,mBAAOA;AAAE,cAAG,EAAEA,EAAC;AAAE,mBAAO,GAAGA,IAAED,EAAC,IAAE;AAAG,cAAG,GAAGC,EAAC;AAAE,mBAAO,KAAG,GAAG,KAAKA,EAAC,IAAE;AAAG,cAAIC,KAAED,KAAE;AAAG,iBAAM,OAAKC,MAAG,IAAED,MAAG,YAAU,OAAKC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASF,IAAE;AAAC,iBAAO,QAAMA,KAAE,KAAG,GAAGA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,iBAAO,EAAED,EAAC,IAAEA,KAAE,GAAGA,IAAEC,EAAC,IAAE,CAACD,EAAC,IAAE,GAAG,GAAGA,EAAC,CAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,cAAG,YAAU,OAAOA,MAAG,GAAGA,EAAC;AAAE,mBAAOA;AAAE,cAAIC,KAAED,KAAE;AAAG,iBAAM,OAAKC,MAAG,IAAED,MAAG,YAAU,OAAKC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,mBAAQC,KAAE,GAAEC,MAAGF,KAAE,GAAGA,IAAED,EAAC,GAAG,QAAO,QAAMA,MAAGE,KAAEC;AAAG,YAAAH,KAAEA,GAAE,GAAGC,GAAEC,IAAG,CAAC,CAAC;AAAE,iBAAOA,MAAGA,MAAGC,KAAEH,KAAE;AAAA,QAAM;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAE,QAAMH,KAAE,SAAO,GAAGA,IAAEC,EAAC;AAAE,iBAAO,WAASE,KAAED,KAAEC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASH,IAAEC,IAAE;AAAC,iBAAO,QAAMD,MAAGC,MAAK,OAAOD,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,mBAAQC,KAAE,IAAGC,MAAGH,KAAE,GAAGA,IAAED,EAAC,GAAG,QAAOK,KAAE,OAAG,EAAEF,KAAEC,MAAG;AAAC,gBAAIE,KAAE,GAAGL,GAAEE,EAAC,CAAC;AAAE,gBAAG,EAAEE,KAAE,QAAML,MAAGE,GAAEF,IAAEM,EAAC;AAAG;AAAM,YAAAN,KAAEA,GAAEM,EAAC;AAAA,UAAC;AAAC,iBAAOD,MAAG,EAAEF,MAAGC,KAAEC,KAAE,CAAC,EAAED,KAAE,QAAMJ,KAAE,IAAEA,GAAE,WAAS,GAAGI,EAAC,KAAG,GAAGE,IAAEF,EAAC,MAAI,EAAEJ,EAAC,KAAG,GAAGA,EAAC;AAAA,QAAE;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,iBAAO,QAAMD,MAAG,GAAGA,IAAEC,IAAE,EAAE;AAAA,QAAC;AAAE,cAAM,KAAG,SAASD,IAAEC,IAAE;AAAC,iBAAO,GAAGD,EAAC,KAAG,GAAGC,EAAC,IAAE,GAAG,GAAGD,EAAC,GAAEC,EAAC,IAAE,SAASC,IAAE;AAAC,gBAAIC,KAAE,GAAGD,IAAEF,EAAC;AAAE,mBAAO,WAASG,MAAGA,OAAIF,KAAE,GAAGC,IAAEF,EAAC,IAAE,GAAGC,IAAEE,IAAE,CAAC;AAAA,UAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASH,IAAE;AAAC,iBAAOA;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,SAASC,IAAE;AAAC,mBAAO,QAAMA,KAAE,SAAOA,GAAED,EAAC;AAAA,UAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,SAASC,IAAE;AAAC,mBAAO,GAAGA,IAAED,EAAC;AAAA,UAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAO,GAAGA,EAAC,IAAE,GAAG,GAAGA,EAAC,CAAC,IAAE,GAAGA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAE;AAAC,iBAAM,cAAY,OAAOA,KAAEA,KAAE,QAAMA,KAAE,KAAG,YAAU,OAAOA,KAAE,EAAEA,EAAC,IAAE,GAAGA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,IAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC;AAAA,QAAC;AAAE,cAAM,KAAG,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,iBAAOD,KAAE,GAAGA,IAAE,CAAC,GAAE,GAAGD,IAAG,SAASA,IAAEG,IAAEC,IAAE;AAAC,eAAGF,IAAED,GAAED,IAAEG,IAAEC,EAAC,GAAEJ,EAAC;AAAA,UAAC,CAAE,GAAEE;AAAA,QAAC;AAAE,iBAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAA,QAAC,MAAM,GAAE;AAAA,UAAC,YAAYA,IAAEC,IAAE;AAAC,eAAG,MAAK,OAAM,MAAM,GAAE,GAAG,MAAK,gBAAe,MAAM,GAAE,KAAK,MAAID,IAAE,KAAK,eAAaC,IAAED,GAAE,iBAAiB,2BAA0BA,GAAE,SAAS,IAAGC,GAAE,MAAKA,GAAE,OAAO;AAAE,mBAAO,QAAQ,EAAC,OAAM,EAAC,GAAE,WAAU,MAAK,CAAC,SAAQ,KAAI,IAAE,GAAE,OAAM,MAAG,iBAAgB,CAAAD,QAAI,EAAE,QAAMA,KAAE,SAAOA,GAAE,MAAM,MAAIA,GAAE,SAAOA,GAAE,OAAO,IAAK,CAAAA,OAAGA,MAAG,GAAGA,IAAG,CAACA,IAAEC,OAAI,SAASA,EAAC,EAAG,CAAE,IAAGD,IAAE,GAAE,qBAAoB,EAAC,GAAE,gBAAe,MAAK,CAAC,SAAQ,QAAQ,EAAC,GAAE,gBAAe,EAAC,GAAE,mBAAkB,MAAK,CAAC,SAAQ,MAAM,EAAC,GAAE,gBAAe,EAAC,GAAE,iBAAgB,MAAK,CAAC,OAAO,EAAC,GAAE,UAAS,EAAC,GAAE,WAAU,MAAK,CAAC,OAAO,EAAC,EAAC,CAAC,EAAE,QAAS,CAAC,CAACE,IAAEC,EAAC,MAAI;AAAC,oBAAMC,MAAG,CAAAJ,OAAG,kBAAkBA,EAAC,IAAIC,GAAE,IAAI,IAAIC,EAAC;AAAE,cAAAF,GAAE,OAAO,GAAGI,IAAG,OAAMF,OAAG;AAAC,oBAAG,GAAG,QAAMD,KAAE,SAAOA,GAAEE,GAAE,CAAC,CAAC,GAAE;AAAC,sBAAIE,KAAE;AAAK,sBAAG;AAAC,oBAAAA,KAAE,MAAMJ,GAAEE,GAAE,CAAC,EAAE,MAAMF,KAAGE,GAAE,QAAM,CAAC,GAAG,IAAK,CAAAH,OAAG;AAAC,0BAAGE,IAAE;AAAC,4BAAG,SAAKF;AAAE,iCAAOE;AAAE,4BAAGA,GAAE,eAAeF,EAAC,GAAE;AAAC,gCAAMC,KAAEC,GAAEF,EAAC;AAAE,iCAAO,OAAOE,GAAEF,EAAC,GAAEC;AAAA,wBAAC;AAAA,sBAAC;AAAA,oBAAC,CAAE,CAAC,GAAEE,GAAE,oBAAkBE,KAAEF,GAAE,gBAAgBE,EAAC;AAAA,kBAAE,SAAOL,IAAE;AAAC,4BAAQ,MAAM,oBAAmBA,EAAC,GAAEK,KAAEL;AAAA,kBAAC,UAAC;AAAQ,oBAAAG,GAAE,SAAOH,GAAE,OAAO,KAAK,GAAGI,EAAC,UAASC,EAAC;AAAA,kBAAC;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,GAAGL,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAA,QAAC;AAAC,cAAM,KAAG,OAAO,IAAI,gBAAgB,GAAE,KAAG,EAAE,EAAE,eAAe,GAAE,KAAG,IAAI,EAAE,IAAG,EAAC,SAAQ,KAAE,CAAC;AAAE,iBAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,cAAIC;AAAE,gBAAK,EAAC,KAAIC,IAAE,OAAMC,IAAE,MAAKC,IAAE,SAAQC,IAAE,YAAWC,IAAE,QAAOC,GAAC,IAAER;AAAE,cAAG,cAAY,OAAOC;AAAE,mBAAO,KAAK,OAAO,MAAM,GAAGE,MAAGC,EAAC,sCAAsC,GAAE;AAAG,gBAAMK,KAAE,SAASV,IAAE;AAAC,gBAAG,YAAU,OAAOA;AAAE,qBAAOA,GAAE,KAAK,EAAE,QAAQ,OAAM,GAAG,EAAE,YAAY;AAAA,UAAC,EAAEI,EAAC;AAAE,cAAG,CAACM;AAAE,mBAAO,KAAK,OAAO,MAAM,GAAGL,EAAC,4BAA4B,GAAE;AAAG,gBAAMM,KAAE,oBAAoBD,EAAC,GAAG,EAAE,EAAE;AAAG,eAAK,OAAO,OAAKC,EAAC,EAAET,EAAC,GAAE,UAAQC,KAAE,KAAK,WAAS,WAASA,MAAGA,GAAE,KAAK,YAAW,EAAC,QAAO,kCAAiC,MAAK,CAAC,KAAK,SAAS,IAAG,CAAC,EAAC,KAAIO,IAAE,OAAML,IAAE,MAAKL,IAAE,MAAKM,IAAE,YAAWE,IAAE,QAAOC,GAAC,GAAE,CAAC,eAAcE,EAAC,CAAC,GAAEJ,EAAC,EAAC,CAAC;AAAA,QAAC;AAAC,iBAAS,GAAGP,IAAE;AAAC,iBAAM,EAAE,YAAU,QAAOC,KAAED,OAAI,OAAKC,GAAE,UAAQ,CAAC,0FAA0F,KAAKA,EAAC,OAAK,GAAG,MAAM,IAAID,EAAC,8BAA8B,GAAE;AAAI,cAAIC;AAAA,QAAC;AAAC,YAAI,KAAG,MAAK,KAAG,oBAAI;AAAI,cAAM,KAAG,EAAC,MAAM,QAAQD,IAAE;AAAC,iBAAO,OAAK,KAAG,MAAM,KAAK,sBAAsB,cAAc,IAAG,YAAU,OAAOA,KAAE,GAAGA,EAAC,IAAE;AAAA,QAAE,GAAE,iBAAgB,IAAG,sBAAsBA,IAAE;AAAC,cAAG,GAAG,IAAIA,GAAE,IAAI;AAAE,kBAAM,IAAI,MAAM,mBAAmBA,GAAE,IAAI,kBAAkB;AAAE,aAAG,IAAIA,GAAE,MAAK,IAAI,GAAG,MAAKA,EAAC,CAAC;AAAA,QAAC,GAAE,uBAAuBA,IAAEC,IAAE;AAAC,gBAAK,EAAC,KAAIC,IAAE,OAAMC,IAAE,YAAWC,GAAC,IAAEJ;AAAE,iBAAO,GAAG,KAAK,MAAK,aAAY,EAAC,KAAIE,IAAE,OAAMC,IAAE,SAAQ,MAAG,YAAWC,GAAC,GAAEH,EAAC;AAAA,QAAC,GAAE,wBAAwBD,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,sBAAU,OAAOF,OAAIA,KAAE,EAAC,MAAK,UAAS,SAAQA,GAAC;AAAG,gBAAK,EAAC,SAAQG,GAAC,IAAEH,IAAEI,KAAE,cAAaC,KAAEH,GAAE,OAAKE,KAAE,EAAE,QAAMD,KAAE,SAAOA,GAAE,SAAS,CAAC;AAAE,iBAAO,GAAG,KAAK,MAAKC,IAAE,EAAC,GAAGF,IAAE,KAAIG,IAAE,SAAQ,OAAG,YAAWL,GAAC,GAAEC,EAAC;AAAA,QAAC,GAAE,eAAeD,IAAEC,IAAE;AAAC,cAAIC;AAAE,gBAAMC,KAAE,KAAK,SAAS;AAAG,oBAAQD,KAAE,KAAK,WAAS,WAASA,MAAGA,GAAE,KAAK,YAAW,EAAC,QAAO,2BAA0B,MAAK,CAACC,IAAEH,IAAEC,EAAC,EAAC,CAAC;AAAA,QAAC,GAAE,qBAAqBD,IAAEC,IAAE;AAAC,cAAG,cAAY,OAAOA;AAAE,mBAAM;AAAG,gBAAMC,KAAEF,KAAE,MAAI,KAAK,SAAS,IAAGG,KAAEH;AAAE,aAAG,KAAK,MAAK,kBAAiB,EAAC,KAAIE,IAAE,OAAMC,GAAC,GAAEF,EAAC;AAAA,QAAC,GAAE,uBAAuBD,IAAEC,IAAE;AAAC,cAAG,CAAC,GAAGD,EAAC;AAAE;AAAO,gBAAME,KAAE,KAAK,SAAS,IAAGC,KAAE,eAAe,EAAE,QAAQH,EAAC,EAAE,CAAC;AAAG,iBAAO,KAAK,OAAO,GAAGG,IAAEF,EAAC,GAAE,KAAK,IAAI,mBAAmBC,IAAEC,EAAC,GAAE,MAAI;AAAC,iBAAK,OAAO,IAAIA,IAAEF,EAAC,GAAE,KAAK,IAAI,qBAAqBC,IAAEC,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,qBAAqBH,OAAKC,IAAE;AAAC,cAAIC;AAAE,cAAG,EAAEF,KAAE,UAAQE,KAAEF,OAAI,WAASE,KAAE,SAAOA,GAAE,KAAK;AAAG;AAAO,cAAG,CAACC,IAAEC,EAAC,IAAEJ,GAAE,MAAM,GAAG;AAAE,cAAG,CAAC,CAAC,UAAS,UAAU,EAAE,SAAS,QAAMI,KAAE,SAAOA,GAAE,YAAY,CAAC;AAAE,kBAAM,IAAI,MAAM,uDAAuD;AAAE,gBAAMC,KAAEL,GAAE,QAAQ,GAAGG,EAAC,IAAIC,EAAC,KAAI,EAAE;AAAE,cAAG,CAACD,MAAG,CAACC,MAAG,CAACC;AAAE,kBAAM,IAAI,MAAM,oBAAoBL,EAAC,6BAA6B;AAAE,iBAAO,KAAK,sBAAsB,8BAA6BG,IAAEC,GAAE,YAAY,GAAEC,IAAEJ,EAAC;AAAA,QAAC,GAAE,cAAcD,IAAE;AAAC,gBAAMC,KAAE,IAAID,OAAI,KAAK,SAAS,iBAAgB,GAAGA,EAAC;AAAE,uBAAWA,KAAE,KAAK,SAAS,cAAc,EAAE,KAAM,CAAAA,OAAG;AAAC,YAAAA,KAAEC,GAAE,IAAEA,GAAE,IAAE;AAAA,UAAC,CAAE,IAAED,KAAEC,GAAE,IAAE,IAAEA,GAAE;AAAA,QAAC,EAAC;AAAE,YAAI,KAAG;AAAE,cAAM,KAAG,EAAC,eAAc;AAAC,iBAAO,KAAK,sBAAsB,gBAAgB;AAAA,QAAC,GAAE,qBAAqBD,IAAEC,IAAE;AAAC,cAAIC;AAAE,aAAG,4BAA2B,KAAK,SAAS,IAAGF,IAAEC,EAAC,GAAE,cAAY,OAAOA,OAAIA,KAAE,CAAC,CAAC,8BAA6B,KAAE,GAAE,CAAC,6BAA6B,GAAE,CAAC,eAAcA,EAAC,CAAC,IAAGA,KAAEA,GAAE,IAAK,CAAAD,OAAG;AAAC,kBAAK,CAACC,IAAE,GAAGC,EAAC,IAAEF;AAAE,gBAAG,kBAAgBC,IAAE;AAAC,kBAAIE,KAAED,GAAE,CAAC,GAAEE,KAAE,MAAI;AAAC,oBAAIJ;AAAE,0BAAQA,KAAE,KAAK,WAAS,WAASA,MAAGA,GAAE,cAAcG,EAAC;AAAA,cAAC;AAAE,4BAAY,OAAOA,OAAIC,KAAED;AAAG,oBAAME,KAAE,mBAAmBJ,EAAC,GAAG,EAAE,EAAE;AAAG,cAAAD,GAAE,CAAC,IAAEK,IAAE,KAAK,OAAO,OAAKA,EAAC,EAAED,EAAC;AAAA,YAAC;AAAC,mBAAOJ;AAAA,UAAC,CAAE,GAAE,UAAQE,KAAE,KAAK,WAAS,WAASA,MAAGA,GAAE,KAAK,YAAW,EAAC,QAAO,iCAAgC,MAAK,CAAC,KAAK,SAAS,IAAG,CAACF,IAAEC,EAAC,CAAC,EAAC,CAAC;AAAA,QAAC,GAAE,6BAA6BD,IAAEC,IAAE;AAAC,cAAG,cAAY,OAAOA;AAAE,mBAAM;AAAG,gBAAMC,KAAEF,KAAE,MAAI,KAAK,SAAS;AAAG,aAAG,KAAK,MAAK,2BAA0B,EAAC,KAAIE,IAAE,OAAMF,GAAC,GAAEC,EAAC;AAAA,QAAC,GAAE,iCAAiCD,IAAEC,IAAEC,IAAE;AAAC,cAAG,cAAY,OAAOD;AAAE,mBAAM;AAAG,gBAAME,KAAEH,KAAE,MAAI,KAAK,SAAS;AAAG,aAAG,KAAK,MAAK,+BAA8B,EAAC,KAAIG,IAAE,OAAMH,IAAE,QAAOE,GAAC,GAAED,EAAC;AAAA,QAAC,GAAE,oBAAoBD,IAAEC,IAAEC,IAAE;AAAC,gBAAMC,KAAE,mBAAiBF;AAAE,kBAAMC,MAAGA,GAAE,eAAa,KAAK,IAAI,aAAa,QAAO,EAAC,MAAKF,GAAC,GAAE,EAAC,QAAOG,GAAC,CAAC,IAAE,KAAK,IAAI,UAAU,QAAO,EAAC,MAAKH,GAAC,GAAE,EAAC,QAAOG,GAAC,CAAC;AAAA,QAAC,EAAC,GAAE,KAAG,EAAC,eAAeH,IAAEC,IAAE;AAAC,cAAG,CAAC,GAAGD,EAAC;AAAE;AAAO,gBAAME,KAAE,KAAK,SAAS,IAAGC,KAAE,WAAW,EAAE,SAASH,EAAC,EAAE,CAAC,IAAGI,KAAE,CAAC,EAAC,OAAMF,IAAE,QAAOC,IAAE,QAAOC,GAAC,MAAI;AAAC,YAAAF,GAAE,SAAOF,MAAGC,GAAEC,IAAEC,IAAEC,EAAC;AAAA,UAAC;AAAE,iBAAO,KAAK,OAAO,GAAGD,IAAEC,EAAC,GAAE,KAAK,IAAI,mBAAmBF,IAAEC,EAAC,GAAE,MAAI;AAAC,iBAAK,OAAO,IAAIA,IAAEC,EAAC,GAAE,KAAK,IAAI,qBAAqBF,IAAEC,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,gBAAgBH,OAAKC,IAAE;AAAC,cAAGA,GAAE,IAAI,GAAE,QAAMA,MAAGA,GAAE,KAAM,CAAAD,OAAG,cAAY,OAAOA,EAAE,GAAE;AAAC,mBAAO,KAAK,YAAY,gBAAgB,EAAE,OAAO,IAAI,iBAAiBA,IAAE,GAAGC,EAAC;AAAA,UAAC;AAAC,iBAAO,KAAK,sBAAsB,oBAAmBD,IAAE,GAAGC,EAAC;AAAA,QAAC,EAAC,GAAE,KAAG,CAAC,GAAE,KAAG,CAAC,GAAE,KAAG,EAAC,qBAAoB;AAAC,iBAAO,IAAI,EAAE,MAAK,EAAC,QAAO,KAAE,CAAC;AAAA,QAAC,EAAC;AAAA,QAAE,MAAM,WAAW,EAAE,EAAE;AAAA,UAAC,YAAYD,IAAEC,IAAE;AAAC,kBAAM,GAAE,GAAG,MAAK,aAAY,MAAM,GAAE,GAAG,MAAK,WAAU,MAAM,GAAE,GAAG,MAAK,YAAW,QAAQ,GAAE,GAAG,MAAK,aAAY,EAAE,GAAE,GAAG,MAAK,mBAAkB,MAAM,GAAE,GAAG,MAAK,cAAa,KAAE,GAAE,GAAG,MAAK,OAAM,oBAAI,KAAG,GAAE,GAAG,MAAK,iBAAgB,MAAM,GAAE,GAAG,MAAK,aAAY,MAAM,GAAE,GAAG,MAAK,iBAAgB,MAAM,GAAE,GAAG,MAAK,yBAAwB,MAAM,GAAE,KAAK,YAAUD,IAAE,KAAK,UAAQC,IAAEA,GAAE,GAAG,kBAAkB,CAAAD,OAAG;AAAC,sBAAMA,MAAGA,GAAE,UAAQ,KAAK,aAAa;AAAA,YAAC,CAAE,GAAEC,GAAE,GAAG,oBAAoB,CAAAD,OAAG;AAAC,oBAAMC,KAAE,OAAO,OAAO,CAAC,GAAE,KAAK,QAAQ,GAAEC,KAAE,OAAO,OAAO,KAAK,UAAU,UAASF,EAAC;AAAE,mBAAK,KAAK,oBAAmB,EAAC,GAAGE,GAAC,GAAED,EAAC;AAAA,YAAC,CAAE,GAAEA,GAAE,GAAG,gBAAgB,OAAMD,OAAG;AAAC,oBAAK,EAAC,OAAMC,IAAE,GAAGC,GAAC,IAAEF,IAAEG,KAAE,KAAK;AAAsB,kBAAG;AAAC,gBAAAA,MAAG,MAAMA,GAAED,EAAC,GAAE,QAAMD,MAAGA,GAAE,QAAQ,IAAI;AAAA,cAAC,SAAOD,IAAE;AAAC,qBAAK,OAAO,MAAM,mBAAkBA,EAAC,GAAE,QAAMC,MAAGA,GAAE,OAAOD,EAAC;AAAA,cAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,MAAM,MAAMA,IAAEC,IAAE;AAAC,gBAAIC,IAAEC;AAAE,gBAAG,CAAC,KAAK;AAAW,kBAAG;AAAC,oBAAIE;AAAE,8BAAY,OAAOL,OAAIC,KAAED,IAAEA,KAAE,CAAC;AAAG,oBAAIM,KAAE,MAAM,KAAK,QAAQ,gBAAgBN,EAAC;AAAE,qBAAK,aAAW,MAAGE,KAAE,KAAK,WAAUC,KAAEG,IAAEA,KAAE,EAAE,EAAEJ,IAAEC,IAAE,EAAC,YAAW,CAACH,IAAEC,OAAIA,GAAC,CAAC,GAAE,KAAK,YAAUK,IAAE,UAAQD,KAAEC,OAAI,WAASD,MAAGA,GAAE,OAAK,KAAK,YAAU,KAAK,QAAQ,WAAS,IAAIC,GAAE,EAAE,KAAKA,GAAE,IAAI,KAAI,KAAK,OAAO,OAAO,KAAK,SAAS,IAAG,KAAK,oBAAkBA,GAAE,WAAS,SAASN,IAAEC,IAAE;AAAC,wBAAMC,MAAGD,MAAG,CAAC,GAAG,OAAQ,CAACD,IAAEC,QAAK,aAAYA,OAAID,GAAEC,GAAE,GAAG,IAAEA,GAAE,UAASD,KAAI,CAAC,CAAC;AAAE,yBAAO,OAAO,OAAOE,IAAEF,EAAC;AAAA,gBAAC,EAAEM,GAAE,UAAS,KAAK,eAAe,GAAE,MAAM,KAAK,kBAAkB,KAAK,eAAe;AAAG,oBAAG;AAAC,wBAAM,KAAK,sBAAsB,kBAAiB,EAAC,SAAQ,KAAK,SAAQ,CAAC;AAAA,gBAAC,SAAON,IAAE;AAAC,0BAAQ,KAAKA,EAAC;AAAA,gBAAC;AAAC,gBAAAC,MAAGA,GAAE,KAAK,MAAKK,EAAC;AAAA,cAAC,SAAON,IAAE;AAAC,wBAAQ,MAAM,GAAG,KAAK,SAAS,kBAAiBA,EAAC;AAAA,cAAC;AAAA,UAAC;AAAA,UAAC,kBAAiB;AAAC,gBAAG,CAAC,KAAK;AAAW,oBAAM,IAAI,MAAM,eAAe;AAAA,UAAC;AAAA,UAAC,aAAaA,IAAE;AAAC,0BAAY,OAAOA,OAAI,KAAK,wBAAsBA;AAAA,UAAE;AAAA,UAAC,aAAaA,IAAE;AAAC,mBAAO,KAAK,OAAO,iBAAiBA,EAAC,GAAE;AAAA,UAAI;AAAA,UAAC,aAAaA,IAAE;AAAC,mBAAO,KAAK,OAAO,KAAK,kBAAiBA,EAAC,GAAE;AAAA,UAAI;AAAA,UAAC,aAAaA,IAAE;AAAC,mBAAO,KAAK,OAAO,KAAK,kBAAiBA,EAAC,GAAE;AAAA,UAAI;AAAA,UAAC,UAAUA,IAAE;AAAC,mBAAO,KAAK,OAAO,KAAK,eAAcA,EAAC,GAAE;AAAA,UAAI;AAAA,UAAC,kBAAkBA,IAAE;AAAC,mBAAO,KAAK,aAAW,KAAK,OAAO,KAAK,mBAAkB,EAAC,QAAOA,IAAE,QAAO,KAAE,CAAC,GAAE,KAAK,kBAAgBA,IAAE;AAAA,UAAI;AAAA,UAAC,eAAeA,IAAE;AAAC,iBAAK,OAAO,KAAK,mBAAkBA,EAAC;AAAA,UAAC;AAAA,UAAC,kBAAkBA,IAAE;AAAC,kBAAMC,KAAE;AAAmB,mBAAO,KAAK,GAAGA,IAAED,EAAC,GAAE,MAAI,KAAK,IAAIC,IAAED,EAAC;AAAA,UAAC;AAAA,UAAC,iBAAgB;AAAC,iBAAK,OAAO,KAAK,4BAA2B,EAAC,SAAQ,KAAE,CAAC;AAAA,UAAC;AAAA,UAAC,iBAAgB;AAAC,iBAAK,OAAO,KAAK,4BAA2B,EAAC,SAAQ,MAAE,CAAC;AAAA,UAAC;AAAA,UAAC,eAAeA,IAAE;AAAC,iBAAK,OAAO,KAAK,iBAAgBA,EAAC;AAAA,UAAC;AAAA,UAAC,qBAAqBA,IAAE;AAAC,iBAAK,OAAO,KAAK,iBAAgBA,EAAC;AAAA,UAAC;AAAA,UAAC,WAAWA,IAAE;AAAC,kBAAMC,KAAE,EAAC,KAAI,GAAE,SAAQ,OAAG,QAAO,QAAMD,KAAE,SAAOA,GAAE,qBAAoB;AAAE,iBAAK,OAAO,KAAK,mBAAkBC,EAAC,GAAE,KAAK,KAAK,sBAAqBA,EAAC,GAAE,KAAK,IAAI,IAAIA,GAAE,KAAIA,EAAC;AAAA,UAAC;AAAA,UAAC,WAAWD,IAAE;AAAC,kBAAMC,KAAE,EAAC,KAAI,GAAE,SAAQ,MAAG,WAAU,QAAMD,KAAE,SAAOA,GAAE,UAAS;AAAE,iBAAK,OAAO,KAAK,mBAAkBC,EAAC,GAAE,KAAK,KAAK,sBAAqBA,EAAC,GAAE,KAAK,IAAI,IAAIA,GAAE,KAAIA,EAAC;AAAA,UAAC;AAAA,UAAC,eAAc;AAAC,kBAAMD,KAAE,GAAEC,KAAE,KAAK,IAAI,IAAID,EAAC;AAAE,YAAAC,MAAGA,GAAE,UAAQ,KAAK,WAAW,IAAE,KAAK,WAAW;AAAA,UAAC;AAAA,UAAC,IAAI,UAAS;AAAC,mBAAO,KAAK;AAAA,UAAQ;AAAA,UAAC,IAAI,kBAAiB;AAAC,kBAAMD,KAAE,KAAK,IAAI,IAAI,CAAC;AAAE,mBAAO,QAAQA,MAAGA,GAAE,OAAO;AAAA,UAAC;AAAA,UAAC,IAAI,YAAW;AAAC,mBAAO,KAAK;AAAA,UAAU;AAAA,UAAC,IAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAS;AAAA,UAAC,IAAI,SAAQ;AAAC,oBAAOA,KAAE,WAAS,UAAQC,KAAED,GAAE,aAAW,WAASC,KAAE,SAAOA,GAAE,WAAS,EAAE,UAAQC,KAAEF,GAAE,aAAW,WAASE,MAAGA,GAAE;AAAM,gBAAIF,IAAEC,IAAEC;AAAA,UAAC;AAAA,UAAC,IAAI,SAAQ;AAAC,mBAAO;AAAA,UAAE;AAAA,UAAC,IAAI,WAAU;AAAC,gBAAIF;AAAE,mBAAO,UAAQA,KAAE,KAAK,aAAW,WAASA,KAAE,SAAOA,GAAE;AAAA,UAAQ;AAAA,UAAC,IAAI,SAAQ;AAAC,mBAAO,KAAK;AAAA,UAAO;AAAA,UAAC,uBAAuBA,IAAE;AAAC,gBAAG,KAAK,gBAAgB,GAAEA;AAAE,qBAAOA,KAAEA,GAAE,QAAQ,YAAW,EAAE,GAAE,EAAE,KAAK,UAAU,KAAIA,EAAC;AAAA,UAAC;AAAA,UAAC,eAAeA,IAAEC,IAAE;AAAC,kBAAMC,KAAE,MAAKC,KAAE,KAAK;AAAO,mBAAO,IAAI,MAAMH,IAAE,EAAC,IAAIA,IAAEI,IAAEC,IAAE;AAAC,oBAAMC,KAAEN,GAAEI,EAAC;AAAE,qBAAO,YAAYJ,IAAE;AAAC,oBAAGM,IAAE;AAAC,wBAAMH,KAAEG,GAAE,MAAMJ,IAAEF,GAAE,OAAOC,EAAC,CAAC;AAAE,sBAAGE,OAAI;AAAG,2BAAOA;AAAA,gBAAC;AAAC,oBAAGF,IAAE;AAAC,wBAAMI,KAAED,GAAE,SAAS,EAAE,MAAM,iBAAiB;AAAE,sBAAG,QAAMC,IAAE;AAAC,0BAAMD,KAAEC,GAAE,CAAC,EAAE,YAAY,GAAEC,KAAED,GAAE,OAAME,KAAE,UAAQH,IAAEI,KAAEN,GAAE,SAAS;AAAG,wBAAIO,KAAEH,GAAE,MAAMF,GAAE,MAAM,GAAEM,KAAEV,GAAE,CAAC,GAAEW,KAAEX,GAAE,CAAC;AAAE,gCAAU,OAAOU,MAAG,cAAY,OAAOC,OAAID,KAAEA,GAAE,QAAQ,YAAW,GAAG,GAAED,KAAE,GAAGA,EAAC,GAAGC,EAAC,IAAGA,KAAEC,IAAEA,KAAEX,GAAE,CAAC,IAAGS,KAAE,QAAQR,EAAC,IAAI,EAAEQ,EAAC,CAAC,IAAGN,GAAEC,EAAC,EAAEK,IAAEC,EAAC;AAAE,0BAAME,KAAE,MAAI;AAAC,sBAAAT,GAAE,IAAIM,IAAEC,EAAC,GAAEP,GAAE,cAAcM,EAAC,KAAGP,GAAE,IAAI,qBAAqBM,IAAEC,EAAC;AAAA,oBAAC;AAAE,2BAAOF,KAAE,KAAKK,GAAE,KAAGV,GAAE,IAAI,mBAAmBM,IAAEC,IAAEE,EAAC,GAAEC;AAAA,kBAAE;AAAA,gBAAC;AAAC,oBAAIP,KAAED;AAAE,uBAAM,CAAC,OAAM,MAAK,QAAQ,EAAE,SAASH,EAAC,MAAII,KAAEJ,KAAE,MAAII,KAAGF,GAAE,UAAU,YAAW,EAAC,KAAIF,IAAE,QAAOI,IAAE,MAAKL,GAAC,CAAC;AAAA,cAAC;AAAA,YAAC,EAAC,CAAC;AAAA,UAAC;AAAA,UAAC,sBAAsBA,OAAKC,IAAE;AAAC,mBAAO,KAAK,QAAQ,UAAU,YAAW,EAAC,QAAOD,IAAE,MAAKC,GAAC,CAAC;AAAA,UAAC;AAAA,UAAC,iBAAiBD,OAAKC,IAAE;AAAC,iBAAK,QAAQ,KAAK,YAAW,EAAC,QAAOD,IAAE,MAAKC,GAAC,CAAC;AAAA,UAAC;AAAA,UAAC,YAAYD,IAAE;AAAC,mBAAO,KAAK,sBAAsB,gBAAe,GAAGA,EAAC;AAAA,UAAC;AAAA,UAAC,IAAI,MAAK;AAAC,mBAAO,KAAK,eAAe,IAAG,KAAK;AAAA,UAAC;AAAA,UAAC,IAAI,SAAQ;AAAC,mBAAO,KAAK,eAAe,IAAG,QAAQ;AAAA,UAAC;AAAA,UAAC,IAAI,KAAI;AAAC,mBAAO,KAAK,eAAe,IAAG,IAAI;AAAA,UAAC;AAAA,UAAC,IAAI,MAAK;AAAC,mBAAO,KAAK,eAAe,IAAG,KAAK;AAAA,UAAC;AAAA,UAAC,IAAI,KAAI;AAAC,mBAAO,KAAK,eAAe,IAAG,IAAI;AAAA,UAAC;AAAA,UAAC,IAAI,SAAQ;AAAC,mBAAO,KAAK,eAAe,IAAG,QAAQ;AAAA,UAAC;AAAA,UAAC,IAAI,cAAa;AAAC,gBAAIA,KAAE,KAAK;AAAc,mBAAOA,OAAIA,KAAE,KAAK,gBAAc,IAAI,EAAE,IAAI,IAAGA;AAAA,UAAC;AAAA,UAAC,IAAI,UAAS;AAAC,gBAAIA,KAAE,KAAK;AAAU,mBAAOA,OAAIA,KAAE,KAAK,YAAU,IAAI,EAAE,IAAI,IAAGA;AAAA,UAAC;AAAA,UAAC,IAAI,cAAa;AAAC,gBAAIA,KAAE,KAAK;AAAc,mBAAOA,OAAIA,KAAE,KAAK,gBAAc,IAAI,EAAE,IAAI,IAAGA;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAO,IAAI,GAAGD,IAAEC,EAAC;AAAA,QAAC;AAAC,YAAG,QAAM,OAAO,eAAc;AAAC,gBAAMD,KAAE,IAAI,EAAE,IAAI;AAAE,iBAAO,SAAO,GAAG,CAAC,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC,GAAG,GAAE;AAAA,IAAC,GAAG,CAAE;AAAA;AAAA;", "names": ["e", "t", "n", "r", "o", "i", "s", "a", "c", "l", "u", "f", "p", "h", "d", "g", "y", "v", "b", "_", "I", "M", "m", "S", "O"]}