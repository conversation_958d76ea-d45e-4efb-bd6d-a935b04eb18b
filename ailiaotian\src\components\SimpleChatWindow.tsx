import React from 'react';
import { getThemeManager } from '../themeManager';

interface SimpleChatWindowProps {
  onClose: () => void;
  onOpenSettings?: () => void;
}

/**
 * 简化版聊天窗口组件 - 用于测试
 */
export const SimpleChatWindow: React.FC<SimpleChatWindowProps> = ({ onClose, onOpenSettings }) => {
  console.log("SimpleChatWindow组件渲染");
  
  const themeManager = getThemeManager();

  return (
    <div
      className={`apple-modal rounded-xl shadow-lg w-full flex ${themeManager.getThemeClasses()}`}
      style={{
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        height: '70vh',
        maxHeight: '600px',
        minHeight: '400px',
        border: `1px solid ${themeManager.getThemeColor('border')}`,
        backgroundColor: themeManager.getThemeColor('background'),
        color: themeManager.getThemeColor('primary')
      }}
    >
      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 头部工具栏 */}
        <div 
          className="p-4 border-b flex items-center justify-between"
          style={{ borderColor: themeManager.getThemeColor('border') }}
        >
          <div className="flex items-center space-x-2">
            <h2 className="font-semibold">聊天窗口测试</h2>
          </div>
          
          <div className="flex items-center space-x-2">
            {onOpenSettings && (
              <button
                onClick={onOpenSettings}
                className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="设置"
              >
                ⚙️
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="关闭"
            >
              ✕
            </button>
          </div>
        </div>

        {/* 消息区域 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          <div className="flex justify-center">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">🎉 聊天窗口测试成功！</h3>
              <p className="text-sm opacity-70">
                这是一个简化版的聊天窗口，用于测试基本显示功能。
              </p>
              <p className="text-sm opacity-70 mt-2">
                如果您能看到这个窗口，说明聊天窗口功能已经正常工作。
              </p>
            </div>
          </div>
        </div>

        {/* 输入区域 */}
        <div 
          className="p-4 border-t"
          style={{ borderColor: themeManager.getThemeColor('border') }}
        >
          <div className="flex space-x-2">
            <input
              type="text"
              placeholder="这是测试输入框..."
              className="flex-1 p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
              style={{
                backgroundColor: themeManager.getThemeColor('surface'),
                borderColor: themeManager.getThemeColor('border'),
                color: themeManager.getThemeColor('primary')
              }}
              disabled
            />
            <button
              disabled
              className="px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed"
            >
              测试中
            </button>
          </div>
          <p className="text-xs mt-2 opacity-50">
            这是简化版测试窗口，输入功能暂时禁用
          </p>
        </div>
      </div>
    </div>
  );
};
