import React, { useState, useEffect, useRef } from 'react';
import { getSettings, PluginSettings } from '../settings';
import { sendChatRequest, ApiResponseHandlers } from '../api';
import { getThemeManager } from '../themeManager';
import { getChatHistoryManager, ChatSession, ChatMessage } from '../chatHistory';

interface ChatWindowProps {
  onClose: () => void;
  onOpenSettings?: () => void;
}

/**
 * 独立聊天窗口组件
 */
export const ChatWindow: React.FC<ChatWindowProps> = ({ onClose, onOpenSettings }) => {
  // 基础状态
  const [settings, setSettings] = useState<PluginSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  // 聊天相关状态
  const [currentInput, setCurrentInput] = useState('');
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [showSidebar, setShowSidebar] = useState(true);
  
  // AI响应状态
  const [streamingResponse, setStreamingResponse] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  
  // 引用
  const modalRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  
  // 管理器实例
  const themeManager = getThemeManager();
  const historyManager = getChatHistoryManager();

  // 初始化
  useEffect(() => {
    initializeComponent();
    loadSessions();
    
    // 聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  }, []);

  // 滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages, streamingResponse]);

  /**
   * 初始化组件
   */
  const initializeComponent = async () => {
    try {
      const pluginSettings = await getSettings();
      setSettings(pluginSettings);
    } catch (error) {
      console.error('初始化设置失败:', error);
      setError('初始化失败，请检查设置');
    }
  };

  /**
   * 加载聊天会话
   */
  const loadSessions = () => {
    const allSessions = historyManager.getSessions();
    setSessions(allSessions);
    
    // 如果没有当前会话，创建新会话
    if (!currentSession && allSessions.length === 0) {
      createNewSession();
    } else if (!currentSession && allSessions.length > 0) {
      setCurrentSession(allSessions[0]);
    }
  };

  /**
   * 创建新会话
   */
  const createNewSession = () => {
    const newSession = historyManager.createSession();
    setCurrentSession(newSession);
    setSessions([newSession, ...sessions]);
  };

  /**
   * 选择会话
   */
  const selectSession = (session: ChatSession) => {
    setCurrentSession(session);
    setStreamingResponse('');
    setError('');
  };

  /**
   * 删除会话
   */
  const deleteSession = (sessionId: string) => {
    historyManager.deleteSession(sessionId);
    const updatedSessions = sessions.filter(s => s.id !== sessionId);
    setSessions(updatedSessions);
    
    if (currentSession?.id === sessionId) {
      if (updatedSessions.length > 0) {
        setCurrentSession(updatedSessions[0]);
      } else {
        createNewSession();
      }
    }
  };

  /**
   * 发送消息
   */
  const sendMessage = async () => {
    if (!currentInput.trim() || !settings || isLoading) return;
    
    const message = currentInput.trim();
    setCurrentInput('');
    setError('');
    setIsLoading(true);
    setIsStreaming(true);
    setStreamingResponse('');

    try {
      // 确保有当前会话
      let session = currentSession;
      if (!session) {
        session = historyManager.createSession();
        setCurrentSession(session);
        setSessions(prev => [session!, ...prev]);
      }

      // 添加用户消息
      historyManager.addMessage(session.id, 'user', message);
      
      // 更新会话显示
      const updatedSession = historyManager.getSession(session.id);
      if (updatedSession) {
        setCurrentSession(updatedSession);
        updateSessionInList(updatedSession);
      }

      // 获取对话上下文
      const contextMessages = historyManager.getSessionContext(session.id, 10);
      
      // 构建消息历史
      const messages = contextMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // 定义响应处理器
      const responseHandlers: ApiResponseHandlers = {
        onChunk: (chunk: string) => {
          setStreamingResponse(prev => prev + chunk);
        },
        onComplete: (fullResponse: string) => {
          // 添加AI响应到历史
          historyManager.addMessage(session!.id, 'assistant', fullResponse);
          
          // 更新会话显示
          const finalSession = historyManager.getSession(session!.id);
          if (finalSession) {
            setCurrentSession(finalSession);
            updateSessionInList(finalSession);
          }
          
          setStreamingResponse('');
          setIsLoading(false);
          setIsStreaming(false);
        },
        onError: (errorMsg: string) => {
          setError(`发送失败: ${errorMsg}`);
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingResponse('');
        }
      };

      // 发送请求（不使用上下文，直接传递消息历史）
      await sendChatRequest(
        message,
        null, // 不使用Logseq上下文
        responseHandlers,
        undefined, // 使用默认系统提示
        messages // 传递消息历史
      );

    } catch (error) {
      console.error('发送消息失败:', error);
      setError('发送消息失败，请重试');
      setIsLoading(false);
      setIsStreaming(false);
      setStreamingResponse('');
    }
  };

  /**
   * 更新会话列表中的会话
   */
  const updateSessionInList = (updatedSession: ChatSession) => {
    setSessions(prev => 
      prev.map(s => s.id === updatedSession.id ? updatedSession : s)
    );
  };

  /**
   * 滚动到底部
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * 处理输入框键盘事件
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  /**
   * 格式化时间
   */
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  if (!settings) {
    return (
      <div className={`apple-modal rounded-xl shadow-lg w-full h-96 flex items-center justify-center ${themeManager.getThemeClasses()}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={modalRef}
      className={`apple-modal rounded-xl shadow-lg w-full flex ${themeManager.getThemeClasses()}`}
      style={{
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        height: '70vh',
        maxHeight: '600px',
        minHeight: '400px',
        border: `1px solid ${themeManager.getThemeColor('border')}`,
        backgroundColor: themeManager.getThemeColor('background'),
        color: themeManager.getThemeColor('primary')
      }}
    >
      {/* 侧边栏 */}
      {showSidebar && (
        <div 
          className="w-64 border-r flex flex-col"
          style={{ borderColor: themeManager.getThemeColor('border') }}
        >
          {/* 侧边栏头部 */}
          <div className="p-4 border-b" style={{ borderColor: themeManager.getThemeColor('border') }}>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">聊天历史</h3>
              <button
                onClick={createNewSession}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="新建对话"
              >
                ➕
              </button>
            </div>
          </div>
          
          {/* 会话列表 */}
          <div className="flex-1 overflow-y-auto">
            {sessions.map(session => (
              <div
                key={session.id}
                className={`p-3 border-b cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                  currentSession?.id === session.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
                style={{ borderColor: themeManager.getThemeColor('border') }}
                onClick={() => selectSession(session)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{session.title}</p>
                    <p className="text-xs text-gray-500 mt-1">{formatTime(session.updatedAt)}</p>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteSession(session.id);
                    }}
                    className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
                    title="删除对话"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 头部工具栏 */}
        <div 
          className="p-4 border-b flex items-center justify-between"
          style={{ borderColor: themeManager.getThemeColor('border') }}
        >
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title={showSidebar ? "隐藏侧边栏" : "显示侧边栏"}
            >
              {showSidebar ? '◀' : '▶'}
            </button>
            <h2 className="font-semibold">
              {currentSession?.title || '新对话'}
            </h2>
          </div>
          
          <div className="flex items-center space-x-2">
            {onOpenSettings && (
              <button
                onClick={onOpenSettings}
                className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="设置"
              >
                ⚙️
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="关闭"
            >
              ✕
            </button>
          </div>
        </div>

        {/* 消息区域 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {currentSession?.messages.map(message => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-700'
                }`}
              >
                <div className="whitespace-pre-wrap break-words">{message.content}</div>
                <div className={`text-xs mt-1 opacity-70 ${
                  message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {formatTime(message.timestamp)}
                </div>
              </div>
            </div>
          ))}
          
          {/* 流式响应显示 */}
          {isStreaming && streamingResponse && (
            <div className="flex justify-start">
              <div className="max-w-[80%] p-3 rounded-lg bg-gray-100 dark:bg-gray-700">
                <div className="whitespace-pre-wrap break-words">{streamingResponse}</div>
                <div className="text-xs mt-1 opacity-70 text-gray-500">正在输入...</div>
              </div>
            </div>
          )}
          
          {/* 加载指示器 */}
          {isLoading && !streamingResponse && (
            <div className="flex justify-start">
              <div className="max-w-[80%] p-3 rounded-lg bg-gray-100 dark:bg-gray-700">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                  <span>AI正在思考...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* 输入区域 */}
        <div 
          className="p-4 border-t"
          style={{ borderColor: themeManager.getThemeColor('border') }}
        >
          <div className="flex space-x-2">
            <textarea
              ref={inputRef}
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入消息... (Enter发送，Shift+Enter换行)"
              className="flex-1 p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
              style={{
                minHeight: '44px',
                maxHeight: '120px',
                backgroundColor: themeManager.getThemeColor('surface'),
                borderColor: themeManager.getThemeColor('border'),
                color: themeManager.getThemeColor('primary')
              }}
              rows={1}
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={!currentInput.trim() || isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? '发送中...' : '发送'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
