# 界面导航指南

## 🎯 问题解决

您提到的问题已经解决！现在可以在聊天界面和设置界面之间自由切换。

## 🚀 新的导航方式

### 1. 从聊天界面进入设置
- 在AI聊天对话框的标题栏中，现在有一个 **⚙️ 设置按钮**
- 点击该按钮即可从聊天界面直接进入设置页面
- 位置：在提示词选择框和关闭按钮之间

### 2. 从设置界面返回聊天
- 在设置页面的标题栏中，现在有一个 **💬 返回聊天按钮**
- 点击该按钮可以返回到之前的聊天界面
- 只有当存在聊天上下文时才会显示此按钮

## 📱 完整的使用流程

### 场景1：首次打开插件
1. 点击工具栏中的 🤖 图标
2. 默认打开设置界面
3. 配置API和快捷键等设置
4. 保存设置后关闭

### 场景2：使用快捷键打开聊天
1. 使用快捷键 `Ctrl+G` (或 `Cmd+G`) 打开AI聊天
2. 直接进入聊天界面，显示当前上下文
3. 如需修改设置，点击标题栏的 ⚙️ 按钮
4. 在设置页面修改配置后，点击 💬 按钮返回聊天

### 场景3：使用斜杠命令
1. 输入 `/AI聊天` 命令
2. 打开聊天界面
3. 可以随时通过 ⚙️ 按钮进入设置

## 🎨 界面元素说明

### 聊天界面标题栏
```
AI 聊天    [提示词选择] [⚙️设置] [✕关闭]
```

### 设置界面标题栏
```
AI 聊天设置                [💬返回聊天] [✕关闭]
```

## ⚡ 快捷操作

- **⚙️ 设置按钮**: 从聊天界面快速进入设置
- **💬 返回聊天按钮**: 从设置界面返回聊天（仅在有聊天上下文时显示）
- **✕ 关闭按钮**: 关闭整个插件界面
- **ESC 键**: 在任何界面都可以快速关闭插件

## 🔧 技术实现

### 智能导航逻辑
- 当有聊天上下文时，设置页面会显示返回聊天按钮
- 当没有聊天上下文时，从设置页面关闭会直接退出插件
- 界面状态在聊天和设置之间保持同步

### 用户体验优化
- 按钮有悬停效果和工具提示
- 图标直观易懂
- 导航逻辑符合用户直觉

## 🎉 现在您可以：

1. ✅ **随时进入设置**: 在聊天界面点击 ⚙️ 按钮
2. ✅ **随时返回聊天**: 在设置界面点击 💬 按钮
3. ✅ **自由切换**: 在两个界面之间无缝切换
4. ✅ **保持上下文**: 切换界面不会丢失聊天上下文
5. ✅ **自定义快捷键**: 在设置中配置个人偏好的快捷键

这样就解决了您提到的问题，现在可以在任何时候方便地访问设置界面了！
